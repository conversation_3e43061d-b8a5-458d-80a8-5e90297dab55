import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';
import {
  HistoryCard,
  HistoryFilterOptions,
  CustomerHistoryOverview,
  HistoryActivitySummary
} from '@/types/history.types';

/**
 * Enhanced History Card Repository
 *
 * This repository handles database operations for the History Card entity.
 * It provides methods for CRUD operations, specialized queries, and comprehensive
 * history tracking across all modules (repairs, maintenance, warranty, AMC, etc.).
 */
export class HistoryCardRepository extends PrismaRepository<
  Prisma.history_cardsGetPayload<{}>,
  string,
  Prisma.history_cardsCreateInput,
  Prisma.history_cardsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('history_cards');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find history cards by customer ID
   * @param customerId Customer ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of history cards
   */
  async findByCustomerId(customerId: string, skip?: number, take?: number): Promise<Prisma.history_cardsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { customerId },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: true,
        repairs: {
          orderBy: { repairDate: 'desc' }
        },
        maintenance: {
          orderBy: { maintenanceDate: 'desc' }
        },
        complaints: {
          orderBy: { complaintDate: 'desc' }
        },
        amcDetails: {
          orderBy: { startDate: 'desc' }
        },
        addonDetails: {
          orderBy: { addonDate: 'desc' }
        },
        waterWashes: {
          orderBy: { washDate: 'desc' }
        },
        componentReplacements: {
          orderBy: { replacementDate: 'desc' }
        },
      },
    });
  }

  /**
   * Find history cards by AMC contract ID
   * @param amcId AMC contract ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of history cards
   */
  async findByAmcId(amcId: string, skip?: number, take?: number): Promise<Prisma.history_cardsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { amcId },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: true,
        repairs: {
          orderBy: { repairDate: 'desc' }
        },
        maintenance: {
          orderBy: { maintenanceDate: 'desc' }
        },
        complaints: {
          orderBy: { complaintDate: 'desc' }
        },
        amcDetails: {
          orderBy: { startDate: 'desc' }
        },
        addonDetails: {
          orderBy: { addonDate: 'desc' }
        },
        waterWashes: {
          orderBy: { washDate: 'desc' }
        },
        componentReplacements: {
          orderBy: { replacementDate: 'desc' }
        },
      },
    });
  }

  /**
   * Find history cards by warranty ID
   * @param warrantyId Warranty ID (inWarrantyId)
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of history cards
   */
  async findByWarrantyId(warrantyId: string, skip?: number, take?: number): Promise<Prisma.history_cardsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { inWarrantyId: warrantyId },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: true,
        repairs: {
          orderBy: { repairDate: 'desc' }
        },
        maintenance: {
          orderBy: { maintenanceDate: 'desc' }
        },
        complaints: {
          orderBy: { complaintDate: 'desc' }
        },
        amcDetails: {
          orderBy: { startDate: 'desc' }
        },
        addonDetails: {
          orderBy: { addonDate: 'desc' }
        },
        waterWashes: {
          orderBy: { washDate: 'desc' }
        },
        componentReplacements: {
          orderBy: { replacementDate: 'desc' }
        },
      },
    });
  }

  /**
   * Find history cards by out warranty ID
   * @param outWarrantyId Out warranty ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of history cards
   */
  async findByOutWarrantyId(outWarrantyId: string, skip?: number, take?: number): Promise<Prisma.history_cardsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { outWarrantyId },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: true,
        repairs: {
          orderBy: { repairDate: 'desc' }
        },
        maintenance: {
          orderBy: { maintenanceDate: 'desc' }
        },
        complaints: {
          orderBy: { complaintDate: 'desc' }
        },
        amcDetails: {
          orderBy: { startDate: 'desc' }
        },
        addonDetails: {
          orderBy: { addonDate: 'desc' }
        },
        waterWashes: {
          orderBy: { washDate: 'desc' }
        },
        componentReplacements: {
          orderBy: { replacementDate: 'desc' }
        },
      },
    });
  }

  /**
   * Find history card with all related data
   * @param id History card ID
   * @returns Promise resolving to the history card with related data or null if not found
   */
  async findWithRelations(id: string): Promise<any | null> {
    return this.model.findUnique({
      where: { id },
      include: {
        customer: true,
        repairs: {
          orderBy: { repairDate: 'desc' }
        },
        maintenance: {
          orderBy: { maintenanceDate: 'desc' }
        },
        complaints: {
          orderBy: { complaintDate: 'desc' }
        },
        amcDetails: {
          orderBy: { startDate: 'desc' }
        },
        addonDetails: {
          orderBy: { addonDate: 'desc' }
        },
        waterWashes: {
          orderBy: { washDate: 'desc' }
        },
        audits: {
          orderBy: { auditDate: 'desc' }
        },
        componentReplacements: {
          orderBy: { replacementDate: 'desc' }
        },
      },
    });
  }

  /**
   * Get comprehensive customer history overview
   * @param customerId Customer ID
   * @returns Promise resolving to customer history overview
   */
  async getCustomerHistoryOverview(customerId: string): Promise<CustomerHistoryOverview | null> {
    const customer = await this.prisma.customer.findUnique({
      where: { id: customerId },
      select: {
        id: true,
        name: true,
        address: true,
        city: true,
        state: true,
        phone: true,
        email: true,
      }
    });

    if (!customer) {
      return null;
    }

    // Get aggregated counts
    const [
      totalHistoryCards,
      totalRepairs,
      totalMaintenance,
      totalComplaints,
      totalWarrantyClaims,
      activeAMCs,
      lastServiceDate,
      nextMaintenanceDate
    ] = await Promise.all([
      this.model.count({ where: { customerId } }),
      this.prisma.historyRepair.count({
        where: { historyCard: { customerId } }
      }),
      this.prisma.historyMaintenance.count({
        where: { historyCard: { customerId } }
      }),
      this.prisma.historyComplaint.count({
        where: { historyCard: { customerId } }
      }),
      0, // Warranty count - will implement when warranty model is available
      this.prisma.historyAmcDetail.count({
        where: {
          historyCard: { customerId }
        }
      }),
      this.prisma.historyRepair.findFirst({
        where: {
          historyCard: { customerId }
        },
        orderBy: { repairDate: 'desc' },
        select: { repairDate: true }
      }),
      this.prisma.historyMaintenance.findFirst({
        where: {
          historyCard: { customerId }
        },
        orderBy: { maintenanceDate: 'asc' },
        select: { maintenanceDate: true }
      })
    ]);

    // Get recent activity
    const recentActivity = await this.getRecentActivity(customerId, 10);

    return {
      customerId,
      customer: {
        ...customer,
        address: customer.address || undefined
      },
      totalHistoryCards,
      totalRepairs,
      totalMaintenance,
      totalComplaints,
      totalWarrantyClaims,
      activeAMCs,
      lastServiceDate: lastServiceDate?.repairDate || undefined,
      nextMaintenanceDate: nextMaintenanceDate?.maintenanceDate || undefined,
      recentActivity
    };
  }

  /**
   * Get recent activity for a customer
   * @param customerId Customer ID
   * @param limit Maximum number of activities to return
   * @returns Promise resolving to recent activity summary
   */
  async getRecentActivity(customerId: string, limit: number = 10): Promise<HistoryActivitySummary[]> {
    const activities: HistoryActivitySummary[] = [];

    // Get recent repairs
    const repairs = await this.prisma.historyRepair.findMany({
      where: { historyCard: { customerId } },
      orderBy: { repairDate: 'desc' },
      take: limit,
      select: {
        id: true,
        repairDate: true,
        description: true
      }
    });

    repairs.forEach(repair => {
      activities.push({
        id: repair.id,
        type: 'REPAIR',
        date: repair.repairDate || new Date(),
        description: repair.description,
        status: 'COMPLETED',
        priority: 'MEDIUM'
      });
    });

    // Get recent maintenance
    const maintenance = await this.prisma.historyMaintenance.findMany({
      where: { historyCard: { customerId } },
      orderBy: { maintenanceDate: 'desc' },
      take: limit,
      select: {
        id: true,
        maintenanceDate: true,
        description: true
      }
    });

    maintenance.forEach(maint => {
      activities.push({
        id: maint.id,
        type: 'MAINTENANCE',
        date: maint.maintenanceDate || new Date(),
        description: maint.description,
        status: 'SCHEDULED',
        priority: 'MAINTENANCE'
      });
    });

    // Get recent complaints
    const complaints = await this.prisma.historyComplaint.findMany({
      where: { historyCard: { customerId } },
      orderBy: { complaintDate: 'desc' },
      take: limit,
      select: {
        id: true,
        complaintDate: true,
        description: true
      }
    });

    complaints.forEach(complaint => {
      activities.push({
        id: complaint.id,
        type: 'COMPLAINT',
        date: complaint.complaintDate || new Date(),
        description: complaint.description,
        status: 'OPEN',
        priority: 'MEDIUM'
      });
    });

    // Sort by date and limit
    return activities
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, limit);
  }

  /**
   * Generic findFirst method for flexible queries
   * @param options Query options
   * @returns Promise resolving to first matching history card
   */
  async findFirst(options?: {
    where?: any;
    include?: any;
    orderBy?: any;
  }): Promise<any | null> {
    return this.model.findFirst({
      ...options,
      include: {
        customer: true,
        ...options?.include,
      },
    });
  }

  /**
   * Find history cards with filter
   * @param filter Filter options
   * @returns Promise resolving to filtered history cards
   */
  async findWithFilter(filter?: {
    customerId?: string;
    status?: string;
    dateFrom?: Date;
    dateTo?: Date;
    skip?: number;
    take?: number;
  }): Promise<any[]> {
    const where: any = {};

    if (filter?.customerId) {
      where.customerId = filter.customerId;
    }

    if (filter?.status) {
      where.status = filter.status;
    }

    if (filter?.dateFrom || filter?.dateTo) {
      where.createdAt = {};
      if (filter.dateFrom) {
        where.createdAt.gte = filter.dateFrom;
      }
      if (filter.dateTo) {
        where.createdAt.lte = filter.dateTo;
      }
    }

    return this.model.findMany({
      where,
      include: {
        customer: true,
      },
      orderBy: { createdAt: 'desc' },
      skip: filter?.skip,
      take: filter?.take,
    });
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.history_cardsGetPayload<{}>,
    string,
    Prisma.history_cardsCreateInput,
    Prisma.history_cardsUpdateInput
  > {
    const repo = new HistoryCardRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}
