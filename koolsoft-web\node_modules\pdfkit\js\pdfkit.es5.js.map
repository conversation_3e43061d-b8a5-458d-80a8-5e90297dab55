{"version": 3, "file": "pdfkit.es5.js", "sources": ["../lib/abstract_reference.js", "../lib/tree.js", "../lib/object.js", "../lib/reference.js", "../lib/page.js", "../lib/name_tree.js", "../lib/saslprep/lib/util.js", "../lib/saslprep/lib/code-points.js", "../lib/saslprep/index.js", "../lib/security.js", "../lib/gradient.js", "../lib/pattern.js", "../lib/mixins/color.js", "../lib/path.js", "../lib/mixins/vector.js", "../lib/font/afm.js", "../lib/font.js", "../lib/font/standard.js", "../lib/font/embedded.js", "../lib/font_factory.js", "../lib/mixins/fonts.js", "../lib/line_wrapper.js", "../lib/mixins/text.js", "../lib/image/jpeg.js", "../lib/image/png.js", "../lib/image.js", "../lib/mixins/images.js", "../lib/mixins/annotations.js", "../lib/outline.js", "../lib/mixins/outline.js", "../lib/structure_content.js", "../lib/structure_element.js", "../lib/number_tree.js", "../lib/mixins/markings.js", "../lib/mixins/acroform.js", "../lib/mixins/attachments.js", "../lib/mixins/pdfa.js", "../lib/mixins/pdfua.js", "../lib/mixins/subsets.js", "../lib/metadata.js", "../lib/mixins/metadata.js", "../lib/document.js"], "sourcesContent": ["/*\r\nPDFAbstractReference - abstract class for PDF reference\r\n*/\r\n\r\nclass PDFAbstractReference {\r\n  toString() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n}\r\n\r\nexport default PDFAbstractReference;\r\n", "/*\r\nPDFTree - abstract base class for name and number tree objects\r\n*/\r\n\r\nimport PDFObject from './object';\r\n\r\nclass PDFTree {\r\n  constructor(options = {}) {\r\n    this._items = {};\r\n    // disable /Limits output for this tree\r\n    this.limits =\r\n      typeof options.limits === 'boolean' ? options.limits : true;\r\n  }\r\n\r\n  add(key, val) {\r\n    return (this._items[key] = val);\r\n  }\r\n\r\n  get(key) {\r\n    return this._items[key];\r\n  }\r\n\r\n  toString() {\r\n    // Needs to be sorted by key\r\n    const sortedKeys = Object.keys(this._items).sort((a, b) =>\r\n      this._compareKeys(a, b)\r\n    );\r\n\r\n    const out = ['<<'];\r\n    if (this.limits && sortedKeys.length > 1) {\r\n      const first = sortedKeys[0],\r\n        last = sortedKeys[sortedKeys.length - 1];\r\n      out.push(\r\n        `  /Limits ${PDFObject.convert([this._dataForKey(first), this._dataForKey(last)])}`\r\n      );\r\n    }\r\n    out.push(`  /${this._keysName()} [`);\r\n    for (let key of sortedKeys) {\r\n      out.push(\r\n        `    ${PDFObject.convert(this._dataFor<PERSON>ey(key))} ${PDFObject.convert(\r\n          this._items[key]\r\n        )}`\r\n      );\r\n    }\r\n    out.push(']');\r\n    out.push('>>');\r\n    return out.join('\\n');\r\n  }\r\n\r\n  _compareKeys(/*a, b*/) {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  _keysName() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  _dataForKey(/*k*/) {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n}\r\n\r\nexport default PDFTree;\r\n", "/*\r\nPDFObject - converts JavaScript types into their corresponding PDF types.\r\nBy <PERSON>\r\n*/\r\n\r\nimport PDFAbstractReference from './abstract_reference';\r\nimport PDFTree from './tree';\r\n\r\nconst pad = (str, length) => (Array(length + 1).join('0') + str).slice(-length);\r\n\r\nconst escapableRe = /[\\n\\r\\t\\b\\f()\\\\]/g;\r\nconst escapable = {\r\n  '\\n': '\\\\n',\r\n  '\\r': '\\\\r',\r\n  '\\t': '\\\\t',\r\n  '\\b': '\\\\b',\r\n  '\\f': '\\\\f',\r\n  '\\\\': '\\\\\\\\',\r\n  '(': '\\\\(',\r\n  ')': '\\\\)'\r\n};\r\n\r\n// Convert little endian UTF-16 to big endian\r\nconst swapBytes = function(buff) {\r\n  const l = buff.length;\r\n  if (l & 0x01) {\r\n    throw new Error('Buffer length must be even');\r\n  } else {\r\n    for (let i = 0, end = l - 1; i < end; i += 2) {\r\n      const a = buff[i];\r\n      buff[i] = buff[i + 1];\r\n      buff[i + 1] = a;\r\n    }\r\n  }\r\n\r\n  return buff;\r\n};\r\n\r\nclass PDFObject {\r\n  static convert(object, encryptFn = null) {\r\n    // String literals are converted to the PDF name type\r\n    if (typeof object === 'string') {\r\n      return `/${object}`;\r\n\r\n      // String objects are converted to PDF strings (UTF-16)\r\n    } else if (object instanceof String) {\r\n      let string = object;\r\n      // Detect if this is a unicode string\r\n      let isUnicode = false;\r\n      for (let i = 0, end = string.length; i < end; i++) {\r\n        if (string.charCodeAt(i) > 0x7f) {\r\n          isUnicode = true;\r\n          break;\r\n        }\r\n      }\r\n\r\n      // If so, encode it as big endian UTF-16\r\n      let stringBuffer;\r\n      if (isUnicode) {\r\n        stringBuffer = swapBytes(Buffer.from(`\\ufeff${string}`, 'utf16le'));\r\n      } else {\r\n        stringBuffer = Buffer.from(string.valueOf(), 'ascii');\r\n      }\r\n\r\n      // Encrypt the string when necessary\r\n      if (encryptFn) {\r\n        string = encryptFn(stringBuffer).toString('binary');\r\n      } else {\r\n        string = stringBuffer.toString('binary');\r\n      }\r\n\r\n      // Escape characters as required by the spec\r\n      string = string.replace(escapableRe, c => escapable[c]);\r\n\r\n      return `(${string})`;\r\n\r\n      // Buffers are converted to PDF hex strings\r\n    } else if (Buffer.isBuffer(object)) {\r\n      return `<${object.toString('hex')}>`;\r\n    } else if (\r\n      object instanceof PDFAbstractReference ||\r\n      object instanceof PDFTree\r\n    ) {\r\n      return object.toString();\r\n    } else if (object instanceof Date) {\r\n      let string =\r\n        `D:${pad(object.getUTCFullYear(), 4)}` +\r\n        pad(object.getUTCMonth() + 1, 2) +\r\n        pad(object.getUTCDate(), 2) +\r\n        pad(object.getUTCHours(), 2) +\r\n        pad(object.getUTCMinutes(), 2) +\r\n        pad(object.getUTCSeconds(), 2) +\r\n        'Z';\r\n\r\n      // Encrypt the string when necessary\r\n      if (encryptFn) {\r\n        string = encryptFn(Buffer.from(string, 'ascii')).toString('binary');\r\n\r\n        // Escape characters as required by the spec\r\n        string = string.replace(escapableRe, c => escapable[c]);\r\n      }\r\n\r\n      return `(${string})`;\r\n    } else if (Array.isArray(object)) {\r\n      const items = object.map(e => PDFObject.convert(e, encryptFn)).join(' ');\r\n      return `[${items}]`;\r\n    } else if ({}.toString.call(object) === '[object Object]') {\r\n      const out = ['<<'];\r\n      for (let key in object) {\r\n        const val = object[key];\r\n        out.push(`/${key} ${PDFObject.convert(val, encryptFn)}`);\r\n      }\r\n\r\n      out.push('>>');\r\n      return out.join('\\n');\r\n    } else if (typeof object === 'number') {\r\n      return PDFObject.number(object);\r\n    } else {\r\n      return `${object}`;\r\n    }\r\n  }\r\n\r\n  static number(n) {\r\n    if (n > -1e21 && n < 1e21) {\r\n      return Math.round(n * 1e6) / 1e6;\r\n    }\r\n\r\n    throw new Error(`unsupported number: ${n}`);\r\n  }\r\n}\r\n\r\nexport default PDFObject;\r\n", "/*\r\nPDFReference - represents a reference to another object in the PDF object heirarchy\r\nBy <PERSON>\r\n*/\r\n\r\nimport zlib from 'zlib';\r\nimport PDFAbstractReference from './abstract_reference';\r\nimport PDFObject from './object';\r\n\r\nclass PDFReference extends PDFAbstractReference {\r\n  constructor(document, id, data = {}) {\r\n    super();\r\n    this.document = document;\r\n    this.id = id;\r\n    this.data = data;\r\n    this.gen = 0;\r\n    this.compress = this.document.compress && !this.data.Filter;\r\n    this.uncompressedLength = 0;\r\n    this.buffer = [];\r\n  }\r\n\r\n  write(chunk) {\r\n    if (!Buffer.isBuffer(chunk)) {\r\n      chunk = Buffer.from(chunk + '\\n', 'binary');\r\n    }\r\n\r\n    this.uncompressedLength += chunk.length;\r\n    if (this.data.Length == null) {\r\n      this.data.Length = 0;\r\n    }\r\n    this.buffer.push(chunk);\r\n    this.data.Length += chunk.length;\r\n    if (this.compress) {\r\n      return (this.data.Filter = 'FlateDecode');\r\n    }\r\n  }\r\n\r\n  end(chunk) {\r\n    if (chunk) {\r\n      this.write(chunk);\r\n    }\r\n    return this.finalize();\r\n  }\r\n\r\n  finalize() {\r\n    this.offset = this.document._offset;\r\n\r\n    const encryptFn = this.document._security\r\n      ? this.document._security.getEncryptFn(this.id, this.gen)\r\n      : null;\r\n\r\n    if (this.buffer.length) {\r\n      this.buffer = Buffer.concat(this.buffer);\r\n      if (this.compress) {\r\n        this.buffer = zlib.deflateSync(this.buffer);\r\n      }\r\n\r\n      if (encryptFn) {\r\n        this.buffer = encryptFn(this.buffer);\r\n      }\r\n\r\n      this.data.Length = this.buffer.length;\r\n    }\r\n\r\n    this.document._write(`${this.id} ${this.gen} obj`);\r\n    this.document._write(PDFObject.convert(this.data, encryptFn));\r\n\r\n    if (this.buffer.length) {\r\n      this.document._write('stream');\r\n      this.document._write(this.buffer);\r\n\r\n      this.buffer = []; // free up memory\r\n      this.document._write('\\nendstream');\r\n    }\r\n\r\n    this.document._write('endobj');\r\n    this.document._refEnd(this);\r\n  }\r\n  toString() {\r\n    return `${this.id} ${this.gen} R`;\r\n  }\r\n}\r\n\r\nexport default PDFReference;\r\n", "/*\r\nPDFPage - represents a single page in the PDF document\r\nBy <PERSON>\r\n*/\r\n\r\nconst DEFAULT_MARGINS = {\r\n  top: 72,\r\n  left: 72,\r\n  bottom: 72,\r\n  right: 72\r\n};\r\n\r\nconst SIZES = {\r\n  '4A0': [4767.87, 6740.79],\r\n  '2A0': [3370.39, 4767.87],\r\n  A0: [2383.94, 3370.39],\r\n  A1: [1683.78, 2383.94],\r\n  A2: [1190.55, 1683.78],\r\n  A3: [841.89, 1190.55],\r\n  A4: [595.28, 841.89],\r\n  A5: [419.53, 595.28],\r\n  A6: [297.64, 419.53],\r\n  A7: [209.76, 297.64],\r\n  A8: [147.4, 209.76],\r\n  A9: [104.88, 147.4],\r\n  A10: [73.7, 104.88],\r\n  B0: [2834.65, 4008.19],\r\n  B1: [2004.09, 2834.65],\r\n  B2: [1417.32, 2004.09],\r\n  B3: [1000.63, 1417.32],\r\n  B4: [708.66, 1000.63],\r\n  B5: [498.9, 708.66],\r\n  B6: [354.33, 498.9],\r\n  B7: [249.45, 354.33],\r\n  B8: [175.75, 249.45],\r\n  B9: [124.72, 175.75],\r\n  B10: [87.87, 124.72],\r\n  C0: [2599.37, 3676.54],\r\n  C1: [1836.85, 2599.37],\r\n  C2: [1298.27, 1836.85],\r\n  C3: [918.43, 1298.27],\r\n  C4: [649.13, 918.43],\r\n  C5: [459.21, 649.13],\r\n  C6: [323.15, 459.21],\r\n  C7: [229.61, 323.15],\r\n  C8: [161.57, 229.61],\r\n  C9: [113.39, 161.57],\r\n  C10: [79.37, 113.39],\r\n  RA0: [2437.8, 3458.27],\r\n  RA1: [1729.13, 2437.8],\r\n  RA2: [1218.9, 1729.13],\r\n  RA3: [864.57, 1218.9],\r\n  RA4: [609.45, 864.57],\r\n  SRA0: [2551.18, 3628.35],\r\n  SRA1: [1814.17, 2551.18],\r\n  SRA2: [1275.59, 1814.17],\r\n  SRA3: [907.09, 1275.59],\r\n  SRA4: [637.8, 907.09],\r\n  EXECUTIVE: [521.86, 756.0],\r\n  FOLIO: [612.0, 936.0],\r\n  LEGAL: [612.0, 1008.0],\r\n  LETTER: [612.0, 792.0],\r\n  TABLOID: [792.0, 1224.0]\r\n};\r\n\r\nclass PDFPage {\r\n  constructor(document, options = {}) {\r\n    this.document = document;\r\n    this.size = options.size || 'letter';\r\n    this.layout = options.layout || 'portrait';\r\n\r\n    // process margins\r\n    if (typeof options.margin === 'number') {\r\n      this.margins = {\r\n        top: options.margin,\r\n        left: options.margin,\r\n        bottom: options.margin,\r\n        right: options.margin\r\n      };\r\n\r\n      // default to 1 inch margins\r\n    } else {\r\n      this.margins = options.margins || DEFAULT_MARGINS;\r\n    }\r\n\r\n    // calculate page dimensions\r\n    const dimensions = Array.isArray(this.size)\r\n      ? this.size\r\n      : SIZES[this.size.toUpperCase()];\r\n    this.width = dimensions[this.layout === 'portrait' ? 0 : 1];\r\n    this.height = dimensions[this.layout === 'portrait' ? 1 : 0];\r\n\r\n    this.content = this.document.ref();\r\n\r\n    // Initialize the Font, XObject, and ExtGState dictionaries\r\n    this.resources = this.document.ref({\r\n      ProcSet: ['PDF', 'Text', 'ImageB', 'ImageC', 'ImageI']\r\n    });\r\n\r\n    // The page dictionary\r\n    this.dictionary = this.document.ref({\r\n      Type: 'Page',\r\n      Parent: this.document._root.data.Pages,\r\n      MediaBox: [0, 0, this.width, this.height],\r\n      Contents: this.content,\r\n      Resources: this.resources\r\n    });\r\n\r\n    this.markings = [];\r\n  }\r\n\r\n  // Lazily create these objects\r\n  get fonts() {\r\n    const data = this.resources.data;\r\n    return data.Font != null ? data.Font : (data.Font = {});\r\n  }\r\n\r\n  get xobjects() {\r\n    const data = this.resources.data;\r\n    return data.XObject != null ? data.XObject : (data.XObject = {});\r\n  }\r\n\r\n  get ext_gstates() {\r\n    const data = this.resources.data;\r\n    return data.ExtGState != null ? data.ExtGState : (data.ExtGState = {});\r\n  }\r\n\r\n  get patterns() {\r\n    const data = this.resources.data;\r\n    return data.Pattern != null ? data.Pattern : (data.Pattern = {});\r\n  }\r\n\r\n  get colorSpaces() {\r\n    const data = this.resources.data;\r\n    return data.ColorSpace || (data.ColorSpace = {});\r\n  }\r\n\r\n  get annotations() {\r\n    const data = this.dictionary.data;\r\n    return data.Annots != null ? data.Annots : (data.Annots = []);\r\n  }\r\n\r\n  get structParentTreeKey() {\r\n    const data = this.dictionary.data;\r\n    return data.StructParents != null\r\n      ? data.StructParents\r\n      : (data.StructParents = this.document.createStructParentTreeNextKey());\r\n  }\r\n\r\n  maxY() {\r\n    return this.height - this.margins.bottom;\r\n  }\r\n\r\n  write(chunk) {\r\n    return this.content.write(chunk);\r\n  }\r\n\r\n  end() {\r\n    this.dictionary.end();\r\n    this.resources.end();\r\n    return this.content.end();\r\n  }\r\n}\r\n\r\nexport default PDFPage;\r\n", "/*\r\nPDFNameTree - represents a name tree object\r\n*/\r\n\r\nimport PDFTree from \"./tree\";\r\n\r\nclass PDFNameTree extends PDFTree {\r\n  _compareKeys(a, b) {\r\n    return a.localeCompare(b);\r\n  }\r\n\r\n  _keysName() {\r\n    return \"Names\";\r\n  }\r\n\r\n  _dataForKey(k) {\r\n    return new String(k);\r\n  }\r\n}\r\n\r\nexport default PDFNameTree;\r\n", "/**\r\n * Check if value is in a range group.\r\n * @param {number} value\r\n * @param {number[]} rangeGroup\r\n * @returns {boolean}\r\n */\r\nfunction inRange(value, rangeGroup) {\r\n  if (value < rangeGroup[0]) return false;\r\n  let startRange = 0;\r\n  let endRange = rangeGroup.length / 2;\r\n  while (startRange <= endRange) {\r\n    const middleRange = Math.floor((startRange + endRange) / 2);\r\n\r\n    // actual array index\r\n    const arrayIndex = middleRange * 2;\r\n\r\n    // Check if value is in range pointed by actual index\r\n    if (\r\n      value >= rangeGroup[arrayIndex] &&\r\n      value <= rangeGroup[arrayIndex + 1]\r\n    ) {\r\n      return true;\r\n    }\r\n\r\n    if (value > rangeGroup[arrayIndex + 1]) {\r\n      // Search Right Side Of Array\r\n      startRange = middleRange + 1;\r\n    } else {\r\n      // Search Left Side Of Array\r\n      endRange = middleRange - 1;\r\n    }\r\n  }\r\n  return false;\r\n}\r\n\r\nexport { inRange };\r\n", "import { inRange } from './util';\r\n\r\n// prettier-ignore-start\r\n/**\r\n * A.1 Unassigned code points in Unicode 3.2\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-A.1\r\n */\r\nconst unassigned_code_points = [\r\n  0x0221,\r\n  0x0221,\r\n  0x0234,\r\n  0x024f,\r\n  0x02ae,\r\n  0x02af,\r\n  0x02ef,\r\n  0x02ff,\r\n  0x0350,\r\n  0x035f,\r\n  0x0370,\r\n  0x0373,\r\n  0x0376,\r\n  0x0379,\r\n  0x037b,\r\n  0x037d,\r\n  0x037f,\r\n  0x0383,\r\n  0x038b,\r\n  0x038b,\r\n  0x038d,\r\n  0x038d,\r\n  0x03a2,\r\n  0x03a2,\r\n  0x03cf,\r\n  0x03cf,\r\n  0x03f7,\r\n  0x03ff,\r\n  0x0487,\r\n  0x0487,\r\n  0x04cf,\r\n  0x04cf,\r\n  0x04f6,\r\n  0x04f7,\r\n  0x04fa,\r\n  0x04ff,\r\n  0x0510,\r\n  0x0530,\r\n  0x0557,\r\n  0x0558,\r\n  0x0560,\r\n  0x0560,\r\n  0x0588,\r\n  0x0588,\r\n  0x058b,\r\n  0x0590,\r\n  0x05a2,\r\n  0x05a2,\r\n  0x05ba,\r\n  0x05ba,\r\n  0x05c5,\r\n  0x05cf,\r\n  0x05eb,\r\n  0x05ef,\r\n  0x05f5,\r\n  0x060b,\r\n  0x060d,\r\n  0x061a,\r\n  0x061c,\r\n  0x061e,\r\n  0x0620,\r\n  0x0620,\r\n  0x063b,\r\n  0x063f,\r\n  0x0656,\r\n  0x065f,\r\n  0x06ee,\r\n  0x06ef,\r\n  0x06ff,\r\n  0x06ff,\r\n  0x070e,\r\n  0x070e,\r\n  0x072d,\r\n  0x072f,\r\n  0x074b,\r\n  0x077f,\r\n  0x07b2,\r\n  0x0900,\r\n  0x0904,\r\n  0x0904,\r\n  0x093a,\r\n  0x093b,\r\n  0x094e,\r\n  0x094f,\r\n  0x0955,\r\n  0x0957,\r\n  0x0971,\r\n  0x0980,\r\n  0x0984,\r\n  0x0984,\r\n  0x098d,\r\n  0x098e,\r\n  0x0991,\r\n  0x0992,\r\n  0x09a9,\r\n  0x09a9,\r\n  0x09b1,\r\n  0x09b1,\r\n  0x09b3,\r\n  0x09b5,\r\n  0x09ba,\r\n  0x09bb,\r\n  0x09bd,\r\n  0x09bd,\r\n  0x09c5,\r\n  0x09c6,\r\n  0x09c9,\r\n  0x09ca,\r\n  0x09ce,\r\n  0x09d6,\r\n  0x09d8,\r\n  0x09db,\r\n  0x09de,\r\n  0x09de,\r\n  0x09e4,\r\n  0x09e5,\r\n  0x09fb,\r\n  0x0a01,\r\n  0x0a03,\r\n  0x0a04,\r\n  0x0a0b,\r\n  0x0a0e,\r\n  0x0a11,\r\n  0x0a12,\r\n  0x0a29,\r\n  0x0a29,\r\n  0x0a31,\r\n  0x0a31,\r\n  0x0a34,\r\n  0x0a34,\r\n  0x0a37,\r\n  0x0a37,\r\n  0x0a3a,\r\n  0x0a3b,\r\n  0x0a3d,\r\n  0x0a3d,\r\n  0x0a43,\r\n  0x0a46,\r\n  0x0a49,\r\n  0x0a4a,\r\n  0x0a4e,\r\n  0x0a58,\r\n  0x0a5d,\r\n  0x0a5d,\r\n  0x0a5f,\r\n  0x0a65,\r\n  0x0a75,\r\n  0x0a80,\r\n  0x0a84,\r\n  0x0a84,\r\n  0x0a8c,\r\n  0x0a8c,\r\n  0x0a8e,\r\n  0x0a8e,\r\n  0x0a92,\r\n  0x0a92,\r\n  0x0aa9,\r\n  0x0aa9,\r\n  0x0ab1,\r\n  0x0ab1,\r\n  0x0ab4,\r\n  0x0ab4,\r\n  0x0aba,\r\n  0x0abb,\r\n  0x0ac6,\r\n  0x0ac6,\r\n  0x0aca,\r\n  0x0aca,\r\n  0x0ace,\r\n  0x0acf,\r\n  0x0ad1,\r\n  0x0adf,\r\n  0x0ae1,\r\n  0x0ae5,\r\n  0x0af0,\r\n  0x0b00,\r\n  0x0b04,\r\n  0x0b04,\r\n  0x0b0d,\r\n  0x0b0e,\r\n  0x0b11,\r\n  0x0b12,\r\n  0x0b29,\r\n  0x0b29,\r\n  0x0b31,\r\n  0x0b31,\r\n  0x0b34,\r\n  0x0b35,\r\n  0x0b3a,\r\n  0x0b3b,\r\n  0x0b44,\r\n  0x0b46,\r\n  0x0b49,\r\n  0x0b4a,\r\n  0x0b4e,\r\n  0x0b55,\r\n  0x0b58,\r\n  0x0b5b,\r\n  0x0b5e,\r\n  0x0b5e,\r\n  0x0b62,\r\n  0x0b65,\r\n  0x0b71,\r\n  0x0b81,\r\n  0x0b84,\r\n  0x0b84,\r\n  0x0b8b,\r\n  0x0b8d,\r\n  0x0b91,\r\n  0x0b91,\r\n  0x0b96,\r\n  0x0b98,\r\n  0x0b9b,\r\n  0x0b9b,\r\n  0x0b9d,\r\n  0x0b9d,\r\n  0x0ba0,\r\n  0x0ba2,\r\n  0x0ba5,\r\n  0x0ba7,\r\n  0x0bab,\r\n  0x0bad,\r\n  0x0bb6,\r\n  0x0bb6,\r\n  0x0bba,\r\n  0x0bbd,\r\n  0x0bc3,\r\n  0x0bc5,\r\n  0x0bc9,\r\n  0x0bc9,\r\n  0x0bce,\r\n  0x0bd6,\r\n  0x0bd8,\r\n  0x0be6,\r\n  0x0bf3,\r\n  0x0c00,\r\n  0x0c04,\r\n  0x0c04,\r\n  0x0c0d,\r\n  0x0c0d,\r\n  0x0c11,\r\n  0x0c11,\r\n  0x0c29,\r\n  0x0c29,\r\n  0x0c34,\r\n  0x0c34,\r\n  0x0c3a,\r\n  0x0c3d,\r\n  0x0c45,\r\n  0x0c45,\r\n  0x0c49,\r\n  0x0c49,\r\n  0x0c4e,\r\n  0x0c54,\r\n  0x0c57,\r\n  0x0c5f,\r\n  0x0c62,\r\n  0x0c65,\r\n  0x0c70,\r\n  0x0c81,\r\n  0x0c84,\r\n  0x0c84,\r\n  0x0c8d,\r\n  0x0c8d,\r\n  0x0c91,\r\n  0x0c91,\r\n  0x0ca9,\r\n  0x0ca9,\r\n  0x0cb4,\r\n  0x0cb4,\r\n  0x0cba,\r\n  0x0cbd,\r\n  0x0cc5,\r\n  0x0cc5,\r\n  0x0cc9,\r\n  0x0cc9,\r\n  0x0cce,\r\n  0x0cd4,\r\n  0x0cd7,\r\n  0x0cdd,\r\n  0x0cdf,\r\n  0x0cdf,\r\n  0x0ce2,\r\n  0x0ce5,\r\n  0x0cf0,\r\n  0x0d01,\r\n  0x0d04,\r\n  0x0d04,\r\n  0x0d0d,\r\n  0x0d0d,\r\n  0x0d11,\r\n  0x0d11,\r\n  0x0d29,\r\n  0x0d29,\r\n  0x0d3a,\r\n  0x0d3d,\r\n  0x0d44,\r\n  0x0d45,\r\n  0x0d49,\r\n  0x0d49,\r\n  0x0d4e,\r\n  0x0d56,\r\n  0x0d58,\r\n  0x0d5f,\r\n  0x0d62,\r\n  0x0d65,\r\n  0x0d70,\r\n  0x0d81,\r\n  0x0d84,\r\n  0x0d84,\r\n  0x0d97,\r\n  0x0d99,\r\n  0x0db2,\r\n  0x0db2,\r\n  0x0dbc,\r\n  0x0dbc,\r\n  0x0dbe,\r\n  0x0dbf,\r\n  0x0dc7,\r\n  0x0dc9,\r\n  0x0dcb,\r\n  0x0dce,\r\n  0x0dd5,\r\n  0x0dd5,\r\n  0x0dd7,\r\n  0x0dd7,\r\n  0x0de0,\r\n  0x0df1,\r\n  0x0df5,\r\n  0x0e00,\r\n  0x0e3b,\r\n  0x0e3e,\r\n  0x0e5c,\r\n  0x0e80,\r\n  0x0e83,\r\n  0x0e83,\r\n  0x0e85,\r\n  0x0e86,\r\n  0x0e89,\r\n  0x0e89,\r\n  0x0e8b,\r\n  0x0e8c,\r\n  0x0e8e,\r\n  0x0e93,\r\n  0x0e98,\r\n  0x0e98,\r\n  0x0ea0,\r\n  0x0ea0,\r\n  0x0ea4,\r\n  0x0ea4,\r\n  0x0ea6,\r\n  0x0ea6,\r\n  0x0ea8,\r\n  0x0ea9,\r\n  0x0eac,\r\n  0x0eac,\r\n  0x0eba,\r\n  0x0eba,\r\n  0x0ebe,\r\n  0x0ebf,\r\n  0x0ec5,\r\n  0x0ec5,\r\n  0x0ec7,\r\n  0x0ec7,\r\n  0x0ece,\r\n  0x0ecf,\r\n  0x0eda,\r\n  0x0edb,\r\n  0x0ede,\r\n  0x0eff,\r\n  0x0f48,\r\n  0x0f48,\r\n  0x0f6b,\r\n  0x0f70,\r\n  0x0f8c,\r\n  0x0f8f,\r\n  0x0f98,\r\n  0x0f98,\r\n  0x0fbd,\r\n  0x0fbd,\r\n  0x0fcd,\r\n  0x0fce,\r\n  0x0fd0,\r\n  0x0fff,\r\n  0x1022,\r\n  0x1022,\r\n  0x1028,\r\n  0x1028,\r\n  0x102b,\r\n  0x102b,\r\n  0x1033,\r\n  0x1035,\r\n  0x103a,\r\n  0x103f,\r\n  0x105a,\r\n  0x109f,\r\n  0x10c6,\r\n  0x10cf,\r\n  0x10f9,\r\n  0x10fa,\r\n  0x10fc,\r\n  0x10ff,\r\n  0x115a,\r\n  0x115e,\r\n  0x11a3,\r\n  0x11a7,\r\n  0x11fa,\r\n  0x11ff,\r\n  0x1207,\r\n  0x1207,\r\n  0x1247,\r\n  0x1247,\r\n  0x1249,\r\n  0x1249,\r\n  0x124e,\r\n  0x124f,\r\n  0x1257,\r\n  0x1257,\r\n  0x1259,\r\n  0x1259,\r\n  0x125e,\r\n  0x125f,\r\n  0x1287,\r\n  0x1287,\r\n  0x1289,\r\n  0x1289,\r\n  0x128e,\r\n  0x128f,\r\n  0x12af,\r\n  0x12af,\r\n  0x12b1,\r\n  0x12b1,\r\n  0x12b6,\r\n  0x12b7,\r\n  0x12bf,\r\n  0x12bf,\r\n  0x12c1,\r\n  0x12c1,\r\n  0x12c6,\r\n  0x12c7,\r\n  0x12cf,\r\n  0x12cf,\r\n  0x12d7,\r\n  0x12d7,\r\n  0x12ef,\r\n  0x12ef,\r\n  0x130f,\r\n  0x130f,\r\n  0x1311,\r\n  0x1311,\r\n  0x1316,\r\n  0x1317,\r\n  0x131f,\r\n  0x131f,\r\n  0x1347,\r\n  0x1347,\r\n  0x135b,\r\n  0x1360,\r\n  0x137d,\r\n  0x139f,\r\n  0x13f5,\r\n  0x1400,\r\n  0x1677,\r\n  0x167f,\r\n  0x169d,\r\n  0x169f,\r\n  0x16f1,\r\n  0x16ff,\r\n  0x170d,\r\n  0x170d,\r\n  0x1715,\r\n  0x171f,\r\n  0x1737,\r\n  0x173f,\r\n  0x1754,\r\n  0x175f,\r\n  0x176d,\r\n  0x176d,\r\n  0x1771,\r\n  0x1771,\r\n  0x1774,\r\n  0x177f,\r\n  0x17dd,\r\n  0x17df,\r\n  0x17ea,\r\n  0x17ff,\r\n  0x180f,\r\n  0x180f,\r\n  0x181a,\r\n  0x181f,\r\n  0x1878,\r\n  0x187f,\r\n  0x18aa,\r\n  0x1dff,\r\n  0x1e9c,\r\n  0x1e9f,\r\n  0x1efa,\r\n  0x1eff,\r\n  0x1f16,\r\n  0x1f17,\r\n  0x1f1e,\r\n  0x1f1f,\r\n  0x1f46,\r\n  0x1f47,\r\n  0x1f4e,\r\n  0x1f4f,\r\n  0x1f58,\r\n  0x1f58,\r\n  0x1f5a,\r\n  0x1f5a,\r\n  0x1f5c,\r\n  0x1f5c,\r\n  0x1f5e,\r\n  0x1f5e,\r\n  0x1f7e,\r\n  0x1f7f,\r\n  0x1fb5,\r\n  0x1fb5,\r\n  0x1fc5,\r\n  0x1fc5,\r\n  0x1fd4,\r\n  0x1fd5,\r\n  0x1fdc,\r\n  0x1fdc,\r\n  0x1ff0,\r\n  0x1ff1,\r\n  0x1ff5,\r\n  0x1ff5,\r\n  0x1fff,\r\n  0x1fff,\r\n  0x2053,\r\n  0x2056,\r\n  0x2058,\r\n  0x205e,\r\n  0x2064,\r\n  0x2069,\r\n  0x2072,\r\n  0x2073,\r\n  0x208f,\r\n  0x209f,\r\n  0x20b2,\r\n  0x20cf,\r\n  0x20eb,\r\n  0x20ff,\r\n  0x213b,\r\n  0x213c,\r\n  0x214c,\r\n  0x2152,\r\n  0x2184,\r\n  0x218f,\r\n  0x23cf,\r\n  0x23ff,\r\n  0x2427,\r\n  0x243f,\r\n  0x244b,\r\n  0x245f,\r\n  0x24ff,\r\n  0x24ff,\r\n  0x2614,\r\n  0x2615,\r\n  0x2618,\r\n  0x2618,\r\n  0x267e,\r\n  0x267f,\r\n  0x268a,\r\n  0x2700,\r\n  0x2705,\r\n  0x2705,\r\n  0x270a,\r\n  0x270b,\r\n  0x2728,\r\n  0x2728,\r\n  0x274c,\r\n  0x274c,\r\n  0x274e,\r\n  0x274e,\r\n  0x2753,\r\n  0x2755,\r\n  0x2757,\r\n  0x2757,\r\n  0x275f,\r\n  0x2760,\r\n  0x2795,\r\n  0x2797,\r\n  0x27b0,\r\n  0x27b0,\r\n  0x27bf,\r\n  0x27cf,\r\n  0x27ec,\r\n  0x27ef,\r\n  0x2b00,\r\n  0x2e7f,\r\n  0x2e9a,\r\n  0x2e9a,\r\n  0x2ef4,\r\n  0x2eff,\r\n  0x2fd6,\r\n  0x2fef,\r\n  0x2ffc,\r\n  0x2fff,\r\n  0x3040,\r\n  0x3040,\r\n  0x3097,\r\n  0x3098,\r\n  0x3100,\r\n  0x3104,\r\n  0x312d,\r\n  0x3130,\r\n  0x318f,\r\n  0x318f,\r\n  0x31b8,\r\n  0x31ef,\r\n  0x321d,\r\n  0x321f,\r\n  0x3244,\r\n  0x3250,\r\n  0x327c,\r\n  0x327e,\r\n  0x32cc,\r\n  0x32cf,\r\n  0x32ff,\r\n  0x32ff,\r\n  0x3377,\r\n  0x337a,\r\n  0x33de,\r\n  0x33df,\r\n  0x33ff,\r\n  0x33ff,\r\n  0x4db6,\r\n  0x4dff,\r\n  0x9fa6,\r\n  0x9fff,\r\n  0xa48d,\r\n  0xa48f,\r\n  0xa4c7,\r\n  0xabff,\r\n  0xd7a4,\r\n  0xd7ff,\r\n  0xfa2e,\r\n  0xfa2f,\r\n  0xfa6b,\r\n  0xfaff,\r\n  0xfb07,\r\n  0xfb12,\r\n  0xfb18,\r\n  0xfb1c,\r\n  0xfb37,\r\n  0xfb37,\r\n  0xfb3d,\r\n  0xfb3d,\r\n  0xfb3f,\r\n  0xfb3f,\r\n  0xfb42,\r\n  0xfb42,\r\n  0xfb45,\r\n  0xfb45,\r\n  0xfbb2,\r\n  0xfbd2,\r\n  0xfd40,\r\n  0xfd4f,\r\n  0xfd90,\r\n  0xfd91,\r\n  0xfdc8,\r\n  0xfdcf,\r\n  0xfdfd,\r\n  0xfdff,\r\n  0xfe10,\r\n  0xfe1f,\r\n  0xfe24,\r\n  0xfe2f,\r\n  0xfe47,\r\n  0xfe48,\r\n  0xfe53,\r\n  0xfe53,\r\n  0xfe67,\r\n  0xfe67,\r\n  0xfe6c,\r\n  0xfe6f,\r\n  0xfe75,\r\n  0xfe75,\r\n  0xfefd,\r\n  0xfefe,\r\n  0xff00,\r\n  0xff00,\r\n  0xffbf,\r\n  0xffc1,\r\n  0xffc8,\r\n  0xffc9,\r\n  0xffd0,\r\n  0xffd1,\r\n  0xffd8,\r\n  0xffd9,\r\n  0xffdd,\r\n  0xffdf,\r\n  0xffe7,\r\n  0xffe7,\r\n  0xffef,\r\n  0xfff8,\r\n  0x10000,\r\n  0x102ff,\r\n  0x1031f,\r\n  0x1031f,\r\n  0x10324,\r\n  0x1032f,\r\n  0x1034b,\r\n  0x103ff,\r\n  0x10426,\r\n  0x10427,\r\n  0x1044e,\r\n  0x1cfff,\r\n  0x1d0f6,\r\n  0x1d0ff,\r\n  0x1d127,\r\n  0x1d129,\r\n  0x1d1de,\r\n  0x1d3ff,\r\n  0x1d455,\r\n  0x1d455,\r\n  0x1d49d,\r\n  0x1d49d,\r\n  0x1d4a0,\r\n  0x1d4a1,\r\n  0x1d4a3,\r\n  0x1d4a4,\r\n  0x1d4a7,\r\n  0x1d4a8,\r\n  0x1d4ad,\r\n  0x1d4ad,\r\n  0x1d4ba,\r\n  0x1d4ba,\r\n  0x1d4bc,\r\n  0x1d4bc,\r\n  0x1d4c1,\r\n  0x1d4c1,\r\n  0x1d4c4,\r\n  0x1d4c4,\r\n  0x1d506,\r\n  0x1d506,\r\n  0x1d50b,\r\n  0x1d50c,\r\n  0x1d515,\r\n  0x1d515,\r\n  0x1d51d,\r\n  0x1d51d,\r\n  0x1d53a,\r\n  0x1d53a,\r\n  0x1d53f,\r\n  0x1d53f,\r\n  0x1d545,\r\n  0x1d545,\r\n  0x1d547,\r\n  0x1d549,\r\n  0x1d551,\r\n  0x1d551,\r\n  0x1d6a4,\r\n  0x1d6a7,\r\n  0x1d7ca,\r\n  0x1d7cd,\r\n  0x1d800,\r\n  0x1fffd,\r\n  0x2a6d7,\r\n  0x2f7ff,\r\n  0x2fa1e,\r\n  0x2fffd,\r\n  0x30000,\r\n  0x3fffd,\r\n  0x40000,\r\n  0x4fffd,\r\n  0x50000,\r\n  0x5fffd,\r\n  0x60000,\r\n  0x6fffd,\r\n  0x70000,\r\n  0x7fffd,\r\n  0x80000,\r\n  0x8fffd,\r\n  0x90000,\r\n  0x9fffd,\r\n  0xa0000,\r\n  0xafffd,\r\n  0xb0000,\r\n  0xbfffd,\r\n  0xc0000,\r\n  0xcfffd,\r\n  0xd0000,\r\n  0xdfffd,\r\n  0xe0000,\r\n  0xe0000,\r\n  0xe0002,\r\n  0xe001f,\r\n  0xe0080,\r\n  0xefffd\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isUnassignedCodePoint = character =>\r\n  inRange(character, unassigned_code_points);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * B.1 Commonly mapped to nothing\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-B.1\r\n */\r\nconst commonly_mapped_to_nothing = [\r\n  0x00ad,\r\n  0x00ad,\r\n  0x034f,\r\n  0x034f,\r\n  0x1806,\r\n  0x1806,\r\n  0x180b,\r\n  0x180b,\r\n  0x180c,\r\n  0x180c,\r\n  0x180d,\r\n  0x180d,\r\n  0x200b,\r\n  0x200b,\r\n  0x200c,\r\n  0x200c,\r\n  0x200d,\r\n  0x200d,\r\n  0x2060,\r\n  0x2060,\r\n  0xfe00,\r\n  0xfe00,\r\n  0xfe01,\r\n  0xfe01,\r\n  0xfe02,\r\n  0xfe02,\r\n  0xfe03,\r\n  0xfe03,\r\n  0xfe04,\r\n  0xfe04,\r\n  0xfe05,\r\n  0xfe05,\r\n  0xfe06,\r\n  0xfe06,\r\n  0xfe07,\r\n  0xfe07,\r\n  0xfe08,\r\n  0xfe08,\r\n  0xfe09,\r\n  0xfe09,\r\n  0xfe0a,\r\n  0xfe0a,\r\n  0xfe0b,\r\n  0xfe0b,\r\n  0xfe0c,\r\n  0xfe0c,\r\n  0xfe0d,\r\n  0xfe0d,\r\n  0xfe0e,\r\n  0xfe0e,\r\n  0xfe0f,\r\n  0xfe0f,\r\n  0xfeff,\r\n  0xfeff\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isCommonlyMappedToNothing = character =>\r\n  inRange(character, commonly_mapped_to_nothing);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * C.1.2 Non-ASCII space characters\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-C.1.2\r\n */\r\nconst non_ASCII_space_characters = [\r\n  0x00a0,\r\n  0x00a0 /* NO-BREAK SPACE */,\r\n  0x1680,\r\n  0x1680 /* OGHAM SPACE MARK */,\r\n  0x2000,\r\n  0x2000 /* EN QUAD */,\r\n  0x2001,\r\n  0x2001 /* EM QUAD */,\r\n  0x2002,\r\n  0x2002 /* EN SPACE */,\r\n  0x2003,\r\n  0x2003 /* EM SPACE */,\r\n  0x2004,\r\n  0x2004 /* THREE-PER-EM SPACE */,\r\n  0x2005,\r\n  0x2005 /* FOUR-PER-EM SPACE */,\r\n  0x2006,\r\n  0x2006 /* SIX-PER-EM SPACE */,\r\n  0x2007,\r\n  0x2007 /* FIGURE SPACE */,\r\n  0x2008,\r\n  0x2008 /* PUNCTUATION SPACE */,\r\n  0x2009,\r\n  0x2009 /* THIN SPACE */,\r\n  0x200a,\r\n  0x200a /* HAIR SPACE */,\r\n  0x200b,\r\n  0x200b /* ZERO WIDTH SPACE */,\r\n  0x202f,\r\n  0x202f /* NARROW NO-BREAK SPACE */,\r\n  0x205f,\r\n  0x205f /* MEDIUM MATHEMATICAL SPACE */,\r\n  0x3000,\r\n  0x3000 /* IDEOGRAPHIC SPACE */\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isNonASCIISpaceCharacter = character =>\r\n  inRange(character, non_ASCII_space_characters);\r\n\r\n// prettier-ignore-start\r\nconst non_ASCII_controls_characters = [\r\n  /**\r\n   * C.2.2 Non-ASCII control characters\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.2.2\r\n   */\r\n  0x0080,\r\n  0x009f /* [CONTROL CHARACTERS] */,\r\n  0x06dd,\r\n  0x06dd /* ARABIC END OF AYAH */,\r\n  0x070f,\r\n  0x070f /* SYRIAC ABBREVIATION MARK */,\r\n  0x180e,\r\n  0x180e /* MONGOLIAN VOWEL SEPARATOR */,\r\n  0x200c,\r\n  0x200c /* ZERO WIDTH NON-JOINER */,\r\n  0x200d,\r\n  0x200d /* ZERO WIDTH JOINER */,\r\n  0x2028,\r\n  0x2028 /* LINE SEPARATOR */,\r\n  0x2029,\r\n  0x2029 /* PARAGRAPH SEPARATOR */,\r\n  0x2060,\r\n  0x2060 /* WORD JOINER */,\r\n  0x2061,\r\n  0x2061 /* FUNCTION APPLICATION */,\r\n  0x2062,\r\n  0x2062 /* INVISIBLE TIMES */,\r\n  0x2063,\r\n  0x2063 /* INVISIBLE SEPARATOR */,\r\n  0x206a,\r\n  0x206f /* [CONTROL CHARACTERS] */,\r\n  0xfeff,\r\n  0xfeff /* ZERO WIDTH NO-BREAK SPACE */,\r\n  0xfff9,\r\n  0xfffc /* [CONTROL CHARACTERS] */,\r\n  0x1d173,\r\n  0x1d17a /* [MUSICAL CONTROL CHARACTERS] */\r\n];\r\n\r\nconst non_character_codepoints = [\r\n  /**\r\n   * C.4 Non-character code points\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.4\r\n   */\r\n  0xfdd0,\r\n  0xfdef /* [NONCHARACTER CODE POINTS] */,\r\n  0xfffe,\r\n  0xffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x1fffe,\r\n  0x1ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x2fffe,\r\n  0x2ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x3fffe,\r\n  0x3ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x4fffe,\r\n  0x4ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x5fffe,\r\n  0x5ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x6fffe,\r\n  0x6ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x7fffe,\r\n  0x7ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x8fffe,\r\n  0x8ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x9fffe,\r\n  0x9ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xafffe,\r\n  0xaffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xbfffe,\r\n  0xbffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xcfffe,\r\n  0xcffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xdfffe,\r\n  0xdffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xefffe,\r\n  0xeffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x10fffe,\r\n  0x10ffff /* [NONCHARACTER CODE POINTS] */\r\n];\r\n\r\n/**\r\n * 2.3.  Prohibited Output\r\n */\r\nconst prohibited_characters = [\r\n  /**\r\n   * C.2.1 ASCII control characters\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.2.1\r\n   */\r\n  0,\r\n  0x001f /* [CONTROL CHARACTERS] */,\r\n  0x007f,\r\n  0x007f /* DELETE */,\r\n\r\n  /**\r\n   * C.8 Change display properties or are deprecated\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.8\r\n   */\r\n  0x0340,\r\n  0x0340 /* COMBINING GRAVE TONE MARK */,\r\n  0x0341,\r\n  0x0341 /* COMBINING ACUTE TONE MARK */,\r\n  0x200e,\r\n  0x200e /* LEFT-TO-RIGHT MARK */,\r\n  0x200f,\r\n  0x200f /* RIGHT-TO-LEFT MARK */,\r\n  0x202a,\r\n  0x202a /* LEFT-TO-RIGHT EMBEDDING */,\r\n  0x202b,\r\n  0x202b /* RIGHT-TO-LEFT EMBEDDING */,\r\n  0x202c,\r\n  0x202c /* POP DIRECTIONAL FORMATTING */,\r\n  0x202d,\r\n  0x202d /* LEFT-TO-RIGHT OVERRIDE */,\r\n  0x202e,\r\n  0x202e /* RIGHT-TO-LEFT OVERRIDE */,\r\n  0x206a,\r\n  0x206a /* INHIBIT SYMMETRIC SWAPPING */,\r\n  0x206b,\r\n  0x206b /* ACTIVATE SYMMETRIC SWAPPING */,\r\n  0x206c,\r\n  0x206c /* INHIBIT ARABIC FORM SHAPING */,\r\n  0x206d,\r\n  0x206d /* ACTIVATE ARABIC FORM SHAPING */,\r\n  0x206e,\r\n  0x206e /* NATIONAL DIGIT SHAPES */,\r\n  0x206f,\r\n  0x206f /* NOMINAL DIGIT SHAPES */,\r\n\r\n  /**\r\n   * C.7 Inappropriate for canonical representation\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.7\r\n   */\r\n  0x2ff0,\r\n  0x2ffb /* [IDEOGRAPHIC DESCRIPTION CHARACTERS] */,\r\n\r\n  /**\r\n   * C.5 Surrogate codes\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.5\r\n   */\r\n  0xd800,\r\n  0xdfff,\r\n\r\n  /**\r\n   * C.3 Private use\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.3\r\n   */\r\n  0xe000,\r\n  0xf8ff /* [PRIVATE USE, PLANE 0] */,\r\n\r\n  /**\r\n   * C.6 Inappropriate for plain text\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.6\r\n   */\r\n  0xfff9,\r\n  0xfff9 /* INTERLINEAR ANNOTATION ANCHOR */,\r\n  0xfffa,\r\n  0xfffa /* INTERLINEAR ANNOTATION SEPARATOR */,\r\n  0xfffb,\r\n  0xfffb /* INTERLINEAR ANNOTATION TERMINATOR */,\r\n  0xfffc,\r\n  0xfffc /* OBJECT REPLACEMENT CHARACTER */,\r\n  0xfffd,\r\n  0xfffd /* REPLACEMENT CHARACTER */,\r\n\r\n  /**\r\n   * C.9 Tagging characters\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.9\r\n   */\r\n  0xe0001,\r\n  0xe0001 /* LANGUAGE TAG */,\r\n  0xe0020,\r\n  0xe007f /* [TAGGING CHARACTERS] */,\r\n\r\n  /**\r\n   * C.3 Private use\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.3\r\n   */\r\n\r\n  0xf0000,\r\n  0xffffd /* [PRIVATE USE, PLANE 15] */,\r\n  0x100000,\r\n  0x10fffd /* [PRIVATE USE, PLANE 16] */\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isProhibitedCharacter = character =>\r\n  inRange(character, non_ASCII_space_characters) ||\r\n  inRange(character, prohibited_characters) ||\r\n  inRange(character, non_ASCII_controls_characters) ||\r\n  inRange(character, non_character_codepoints);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * D.1 Characters with bidirectional property \"R\" or \"AL\"\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-D.1\r\n */\r\nconst bidirectional_r_al = [\r\n  0x05be,\r\n  0x05be,\r\n  0x05c0,\r\n  0x05c0,\r\n  0x05c3,\r\n  0x05c3,\r\n  0x05d0,\r\n  0x05ea,\r\n  0x05f0,\r\n  0x05f4,\r\n  0x061b,\r\n  0x061b,\r\n  0x061f,\r\n  0x061f,\r\n  0x0621,\r\n  0x063a,\r\n  0x0640,\r\n  0x064a,\r\n  0x066d,\r\n  0x066f,\r\n  0x0671,\r\n  0x06d5,\r\n  0x06dd,\r\n  0x06dd,\r\n  0x06e5,\r\n  0x06e6,\r\n  0x06fa,\r\n  0x06fe,\r\n  0x0700,\r\n  0x070d,\r\n  0x0710,\r\n  0x0710,\r\n  0x0712,\r\n  0x072c,\r\n  0x0780,\r\n  0x07a5,\r\n  0x07b1,\r\n  0x07b1,\r\n  0x200f,\r\n  0x200f,\r\n  0xfb1d,\r\n  0xfb1d,\r\n  0xfb1f,\r\n  0xfb28,\r\n  0xfb2a,\r\n  0xfb36,\r\n  0xfb38,\r\n  0xfb3c,\r\n  0xfb3e,\r\n  0xfb3e,\r\n  0xfb40,\r\n  0xfb41,\r\n  0xfb43,\r\n  0xfb44,\r\n  0xfb46,\r\n  0xfbb1,\r\n  0xfbd3,\r\n  0xfd3d,\r\n  0xfd50,\r\n  0xfd8f,\r\n  0xfd92,\r\n  0xfdc7,\r\n  0xfdf0,\r\n  0xfdfc,\r\n  0xfe70,\r\n  0xfe74,\r\n  0xfe76,\r\n  0xfefc\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isBidirectionalRAL = character => inRange(character, bidirectional_r_al);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * D.2 Characters with bidirectional property \"L\"\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-D.2\r\n */\r\nconst bidirectional_l = [\r\n  0x0041,\r\n  0x005a,\r\n  0x0061,\r\n  0x007a,\r\n  0x00aa,\r\n  0x00aa,\r\n  0x00b5,\r\n  0x00b5,\r\n  0x00ba,\r\n  0x00ba,\r\n  0x00c0,\r\n  0x00d6,\r\n  0x00d8,\r\n  0x00f6,\r\n  0x00f8,\r\n  0x0220,\r\n  0x0222,\r\n  0x0233,\r\n  0x0250,\r\n  0x02ad,\r\n  0x02b0,\r\n  0x02b8,\r\n  0x02bb,\r\n  0x02c1,\r\n  0x02d0,\r\n  0x02d1,\r\n  0x02e0,\r\n  0x02e4,\r\n  0x02ee,\r\n  0x02ee,\r\n  0x037a,\r\n  0x037a,\r\n  0x0386,\r\n  0x0386,\r\n  0x0388,\r\n  0x038a,\r\n  0x038c,\r\n  0x038c,\r\n  0x038e,\r\n  0x03a1,\r\n  0x03a3,\r\n  0x03ce,\r\n  0x03d0,\r\n  0x03f5,\r\n  0x0400,\r\n  0x0482,\r\n  0x048a,\r\n  0x04ce,\r\n  0x04d0,\r\n  0x04f5,\r\n  0x04f8,\r\n  0x04f9,\r\n  0x0500,\r\n  0x050f,\r\n  0x0531,\r\n  0x0556,\r\n  0x0559,\r\n  0x055f,\r\n  0x0561,\r\n  0x0587,\r\n  0x0589,\r\n  0x0589,\r\n  0x0903,\r\n  0x0903,\r\n  0x0905,\r\n  0x0939,\r\n  0x093d,\r\n  0x0940,\r\n  0x0949,\r\n  0x094c,\r\n  0x0950,\r\n  0x0950,\r\n  0x0958,\r\n  0x0961,\r\n  0x0964,\r\n  0x0970,\r\n  0x0982,\r\n  0x0983,\r\n  0x0985,\r\n  0x098c,\r\n  0x098f,\r\n  0x0990,\r\n  0x0993,\r\n  0x09a8,\r\n  0x09aa,\r\n  0x09b0,\r\n  0x09b2,\r\n  0x09b2,\r\n  0x09b6,\r\n  0x09b9,\r\n  0x09be,\r\n  0x09c0,\r\n  0x09c7,\r\n  0x09c8,\r\n  0x09cb,\r\n  0x09cc,\r\n  0x09d7,\r\n  0x09d7,\r\n  0x09dc,\r\n  0x09dd,\r\n  0x09df,\r\n  0x09e1,\r\n  0x09e6,\r\n  0x09f1,\r\n  0x09f4,\r\n  0x09fa,\r\n  0x0a05,\r\n  0x0a0a,\r\n  0x0a0f,\r\n  0x0a10,\r\n  0x0a13,\r\n  0x0a28,\r\n  0x0a2a,\r\n  0x0a30,\r\n  0x0a32,\r\n  0x0a33,\r\n  0x0a35,\r\n  0x0a36,\r\n  0x0a38,\r\n  0x0a39,\r\n  0x0a3e,\r\n  0x0a40,\r\n  0x0a59,\r\n  0x0a5c,\r\n  0x0a5e,\r\n  0x0a5e,\r\n  0x0a66,\r\n  0x0a6f,\r\n  0x0a72,\r\n  0x0a74,\r\n  0x0a83,\r\n  0x0a83,\r\n  0x0a85,\r\n  0x0a8b,\r\n  0x0a8d,\r\n  0x0a8d,\r\n  0x0a8f,\r\n  0x0a91,\r\n  0x0a93,\r\n  0x0aa8,\r\n  0x0aaa,\r\n  0x0ab0,\r\n  0x0ab2,\r\n  0x0ab3,\r\n  0x0ab5,\r\n  0x0ab9,\r\n  0x0abd,\r\n  0x0ac0,\r\n  0x0ac9,\r\n  0x0ac9,\r\n  0x0acb,\r\n  0x0acc,\r\n  0x0ad0,\r\n  0x0ad0,\r\n  0x0ae0,\r\n  0x0ae0,\r\n  0x0ae6,\r\n  0x0aef,\r\n  0x0b02,\r\n  0x0b03,\r\n  0x0b05,\r\n  0x0b0c,\r\n  0x0b0f,\r\n  0x0b10,\r\n  0x0b13,\r\n  0x0b28,\r\n  0x0b2a,\r\n  0x0b30,\r\n  0x0b32,\r\n  0x0b33,\r\n  0x0b36,\r\n  0x0b39,\r\n  0x0b3d,\r\n  0x0b3e,\r\n  0x0b40,\r\n  0x0b40,\r\n  0x0b47,\r\n  0x0b48,\r\n  0x0b4b,\r\n  0x0b4c,\r\n  0x0b57,\r\n  0x0b57,\r\n  0x0b5c,\r\n  0x0b5d,\r\n  0x0b5f,\r\n  0x0b61,\r\n  0x0b66,\r\n  0x0b70,\r\n  0x0b83,\r\n  0x0b83,\r\n  0x0b85,\r\n  0x0b8a,\r\n  0x0b8e,\r\n  0x0b90,\r\n  0x0b92,\r\n  0x0b95,\r\n  0x0b99,\r\n  0x0b9a,\r\n  0x0b9c,\r\n  0x0b9c,\r\n  0x0b9e,\r\n  0x0b9f,\r\n  0x0ba3,\r\n  0x0ba4,\r\n  0x0ba8,\r\n  0x0baa,\r\n  0x0bae,\r\n  0x0bb5,\r\n  0x0bb7,\r\n  0x0bb9,\r\n  0x0bbe,\r\n  0x0bbf,\r\n  0x0bc1,\r\n  0x0bc2,\r\n  0x0bc6,\r\n  0x0bc8,\r\n  0x0bca,\r\n  0x0bcc,\r\n  0x0bd7,\r\n  0x0bd7,\r\n  0x0be7,\r\n  0x0bf2,\r\n  0x0c01,\r\n  0x0c03,\r\n  0x0c05,\r\n  0x0c0c,\r\n  0x0c0e,\r\n  0x0c10,\r\n  0x0c12,\r\n  0x0c28,\r\n  0x0c2a,\r\n  0x0c33,\r\n  0x0c35,\r\n  0x0c39,\r\n  0x0c41,\r\n  0x0c44,\r\n  0x0c60,\r\n  0x0c61,\r\n  0x0c66,\r\n  0x0c6f,\r\n  0x0c82,\r\n  0x0c83,\r\n  0x0c85,\r\n  0x0c8c,\r\n  0x0c8e,\r\n  0x0c90,\r\n  0x0c92,\r\n  0x0ca8,\r\n  0x0caa,\r\n  0x0cb3,\r\n  0x0cb5,\r\n  0x0cb9,\r\n  0x0cbe,\r\n  0x0cbe,\r\n  0x0cc0,\r\n  0x0cc4,\r\n  0x0cc7,\r\n  0x0cc8,\r\n  0x0cca,\r\n  0x0ccb,\r\n  0x0cd5,\r\n  0x0cd6,\r\n  0x0cde,\r\n  0x0cde,\r\n  0x0ce0,\r\n  0x0ce1,\r\n  0x0ce6,\r\n  0x0cef,\r\n  0x0d02,\r\n  0x0d03,\r\n  0x0d05,\r\n  0x0d0c,\r\n  0x0d0e,\r\n  0x0d10,\r\n  0x0d12,\r\n  0x0d28,\r\n  0x0d2a,\r\n  0x0d39,\r\n  0x0d3e,\r\n  0x0d40,\r\n  0x0d46,\r\n  0x0d48,\r\n  0x0d4a,\r\n  0x0d4c,\r\n  0x0d57,\r\n  0x0d57,\r\n  0x0d60,\r\n  0x0d61,\r\n  0x0d66,\r\n  0x0d6f,\r\n  0x0d82,\r\n  0x0d83,\r\n  0x0d85,\r\n  0x0d96,\r\n  0x0d9a,\r\n  0x0db1,\r\n  0x0db3,\r\n  0x0dbb,\r\n  0x0dbd,\r\n  0x0dbd,\r\n  0x0dc0,\r\n  0x0dc6,\r\n  0x0dcf,\r\n  0x0dd1,\r\n  0x0dd8,\r\n  0x0ddf,\r\n  0x0df2,\r\n  0x0df4,\r\n  0x0e01,\r\n  0x0e30,\r\n  0x0e32,\r\n  0x0e33,\r\n  0x0e40,\r\n  0x0e46,\r\n  0x0e4f,\r\n  0x0e5b,\r\n  0x0e81,\r\n  0x0e82,\r\n  0x0e84,\r\n  0x0e84,\r\n  0x0e87,\r\n  0x0e88,\r\n  0x0e8a,\r\n  0x0e8a,\r\n  0x0e8d,\r\n  0x0e8d,\r\n  0x0e94,\r\n  0x0e97,\r\n  0x0e99,\r\n  0x0e9f,\r\n  0x0ea1,\r\n  0x0ea3,\r\n  0x0ea5,\r\n  0x0ea5,\r\n  0x0ea7,\r\n  0x0ea7,\r\n  0x0eaa,\r\n  0x0eab,\r\n  0x0ead,\r\n  0x0eb0,\r\n  0x0eb2,\r\n  0x0eb3,\r\n  0x0ebd,\r\n  0x0ebd,\r\n  0x0ec0,\r\n  0x0ec4,\r\n  0x0ec6,\r\n  0x0ec6,\r\n  0x0ed0,\r\n  0x0ed9,\r\n  0x0edc,\r\n  0x0edd,\r\n  0x0f00,\r\n  0x0f17,\r\n  0x0f1a,\r\n  0x0f34,\r\n  0x0f36,\r\n  0x0f36,\r\n  0x0f38,\r\n  0x0f38,\r\n  0x0f3e,\r\n  0x0f47,\r\n  0x0f49,\r\n  0x0f6a,\r\n  0x0f7f,\r\n  0x0f7f,\r\n  0x0f85,\r\n  0x0f85,\r\n  0x0f88,\r\n  0x0f8b,\r\n  0x0fbe,\r\n  0x0fc5,\r\n  0x0fc7,\r\n  0x0fcc,\r\n  0x0fcf,\r\n  0x0fcf,\r\n  0x1000,\r\n  0x1021,\r\n  0x1023,\r\n  0x1027,\r\n  0x1029,\r\n  0x102a,\r\n  0x102c,\r\n  0x102c,\r\n  0x1031,\r\n  0x1031,\r\n  0x1038,\r\n  0x1038,\r\n  0x1040,\r\n  0x1057,\r\n  0x10a0,\r\n  0x10c5,\r\n  0x10d0,\r\n  0x10f8,\r\n  0x10fb,\r\n  0x10fb,\r\n  0x1100,\r\n  0x1159,\r\n  0x115f,\r\n  0x11a2,\r\n  0x11a8,\r\n  0x11f9,\r\n  0x1200,\r\n  0x1206,\r\n  0x1208,\r\n  0x1246,\r\n  0x1248,\r\n  0x1248,\r\n  0x124a,\r\n  0x124d,\r\n  0x1250,\r\n  0x1256,\r\n  0x1258,\r\n  0x1258,\r\n  0x125a,\r\n  0x125d,\r\n  0x1260,\r\n  0x1286,\r\n  0x1288,\r\n  0x1288,\r\n  0x128a,\r\n  0x128d,\r\n  0x1290,\r\n  0x12ae,\r\n  0x12b0,\r\n  0x12b0,\r\n  0x12b2,\r\n  0x12b5,\r\n  0x12b8,\r\n  0x12be,\r\n  0x12c0,\r\n  0x12c0,\r\n  0x12c2,\r\n  0x12c5,\r\n  0x12c8,\r\n  0x12ce,\r\n  0x12d0,\r\n  0x12d6,\r\n  0x12d8,\r\n  0x12ee,\r\n  0x12f0,\r\n  0x130e,\r\n  0x1310,\r\n  0x1310,\r\n  0x1312,\r\n  0x1315,\r\n  0x1318,\r\n  0x131e,\r\n  0x1320,\r\n  0x1346,\r\n  0x1348,\r\n  0x135a,\r\n  0x1361,\r\n  0x137c,\r\n  0x13a0,\r\n  0x13f4,\r\n  0x1401,\r\n  0x1676,\r\n  0x1681,\r\n  0x169a,\r\n  0x16a0,\r\n  0x16f0,\r\n  0x1700,\r\n  0x170c,\r\n  0x170e,\r\n  0x1711,\r\n  0x1720,\r\n  0x1731,\r\n  0x1735,\r\n  0x1736,\r\n  0x1740,\r\n  0x1751,\r\n  0x1760,\r\n  0x176c,\r\n  0x176e,\r\n  0x1770,\r\n  0x1780,\r\n  0x17b6,\r\n  0x17be,\r\n  0x17c5,\r\n  0x17c7,\r\n  0x17c8,\r\n  0x17d4,\r\n  0x17da,\r\n  0x17dc,\r\n  0x17dc,\r\n  0x17e0,\r\n  0x17e9,\r\n  0x1810,\r\n  0x1819,\r\n  0x1820,\r\n  0x1877,\r\n  0x1880,\r\n  0x18a8,\r\n  0x1e00,\r\n  0x1e9b,\r\n  0x1ea0,\r\n  0x1ef9,\r\n  0x1f00,\r\n  0x1f15,\r\n  0x1f18,\r\n  0x1f1d,\r\n  0x1f20,\r\n  0x1f45,\r\n  0x1f48,\r\n  0x1f4d,\r\n  0x1f50,\r\n  0x1f57,\r\n  0x1f59,\r\n  0x1f59,\r\n  0x1f5b,\r\n  0x1f5b,\r\n  0x1f5d,\r\n  0x1f5d,\r\n  0x1f5f,\r\n  0x1f7d,\r\n  0x1f80,\r\n  0x1fb4,\r\n  0x1fb6,\r\n  0x1fbc,\r\n  0x1fbe,\r\n  0x1fbe,\r\n  0x1fc2,\r\n  0x1fc4,\r\n  0x1fc6,\r\n  0x1fcc,\r\n  0x1fd0,\r\n  0x1fd3,\r\n  0x1fd6,\r\n  0x1fdb,\r\n  0x1fe0,\r\n  0x1fec,\r\n  0x1ff2,\r\n  0x1ff4,\r\n  0x1ff6,\r\n  0x1ffc,\r\n  0x200e,\r\n  0x200e,\r\n  0x2071,\r\n  0x2071,\r\n  0x207f,\r\n  0x207f,\r\n  0x2102,\r\n  0x2102,\r\n  0x2107,\r\n  0x2107,\r\n  0x210a,\r\n  0x2113,\r\n  0x2115,\r\n  0x2115,\r\n  0x2119,\r\n  0x211d,\r\n  0x2124,\r\n  0x2124,\r\n  0x2126,\r\n  0x2126,\r\n  0x2128,\r\n  0x2128,\r\n  0x212a,\r\n  0x212d,\r\n  0x212f,\r\n  0x2131,\r\n  0x2133,\r\n  0x2139,\r\n  0x213d,\r\n  0x213f,\r\n  0x2145,\r\n  0x2149,\r\n  0x2160,\r\n  0x2183,\r\n  0x2336,\r\n  0x237a,\r\n  0x2395,\r\n  0x2395,\r\n  0x249c,\r\n  0x24e9,\r\n  0x3005,\r\n  0x3007,\r\n  0x3021,\r\n  0x3029,\r\n  0x3031,\r\n  0x3035,\r\n  0x3038,\r\n  0x303c,\r\n  0x3041,\r\n  0x3096,\r\n  0x309d,\r\n  0x309f,\r\n  0x30a1,\r\n  0x30fa,\r\n  0x30fc,\r\n  0x30ff,\r\n  0x3105,\r\n  0x312c,\r\n  0x3131,\r\n  0x318e,\r\n  0x3190,\r\n  0x31b7,\r\n  0x31f0,\r\n  0x321c,\r\n  0x3220,\r\n  0x3243,\r\n  0x3260,\r\n  0x327b,\r\n  0x327f,\r\n  0x32b0,\r\n  0x32c0,\r\n  0x32cb,\r\n  0x32d0,\r\n  0x32fe,\r\n  0x3300,\r\n  0x3376,\r\n  0x337b,\r\n  0x33dd,\r\n  0x33e0,\r\n  0x33fe,\r\n  0x3400,\r\n  0x4db5,\r\n  0x4e00,\r\n  0x9fa5,\r\n  0xa000,\r\n  0xa48c,\r\n  0xac00,\r\n  0xd7a3,\r\n  0xd800,\r\n  0xfa2d,\r\n  0xfa30,\r\n  0xfa6a,\r\n  0xfb00,\r\n  0xfb06,\r\n  0xfb13,\r\n  0xfb17,\r\n  0xff21,\r\n  0xff3a,\r\n  0xff41,\r\n  0xff5a,\r\n  0xff66,\r\n  0xffbe,\r\n  0xffc2,\r\n  0xffc7,\r\n  0xffca,\r\n  0xffcf,\r\n  0xffd2,\r\n  0xffd7,\r\n  0xffda,\r\n  0xffdc,\r\n  0x10300,\r\n  0x1031e,\r\n  0x10320,\r\n  0x10323,\r\n  0x10330,\r\n  0x1034a,\r\n  0x10400,\r\n  0x10425,\r\n  0x10428,\r\n  0x1044d,\r\n  0x1d000,\r\n  0x1d0f5,\r\n  0x1d100,\r\n  0x1d126,\r\n  0x1d12a,\r\n  0x1d166,\r\n  0x1d16a,\r\n  0x1d172,\r\n  0x1d183,\r\n  0x1d184,\r\n  0x1d18c,\r\n  0x1d1a9,\r\n  0x1d1ae,\r\n  0x1d1dd,\r\n  0x1d400,\r\n  0x1d454,\r\n  0x1d456,\r\n  0x1d49c,\r\n  0x1d49e,\r\n  0x1d49f,\r\n  0x1d4a2,\r\n  0x1d4a2,\r\n  0x1d4a5,\r\n  0x1d4a6,\r\n  0x1d4a9,\r\n  0x1d4ac,\r\n  0x1d4ae,\r\n  0x1d4b9,\r\n  0x1d4bb,\r\n  0x1d4bb,\r\n  0x1d4bd,\r\n  0x1d4c0,\r\n  0x1d4c2,\r\n  0x1d4c3,\r\n  0x1d4c5,\r\n  0x1d505,\r\n  0x1d507,\r\n  0x1d50a,\r\n  0x1d50d,\r\n  0x1d514,\r\n  0x1d516,\r\n  0x1d51c,\r\n  0x1d51e,\r\n  0x1d539,\r\n  0x1d53b,\r\n  0x1d53e,\r\n  0x1d540,\r\n  0x1d544,\r\n  0x1d546,\r\n  0x1d546,\r\n  0x1d54a,\r\n  0x1d550,\r\n  0x1d552,\r\n  0x1d6a3,\r\n  0x1d6a8,\r\n  0x1d7c9,\r\n  0x20000,\r\n  0x2a6d6,\r\n  0x2f800,\r\n  0x2fa1d,\r\n  0xf0000,\r\n  0xffffd,\r\n  0x100000,\r\n  0x10fffd\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isBidirectionalL = character => inRange(character, bidirectional_l);\r\n\r\nexport {\r\n  isUnassignedCodePoint,\r\n  isCommonlyMappedToNothing,\r\n  isNonASCIISpaceCharacter,\r\n  isProhibitedCharacter,\r\n  isBidirectionalRAL,\r\n  isBidirectionalL\r\n};\r\n", "import {\r\n  isUnassignedCodePoint,\r\n  isC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  isNonASCI<PERSON><PERSON><PERSON>haracter,\r\n  isProhibited<PERSON>haracter,\r\n  isBidirectionalRAL,\r\n  isBidirectionalL\r\n} from './lib/code-points';\r\n\r\n// 2.1.  Mapping\r\n\r\n/**\r\n * non-ASCII space characters [StringPrep, C.1.2] that can be\r\n * mapped to SPACE (U+0020)\r\n */\r\nconst mapping2space = isNonASCIISpaceCharacter;\r\n\r\n/**\r\n * the \"commonly mapped to nothing\" characters [StringPrep, B.1]\r\n * that can be mapped to nothing.\r\n */\r\nconst mapping2nothing = isCommonlyMappedToNothing;\r\n\r\n// utils\r\nconst getCodePoint = character => character.codePointAt(0);\r\nconst first = x => x[0];\r\nconst last = x => x[x.length - 1];\r\n\r\n/**\r\n * Convert provided string into an array of Unicode Code Points.\r\n * Based on https://stackoverflow.com/a/21409165/1556249\r\n * and https://www.npmjs.com/package/code-point-at.\r\n * @param {string} input\r\n * @returns {number[]}\r\n */\r\nfunction toCodePoints(input) {\r\n  const codepoints = [];\r\n  const size = input.length;\r\n\r\n  for (let i = 0; i < size; i += 1) {\r\n    const before = input.charCodeAt(i);\r\n\r\n    if (before >= 0xd800 && before <= 0xdbff && size > i + 1) {\r\n      const next = input.charCodeAt(i + 1);\r\n\r\n      if (next >= 0xdc00 && next <= 0xdfff) {\r\n        codepoints.push((before - 0xd800) * 0x400 + next - 0xdc00 + 0x10000);\r\n        i += 1;\r\n        continue;\r\n      }\r\n    }\r\n\r\n    codepoints.push(before);\r\n  }\r\n\r\n  return codepoints;\r\n}\r\n\r\n/**\r\n * SASLprep.\r\n * @param {string} input\r\n * @param {Object} opts\r\n * @param {boolean} opts.allowUnassigned\r\n * @returns {string}\r\n */\r\nfunction saslprep(input, opts = {}) {\r\n  if (typeof input !== 'string') {\r\n    throw new TypeError('Expected string.');\r\n  }\r\n\r\n  if (input.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  // 1. Map\r\n  const mapped_input = toCodePoints(input)\r\n    // 1.1 mapping to space\r\n    .map(character => (mapping2space(character) ? 0x20 : character))\r\n    // 1.2 mapping to nothing\r\n    .filter(character => !mapping2nothing(character));\r\n\r\n  // 2. Normalize\r\n  const normalized_input = String.fromCodePoint\r\n    .apply(null, mapped_input)\r\n    .normalize('NFKC');\r\n\r\n  const normalized_map = toCodePoints(normalized_input);\r\n\r\n  // 3. Prohibit\r\n  const hasProhibited = normalized_map.some(isProhibitedCharacter);\r\n\r\n  if (hasProhibited) {\r\n    throw new Error(\r\n      'Prohibited character, see https://tools.ietf.org/html/rfc4013#section-2.3'\r\n    );\r\n  }\r\n\r\n  // Unassigned Code Points\r\n  if (opts.allowUnassigned !== true) {\r\n    const hasUnassigned = normalized_map.some(isUnassignedCodePoint);\r\n\r\n    if (hasUnassigned) {\r\n      throw new Error(\r\n        'Unassigned code point, see https://tools.ietf.org/html/rfc4013#section-2.5'\r\n      );\r\n    }\r\n  }\r\n\r\n  // 4. check bidi\r\n\r\n  const hasBidiRAL = normalized_map.some(isBidirectionalRAL);\r\n\r\n  const hasBidiL = normalized_map.some(isBidirectionalL);\r\n\r\n  // 4.1 If a string contains any RandALCat character, the string MUST NOT\r\n  // contain any LCat character.\r\n  if (hasBidiRAL && hasBidiL) {\r\n    throw new Error(\r\n      'String must not contain RandALCat and LCat at the same time,' +\r\n        ' see https://tools.ietf.org/html/rfc3454#section-6'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * 4.2 If a string contains any RandALCat character, a RandALCat\r\n   * character MUST be the first character of the string, and a\r\n   * RandALCat character MUST be the last character of the string.\r\n   */\r\n\r\n  const isFirstBidiRAL = isBidirectionalRAL(\r\n    getCodePoint(first(normalized_input))\r\n  );\r\n  const isLastBidiRAL = isBidirectionalRAL(\r\n    getCodePoint(last(normalized_input))\r\n  );\r\n\r\n  if (hasBidiRAL && !(isFirstBidiRAL && isLastBidiRAL)) {\r\n    throw new Error(\r\n      'Bidirectional RandALCat character must be the first and the last' +\r\n        ' character of the string, see https://tools.ietf.org/html/rfc3454#section-6'\r\n    );\r\n  }\r\n\r\n  return normalized_input;\r\n}\r\n\r\nexport default saslprep;\r\n", "/*\r\n   PDFSecurity - represents PDF security settings\r\n   By <PERSON> <<EMAIL>>\r\n */\r\n\r\nimport CryptoJS from 'crypto-js';\r\nimport saslprep from './saslprep/index';\r\n\r\nclass PDFSecurity {\r\n  static generateFileID(info = {}) {\r\n    let infoStr = `${info.CreationDate.getTime()}\\n`;\r\n\r\n    for (let key in info) {\r\n      // eslint-disable-next-line no-prototype-builtins\r\n      if (!info.hasOwnProperty(key)) {\r\n        continue;\r\n      }\r\n      infoStr += `${key}: ${info[key].valueOf()}\\n`;\r\n    }\r\n\r\n    return wordArrayToBuffer(CryptoJS.MD5(infoStr));\r\n  }\r\n\r\n  static generateRandomWordArray(bytes) {\r\n    return CryptoJS.lib.WordArray.random(bytes);\r\n  }\r\n\r\n  static create(document, options = {}) {\r\n    if (!options.ownerPassword && !options.userPassword) {\r\n      return null;\r\n    }\r\n    return new PDFSecurity(document, options);\r\n  }\r\n\r\n  constructor(document, options = {}) {\r\n    if (!options.ownerPassword && !options.userPassword) {\r\n      throw new Error('None of owner password and user password is defined.');\r\n    }\r\n\r\n    this.document = document;\r\n    this._setupEncryption(options);\r\n  }\r\n\r\n  _setupEncryption(options) {\r\n    switch (options.pdfVersion) {\r\n      case '1.4':\r\n      case '1.5':\r\n        this.version = 2;\r\n        break;\r\n      case '1.6':\r\n      case '1.7':\r\n        this.version = 4;\r\n        break;\r\n      case '1.7ext3':\r\n        this.version = 5;\r\n        break;\r\n      default:\r\n        this.version = 1;\r\n        break;\r\n    }\r\n\r\n    const encDict = {\r\n      Filter: 'Standard'\r\n    };\r\n\r\n    switch (this.version) {\r\n      case 1:\r\n      case 2:\r\n      case 4:\r\n        this._setupEncryptionV1V2V4(this.version, encDict, options);\r\n        break;\r\n      case 5:\r\n        this._setupEncryptionV5(encDict, options);\r\n        break;\r\n    }\r\n\r\n    this.dictionary = this.document.ref(encDict);\r\n  }\r\n\r\n  _setupEncryptionV1V2V4(v, encDict, options) {\r\n    let r, permissions;\r\n    switch (v) {\r\n      case 1:\r\n        r = 2;\r\n        this.keyBits = 40;\r\n        permissions = getPermissionsR2(options.permissions);\r\n        break;\r\n      case 2:\r\n        r = 3;\r\n        this.keyBits = 128;\r\n        permissions = getPermissionsR3(options.permissions);\r\n        break;\r\n      case 4:\r\n        r = 4;\r\n        this.keyBits = 128;\r\n        permissions = getPermissionsR3(options.permissions);\r\n        break;\r\n    }\r\n\r\n    const paddedUserPassword = processPasswordR2R3R4(options.userPassword);\r\n    const paddedOwnerPassword = options.ownerPassword\r\n      ? processPasswordR2R3R4(options.ownerPassword)\r\n      : paddedUserPassword;\r\n\r\n    const ownerPasswordEntry = getOwnerPasswordR2R3R4(\r\n      r,\r\n      this.keyBits,\r\n      paddedUserPassword,\r\n      paddedOwnerPassword\r\n    );\r\n    this.encryptionKey = getEncryptionKeyR2R3R4(\r\n      r,\r\n      this.keyBits,\r\n      this.document._id,\r\n      paddedUserPassword,\r\n      ownerPasswordEntry,\r\n      permissions\r\n    );\r\n    let userPasswordEntry;\r\n    if (r === 2) {\r\n      userPasswordEntry = getUserPasswordR2(this.encryptionKey);\r\n    } else {\r\n      userPasswordEntry = getUserPasswordR3R4(\r\n        this.document._id,\r\n        this.encryptionKey\r\n      );\r\n    }\r\n\r\n    encDict.V = v;\r\n    if (v >= 2) {\r\n      encDict.Length = this.keyBits;\r\n    }\r\n    if (v === 4) {\r\n      encDict.CF = {\r\n        StdCF: {\r\n          AuthEvent: 'DocOpen',\r\n          CFM: 'AESV2',\r\n          Length: this.keyBits / 8\r\n        }\r\n      };\r\n      encDict.StmF = 'StdCF';\r\n      encDict.StrF = 'StdCF';\r\n    }\r\n    encDict.R = r;\r\n    encDict.O = wordArrayToBuffer(ownerPasswordEntry);\r\n    encDict.U = wordArrayToBuffer(userPasswordEntry);\r\n    encDict.P = permissions;\r\n  }\r\n\r\n  _setupEncryptionV5(encDict, options) {\r\n    this.keyBits = 256;\r\n    const permissions = getPermissionsR3(options.permissions);\r\n\r\n    const processedUserPassword = processPasswordR5(options.userPassword);\r\n    const processedOwnerPassword = options.ownerPassword\r\n      ? processPasswordR5(options.ownerPassword)\r\n      : processedUserPassword;\r\n\r\n    this.encryptionKey = getEncryptionKeyR5(\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n    const userPasswordEntry = getUserPasswordR5(\r\n      processedUserPassword,\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n    const userKeySalt = CryptoJS.lib.WordArray.create(\r\n      userPasswordEntry.words.slice(10, 12),\r\n      8\r\n    );\r\n    const userEncryptionKeyEntry = getUserEncryptionKeyR5(\r\n      processedUserPassword,\r\n      userKeySalt,\r\n      this.encryptionKey\r\n    );\r\n    const ownerPasswordEntry = getOwnerPasswordR5(\r\n      processedOwnerPassword,\r\n      userPasswordEntry,\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n    const ownerKeySalt = CryptoJS.lib.WordArray.create(\r\n      ownerPasswordEntry.words.slice(10, 12),\r\n      8\r\n    );\r\n    const ownerEncryptionKeyEntry = getOwnerEncryptionKeyR5(\r\n      processedOwnerPassword,\r\n      ownerKeySalt,\r\n      userPasswordEntry,\r\n      this.encryptionKey\r\n    );\r\n    const permsEntry = getEncryptedPermissionsR5(\r\n      permissions,\r\n      this.encryptionKey,\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n\r\n    encDict.V = 5;\r\n    encDict.Length = this.keyBits;\r\n    encDict.CF = {\r\n      StdCF: {\r\n        AuthEvent: 'DocOpen',\r\n        CFM: 'AESV3',\r\n        Length: this.keyBits / 8\r\n      }\r\n    };\r\n    encDict.StmF = 'StdCF';\r\n    encDict.StrF = 'StdCF';\r\n    encDict.R = 5;\r\n    encDict.O = wordArrayToBuffer(ownerPasswordEntry);\r\n    encDict.OE = wordArrayToBuffer(ownerEncryptionKeyEntry);\r\n    encDict.U = wordArrayToBuffer(userPasswordEntry);\r\n    encDict.UE = wordArrayToBuffer(userEncryptionKeyEntry);\r\n    encDict.P = permissions;\r\n    encDict.Perms = wordArrayToBuffer(permsEntry);\r\n  }\r\n\r\n  getEncryptFn(obj, gen) {\r\n    let digest;\r\n    if (this.version < 5) {\r\n      digest = this.encryptionKey\r\n        .clone()\r\n        .concat(\r\n          CryptoJS.lib.WordArray.create(\r\n            [\r\n              ((obj & 0xff) << 24) |\r\n                ((obj & 0xff00) << 8) |\r\n                ((obj >> 8) & 0xff00) |\r\n                (gen & 0xff),\r\n              (gen & 0xff00) << 16\r\n            ],\r\n            5\r\n          )\r\n        );\r\n    }\r\n\r\n    if (this.version === 1 || this.version === 2) {\r\n      let key = CryptoJS.MD5(digest);\r\n      key.sigBytes = Math.min(16, this.keyBits / 8 + 5);\r\n      return buffer =>\r\n        wordArrayToBuffer(\r\n          CryptoJS.RC4.encrypt(CryptoJS.lib.WordArray.create(buffer), key)\r\n            .ciphertext\r\n        );\r\n    }\r\n\r\n    let key;\r\n    if (this.version === 4) {\r\n      key = CryptoJS.MD5(\r\n        digest.concat(CryptoJS.lib.WordArray.create([0x73416c54], 4))\r\n      );\r\n    } else {\r\n      key = this.encryptionKey;\r\n    }\r\n\r\n    const iv = PDFSecurity.generateRandomWordArray(16);\r\n    const options = {\r\n      mode: CryptoJS.mode.CBC,\r\n      padding: CryptoJS.pad.Pkcs7,\r\n      iv\r\n    };\r\n\r\n    return buffer =>\r\n      wordArrayToBuffer(\r\n        iv\r\n          .clone()\r\n          .concat(\r\n            CryptoJS.AES.encrypt(\r\n              CryptoJS.lib.WordArray.create(buffer),\r\n              key,\r\n              options\r\n            ).ciphertext\r\n          )\r\n      );\r\n  }\r\n\r\n  end() {\r\n    this.dictionary.end();\r\n  }\r\n}\r\n\r\nfunction getPermissionsR2(permissionObject = {}) {\r\n  let permissions = 0xffffffc0 >> 0;\r\n  if (permissionObject.printing) {\r\n    permissions |= 0b000000000100;\r\n  }\r\n  if (permissionObject.modifying) {\r\n    permissions |= 0b000000001000;\r\n  }\r\n  if (permissionObject.copying) {\r\n    permissions |= 0b000000010000;\r\n  }\r\n  if (permissionObject.annotating) {\r\n    permissions |= 0b000000100000;\r\n  }\r\n  return permissions;\r\n}\r\n\r\nfunction getPermissionsR3(permissionObject = {}) {\r\n  let permissions = 0xfffff0c0 >> 0;\r\n  if (permissionObject.printing === 'lowResolution') {\r\n    permissions |= 0b000000000100;\r\n  }\r\n  if (permissionObject.printing === 'highResolution') {\r\n    permissions |= 0b100000000100;\r\n  }\r\n  if (permissionObject.modifying) {\r\n    permissions |= 0b000000001000;\r\n  }\r\n  if (permissionObject.copying) {\r\n    permissions |= 0b000000010000;\r\n  }\r\n  if (permissionObject.annotating) {\r\n    permissions |= 0b000000100000;\r\n  }\r\n  if (permissionObject.fillingForms) {\r\n    permissions |= 0b000100000000;\r\n  }\r\n  if (permissionObject.contentAccessibility) {\r\n    permissions |= 0b001000000000;\r\n  }\r\n  if (permissionObject.documentAssembly) {\r\n    permissions |= 0b010000000000;\r\n  }\r\n  return permissions;\r\n}\r\n\r\nfunction getUserPasswordR2(encryptionKey) {\r\n  return CryptoJS.RC4.encrypt(processPasswordR2R3R4(), encryptionKey)\r\n    .ciphertext;\r\n}\r\n\r\nfunction getUserPasswordR3R4(documentId, encryptionKey) {\r\n  const key = encryptionKey.clone();\r\n  let cipher = CryptoJS.MD5(\r\n    processPasswordR2R3R4().concat(CryptoJS.lib.WordArray.create(documentId))\r\n  );\r\n  for (let i = 0; i < 20; i++) {\r\n    const xorRound = Math.ceil(key.sigBytes / 4);\r\n    for (let j = 0; j < xorRound; j++) {\r\n      key.words[j] =\r\n        encryptionKey.words[j] ^ (i | (i << 8) | (i << 16) | (i << 24));\r\n    }\r\n    cipher = CryptoJS.RC4.encrypt(cipher, key).ciphertext;\r\n  }\r\n  return cipher.concat(CryptoJS.lib.WordArray.create(null, 16));\r\n}\r\n\r\nfunction getOwnerPasswordR2R3R4(\r\n  r,\r\n  keyBits,\r\n  paddedUserPassword,\r\n  paddedOwnerPassword\r\n) {\r\n  let digest = paddedOwnerPassword;\r\n  let round = r >= 3 ? 51 : 1;\r\n  for (let i = 0; i < round; i++) {\r\n    digest = CryptoJS.MD5(digest);\r\n  }\r\n\r\n  const key = digest.clone();\r\n  key.sigBytes = keyBits / 8;\r\n  let cipher = paddedUserPassword;\r\n  round = r >= 3 ? 20 : 1;\r\n  for (let i = 0; i < round; i++) {\r\n    const xorRound = Math.ceil(key.sigBytes / 4);\r\n    for (let j = 0; j < xorRound; j++) {\r\n      key.words[j] = digest.words[j] ^ (i | (i << 8) | (i << 16) | (i << 24));\r\n    }\r\n    cipher = CryptoJS.RC4.encrypt(cipher, key).ciphertext;\r\n  }\r\n  return cipher;\r\n}\r\n\r\nfunction getEncryptionKeyR2R3R4(\r\n  r,\r\n  keyBits,\r\n  documentId,\r\n  paddedUserPassword,\r\n  ownerPasswordEntry,\r\n  permissions\r\n) {\r\n  let key = paddedUserPassword\r\n    .clone()\r\n    .concat(ownerPasswordEntry)\r\n    .concat(CryptoJS.lib.WordArray.create([lsbFirstWord(permissions)], 4))\r\n    .concat(CryptoJS.lib.WordArray.create(documentId));\r\n  const round = r >= 3 ? 51 : 1;\r\n  for (let i = 0; i < round; i++) {\r\n    key = CryptoJS.MD5(key);\r\n    key.sigBytes = keyBits / 8;\r\n  }\r\n  return key;\r\n}\r\n\r\nfunction getUserPasswordR5(processedUserPassword, generateRandomWordArray) {\r\n  const validationSalt = generateRandomWordArray(8);\r\n  const keySalt = generateRandomWordArray(8);\r\n  return CryptoJS.SHA256(processedUserPassword.clone().concat(validationSalt))\r\n    .concat(validationSalt)\r\n    .concat(keySalt);\r\n}\r\n\r\nfunction getUserEncryptionKeyR5(\r\n  processedUserPassword,\r\n  userKeySalt,\r\n  encryptionKey\r\n) {\r\n  const key = CryptoJS.SHA256(\r\n    processedUserPassword.clone().concat(userKeySalt)\r\n  );\r\n  const options = {\r\n    mode: CryptoJS.mode.CBC,\r\n    padding: CryptoJS.pad.NoPadding,\r\n    iv: CryptoJS.lib.WordArray.create(null, 16)\r\n  };\r\n  return CryptoJS.AES.encrypt(encryptionKey, key, options).ciphertext;\r\n}\r\n\r\nfunction getOwnerPasswordR5(\r\n  processedOwnerPassword,\r\n  userPasswordEntry,\r\n  generateRandomWordArray\r\n) {\r\n  const validationSalt = generateRandomWordArray(8);\r\n  const keySalt = generateRandomWordArray(8);\r\n  return CryptoJS.SHA256(\r\n    processedOwnerPassword\r\n      .clone()\r\n      .concat(validationSalt)\r\n      .concat(userPasswordEntry)\r\n  )\r\n    .concat(validationSalt)\r\n    .concat(keySalt);\r\n}\r\n\r\nfunction getOwnerEncryptionKeyR5(\r\n  processedOwnerPassword,\r\n  ownerKeySalt,\r\n  userPasswordEntry,\r\n  encryptionKey\r\n) {\r\n  const key = CryptoJS.SHA256(\r\n    processedOwnerPassword\r\n      .clone()\r\n      .concat(ownerKeySalt)\r\n      .concat(userPasswordEntry)\r\n  );\r\n  const options = {\r\n    mode: CryptoJS.mode.CBC,\r\n    padding: CryptoJS.pad.NoPadding,\r\n    iv: CryptoJS.lib.WordArray.create(null, 16)\r\n  };\r\n  return CryptoJS.AES.encrypt(encryptionKey, key, options).ciphertext;\r\n}\r\n\r\nfunction getEncryptionKeyR5(generateRandomWordArray) {\r\n  return generateRandomWordArray(32);\r\n}\r\n\r\nfunction getEncryptedPermissionsR5(\r\n  permissions,\r\n  encryptionKey,\r\n  generateRandomWordArray\r\n) {\r\n  const cipher = CryptoJS.lib.WordArray.create(\r\n    [lsbFirstWord(permissions), 0xffffffff, 0x54616462],\r\n    12\r\n  ).concat(generateRandomWordArray(4));\r\n  const options = {\r\n    mode: CryptoJS.mode.ECB,\r\n    padding: CryptoJS.pad.NoPadding\r\n  };\r\n  return CryptoJS.AES.encrypt(cipher, encryptionKey, options).ciphertext;\r\n}\r\n\r\nfunction processPasswordR2R3R4(password = '') {\r\n  const out = Buffer.alloc(32);\r\n  const length = password.length;\r\n  let index = 0;\r\n  while (index < length && index < 32) {\r\n    const code = password.charCodeAt(index);\r\n    if (code > 0xff) {\r\n      throw new Error('Password contains one or more invalid characters.');\r\n    }\r\n    out[index] = code;\r\n    index++;\r\n  }\r\n  while (index < 32) {\r\n    out[index] = PASSWORD_PADDING[index - length];\r\n    index++;\r\n  }\r\n  return CryptoJS.lib.WordArray.create(out);\r\n}\r\n\r\nfunction processPasswordR5(password = '') {\r\n  password = unescape(encodeURIComponent(saslprep(password)));\r\n  const length = Math.min(127, password.length);\r\n  const out = Buffer.alloc(length);\r\n\r\n  for (let i = 0; i < length; i++) {\r\n    out[i] = password.charCodeAt(i);\r\n  }\r\n\r\n  return CryptoJS.lib.WordArray.create(out);\r\n}\r\n\r\nfunction lsbFirstWord(data) {\r\n  return (\r\n    ((data & 0xff) << 24) |\r\n    ((data & 0xff00) << 8) |\r\n    ((data >> 8) & 0xff00) |\r\n    ((data >> 24) & 0xff)\r\n  );\r\n}\r\n\r\nfunction wordArrayToBuffer(wordArray) {\r\n  const byteArray = [];\r\n  for (let i = 0; i < wordArray.sigBytes; i++) {\r\n    byteArray.push(\r\n      (wordArray.words[Math.floor(i / 4)] >> (8 * (3 - (i % 4)))) & 0xff\r\n    );\r\n  }\r\n  return Buffer.from(byteArray);\r\n}\r\n\r\nconst PASSWORD_PADDING = [\r\n  0x28,\r\n  0xbf,\r\n  0x4e,\r\n  0x5e,\r\n  0x4e,\r\n  0x75,\r\n  0x8a,\r\n  0x41,\r\n  0x64,\r\n  0x00,\r\n  0x4e,\r\n  0x56,\r\n  0xff,\r\n  0xfa,\r\n  0x01,\r\n  0x08,\r\n  0x2e,\r\n  0x2e,\r\n  0x00,\r\n  0xb6,\r\n  0xd0,\r\n  0x68,\r\n  0x3e,\r\n  0x80,\r\n  0x2f,\r\n  0x0c,\r\n  0xa9,\r\n  0xfe,\r\n  0x64,\r\n  0x53,\r\n  0x69,\r\n  0x7a\r\n];\r\n\r\nexport default PDFSecurity;\r\n", "import PDFObject from './object';\r\n\r\nconst { number } = PDFObject;\r\n\r\nclass PDFGradient {\r\n  constructor(doc) {\r\n    this.doc = doc;\r\n    this.stops = [];\r\n    this.embedded = false;\r\n    this.transform = [1, 0, 0, 1, 0, 0];\r\n  }\r\n\r\n  stop(pos, color, opacity) {\r\n    if (opacity == null) {\r\n      opacity = 1;\r\n    }\r\n    color = this.doc._normalizeColor(color);\r\n\r\n    if (this.stops.length === 0) {\r\n      if (color.length === 3) {\r\n        this._colorSpace = 'DeviceRGB';\r\n      } else if (color.length === 4) {\r\n        this._colorSpace = 'DeviceCMYK';\r\n      } else if (color.length === 1) {\r\n        this._colorSpace = 'DeviceGray';\r\n      } else {\r\n        throw new Error('Unknown color space');\r\n      }\r\n    } else if (\r\n      (this._colorSpace === 'DeviceRGB' && color.length !== 3) ||\r\n      (this._colorSpace === 'DeviceCMYK' && color.length !== 4) ||\r\n      (this._colorSpace === 'DeviceGray' && color.length !== 1)\r\n    ) {\r\n      throw new Error('All gradient stops must use the same color space');\r\n    }\r\n\r\n    opacity = Math.max(0, Math.min(1, opacity));\r\n    this.stops.push([pos, color, opacity]);\r\n    return this;\r\n  }\r\n\r\n  setTransform(m11, m12, m21, m22, dx, dy) {\r\n    this.transform = [m11, m12, m21, m22, dx, dy];\r\n    return this;\r\n  }\r\n\r\n  embed(m) {\r\n    let fn;\r\n    const stopsLength = this.stops.length;\r\n    if (stopsLength === 0) {\r\n      return;\r\n    }\r\n    this.embedded = true;\r\n    this.matrix = m;\r\n\r\n    // if the last stop comes before 100%, add a copy at 100%\r\n    const last = this.stops[stopsLength - 1];\r\n    if (last[0] < 1) {\r\n      this.stops.push([1, last[1], last[2]]);\r\n    }\r\n\r\n    const bounds = [];\r\n    const encode = [];\r\n    const stops = [];\r\n\r\n    for (let i = 0; i < stopsLength - 1; i++) {\r\n      encode.push(0, 1);\r\n      if (i + 2 !== stopsLength) {\r\n        bounds.push(this.stops[i + 1][0]);\r\n      }\r\n\r\n      fn = this.doc.ref({\r\n        FunctionType: 2,\r\n        Domain: [0, 1],\r\n        C0: this.stops[i + 0][1],\r\n        C1: this.stops[i + 1][1],\r\n        N: 1\r\n      });\r\n\r\n      stops.push(fn);\r\n      fn.end();\r\n    }\r\n\r\n    // if there are only two stops, we don't need a stitching function\r\n    if (stopsLength === 1) {\r\n      fn = stops[0];\r\n    } else {\r\n      fn = this.doc.ref({\r\n        FunctionType: 3, // stitching function\r\n        Domain: [0, 1],\r\n        Functions: stops,\r\n        Bounds: bounds,\r\n        Encode: encode\r\n      });\r\n\r\n      fn.end();\r\n    }\r\n\r\n    this.id = `Sh${++this.doc._gradCount}`;\r\n\r\n    const shader = this.shader(fn);\r\n    shader.end();\r\n\r\n    const pattern = this.doc.ref({\r\n      Type: 'Pattern',\r\n      PatternType: 2,\r\n      Shading: shader,\r\n      Matrix: this.matrix.map(number)\r\n    });\r\n\r\n    pattern.end();\r\n\r\n    if (this.stops.some(stop => stop[2] < 1)) {\r\n      let grad = this.opacityGradient();\r\n      grad._colorSpace = 'DeviceGray';\r\n\r\n      for (let stop of this.stops) {\r\n        grad.stop(stop[0], [stop[2]]);\r\n      }\r\n\r\n      grad = grad.embed(this.matrix);\r\n\r\n      const pageBBox = [0, 0, this.doc.page.width, this.doc.page.height];\r\n\r\n      const form = this.doc.ref({\r\n        Type: 'XObject',\r\n        Subtype: 'Form',\r\n        FormType: 1,\r\n        BBox: pageBBox,\r\n        Group: {\r\n          Type: 'Group',\r\n          S: 'Transparency',\r\n          CS: 'DeviceGray'\r\n        },\r\n        Resources: {\r\n          ProcSet: ['PDF', 'Text', 'ImageB', 'ImageC', 'ImageI'],\r\n          Pattern: {\r\n            Sh1: grad\r\n          }\r\n        }\r\n      });\r\n\r\n      form.write('/Pattern cs /Sh1 scn');\r\n      form.end(`${pageBBox.join(' ')} re f`);\r\n\r\n      const gstate = this.doc.ref({\r\n        Type: 'ExtGState',\r\n        SMask: {\r\n          Type: 'Mask',\r\n          S: 'Luminosity',\r\n          G: form\r\n        }\r\n      });\r\n\r\n      gstate.end();\r\n\r\n      const opacityPattern = this.doc.ref({\r\n        Type: 'Pattern',\r\n        PatternType: 1,\r\n        PaintType: 1,\r\n        TilingType: 2,\r\n        BBox: pageBBox,\r\n        XStep: pageBBox[2],\r\n        YStep: pageBBox[3],\r\n        Resources: {\r\n          ProcSet: ['PDF', 'Text', 'ImageB', 'ImageC', 'ImageI'],\r\n          Pattern: {\r\n            Sh1: pattern\r\n          },\r\n          ExtGState: {\r\n            Gs1: gstate\r\n          }\r\n        }\r\n      });\r\n\r\n      opacityPattern.write('/Gs1 gs /Pattern cs /Sh1 scn');\r\n      opacityPattern.end(`${pageBBox.join(' ')} re f`);\r\n\r\n      this.doc.page.patterns[this.id] = opacityPattern;\r\n    } else {\r\n      this.doc.page.patterns[this.id] = pattern;\r\n    }\r\n\r\n    return pattern;\r\n  }\r\n\r\n  apply(stroke) {\r\n    // apply gradient transform to existing document ctm\r\n    const [m0, m1, m2, m3, m4, m5] = this.doc._ctm;\r\n    const [m11, m12, m21, m22, dx, dy] = this.transform;\r\n    const m = [\r\n      m0 * m11 + m2 * m12,\r\n      m1 * m11 + m3 * m12,\r\n      m0 * m21 + m2 * m22,\r\n      m1 * m21 + m3 * m22,\r\n      m0 * dx + m2 * dy + m4,\r\n      m1 * dx + m3 * dy + m5\r\n    ];\r\n\r\n    if (!this.embedded || m.join(' ') !== this.matrix.join(' ')) {\r\n      this.embed(m);\r\n    }\r\n    this.doc._setColorSpace('Pattern', stroke);\r\n    const op = stroke ? 'SCN' : 'scn';\r\n    return this.doc.addContent(`/${this.id} ${op}`);\r\n  }\r\n}\r\n\r\nclass PDFLinearGradient extends PDFGradient {\r\n  constructor(doc, x1, y1, x2, y2) {\r\n    super(doc);\r\n    this.x1 = x1;\r\n    this.y1 = y1;\r\n    this.x2 = x2;\r\n    this.y2 = y2;\r\n  }\r\n\r\n  shader(fn) {\r\n    return this.doc.ref({\r\n      ShadingType: 2,\r\n      ColorSpace: this._colorSpace,\r\n      Coords: [this.x1, this.y1, this.x2, this.y2],\r\n      Function: fn,\r\n      Extend: [true, true]\r\n    });\r\n  }\r\n\r\n  opacityGradient() {\r\n    return new PDFLinearGradient(this.doc, this.x1, this.y1, this.x2, this.y2);\r\n  }\r\n}\r\n\r\nclass PDFRadialGradient extends PDFGradient {\r\n  constructor(doc, x1, y1, r1, x2, y2, r2) {\r\n    super(doc);\r\n    this.doc = doc;\r\n    this.x1 = x1;\r\n    this.y1 = y1;\r\n    this.r1 = r1;\r\n    this.x2 = x2;\r\n    this.y2 = y2;\r\n    this.r2 = r2;\r\n  }\r\n\r\n  shader(fn) {\r\n    return this.doc.ref({\r\n      ShadingType: 3,\r\n      ColorSpace: this._colorSpace,\r\n      Coords: [this.x1, this.y1, this.r1, this.x2, this.y2, this.r2],\r\n      Function: fn,\r\n      Extend: [true, true]\r\n    });\r\n  }\r\n\r\n  opacityGradient() {\r\n    return new PDFRadialGradient(\r\n      this.doc,\r\n      this.x1,\r\n      this.y1,\r\n      this.r1,\r\n      this.x2,\r\n      this.y2,\r\n      this.r2\r\n    );\r\n  }\r\n}\r\n\r\nexport default { PDFGradient, PDFLinearGradient, PDFRadialGradient };\r\n", "/*\r\nPDF tiling pattern support. Uncolored only.\r\n */\r\n\r\nconst underlyingColorSpaces = ['DeviceCMYK', 'DeviceRGB'];\r\n\r\nclass PDFTilingPattern {\r\n  constructor(doc, bBox, xStep, yStep, stream) {\r\n    this.doc = doc;\r\n    this.bBox = bBox;\r\n    this.xStep = xStep;\r\n    this.yStep = yStep;\r\n    this.stream = stream;\r\n  }\r\n\r\n  createPattern() {\r\n    // no resources needed for our current usage\r\n    // required entry\r\n    const resources = this.doc.ref();\r\n    resources.end();\r\n    // apply default transform matrix (flipped in the default doc._ctm)\r\n    // see document.js & gradient.js\r\n    const [m0, m1, m2, m3, m4, m5] = this.doc._ctm;\r\n    const [m11, m12, m21, m22, dx, dy] = [1, 0, 0, 1, 0, 0];\r\n    const m = [\r\n      m0 * m11 + m2 * m12,\r\n      m1 * m11 + m3 * m12,\r\n      m0 * m21 + m2 * m22,\r\n      m1 * m21 + m3 * m22,\r\n      m0 * dx + m2 * dy + m4,\r\n      m1 * dx + m3 * dy + m5\r\n    ];\r\n    const pattern = this.doc.ref({\r\n      Type: 'Pattern',\r\n      PatternType: 1, // tiling\r\n      PaintType: 2, // 1-colored, 2-uncolored\r\n      TilingType: 2, // 2-no distortion\r\n      BBox: this.bBox,\r\n      XStep: this.xStep,\r\n      YStep: this.yStep,\r\n      Matrix: m.map(v => +v.toFixed(5)),\r\n      Resources: resources\r\n    });\r\n    pattern.end(this.stream);\r\n    return pattern;\r\n  }\r\n\r\n  embedPatternColorSpaces() {\r\n    // map each pattern to an underlying color space\r\n    // and embed on each page\r\n    underlyingColorSpaces.forEach(csName => {\r\n      const csId = this.getPatternColorSpaceId(csName);\r\n\r\n      if (this.doc.page.colorSpaces[csId]) return;\r\n      const cs = this.doc.ref(['Pattern', csName]);\r\n      cs.end();\r\n      this.doc.page.colorSpaces[csId] = cs;\r\n    });\r\n  }\r\n\r\n  getPatternColorSpaceId(underlyingColorspace) {\r\n    return `CsP${underlyingColorspace}`;\r\n  }\r\n\r\n  embed() {\r\n    if (!this.id) {\r\n      this.doc._patternCount = this.doc._patternCount + 1;\r\n      this.id = 'P' + this.doc._patternCount;\r\n      this.pattern = this.createPattern();\r\n    }\r\n\r\n    // patterns are embedded in each page\r\n    if (!this.doc.page.patterns[this.id]) {\r\n      this.doc.page.patterns[this.id] = this.pattern;\r\n    }\r\n  }\r\n\r\n  apply(stroke, patternColor) {\r\n    // do any embedding/creating that might be needed\r\n    this.embedPatternColorSpaces();\r\n    this.embed();\r\n\r\n    const normalizedColor = this.doc._normalizeColor(patternColor);\r\n    if (!normalizedColor)\r\n      throw Error(`invalid pattern color. (value: ${patternColor})`);\r\n\r\n    // select one of the pattern color spaces\r\n    const csId = this.getPatternColorSpaceId(\r\n      this.doc._getColorSpace(normalizedColor)\r\n    );\r\n    this.doc._setColorSpace(csId, stroke);\r\n\r\n    // stroke/fill using the pattern and color (in the above underlying color space)\r\n    const op = stroke ? 'SCN' : 'scn';\r\n    return this.doc.addContent(\r\n      `${normalizedColor.join(' ')} /${this.id} ${op}`\r\n    );\r\n  }\r\n}\r\n\r\nexport default { PDFTilingPattern };\r\n", "import Gradient from '../gradient';\r\nimport pattern from '../pattern';\r\n\r\nconst { PDFGradient, PDFLinearGradient, PDFRadialGradient } = Gradient;\r\nconst { PDFTilingPattern } = pattern;\r\n\r\nexport default {\r\n  initColor() {\r\n    // The opacity dictionaries\r\n    this._opacityRegistry = {};\r\n    this._opacityCount = 0;\r\n    this._patternCount = 0;\r\n    return (this._gradCount = 0);\r\n  },\r\n\r\n  _normalizeColor(color) {\r\n    if (typeof color === 'string') {\r\n      if (color.charAt(0) === '#') {\r\n        if (color.length === 4) {\r\n          color = color.replace(\r\n            /#([0-9A-F])([0-9A-F])([0-9A-F])/i,\r\n            '#$1$1$2$2$3$3'\r\n          );\r\n        }\r\n        const hex = parseInt(color.slice(1), 16);\r\n        color = [hex >> 16, (hex >> 8) & 0xff, hex & 0xff];\r\n      } else if (namedColors[color]) {\r\n        color = namedColors[color];\r\n      }\r\n    }\r\n\r\n    if (Array.isArray(color)) {\r\n      // RGB\r\n      if (color.length === 3) {\r\n        color = color.map(part => part / 255);\r\n        // CMYK\r\n      } else if (color.length === 4) {\r\n        color = color.map(part => part / 100);\r\n      }\r\n      return color;\r\n    }\r\n\r\n    return null;\r\n  },\r\n\r\n  _setColor(color, stroke) {\r\n    if (color instanceof PDFGradient) {\r\n      color.apply(stroke);\r\n      return true;\r\n      // see if tiling pattern, decode & apply it it\r\n    } else if (Array.isArray(color) && color[0] instanceof PDFTilingPattern) {\r\n      color[0].apply(stroke, color[1]);\r\n      return true;\r\n    }\r\n    // any other case should be a normal color and not a pattern\r\n    return this._setColorCore(color, stroke);\r\n  },\r\n\r\n  _setColorCore(color, stroke) {\r\n    color = this._normalizeColor(color);\r\n    if (!color) {\r\n      return false;\r\n    }\r\n\r\n    const op = stroke ? 'SCN' : 'scn';\r\n    const space = this._getColorSpace(color);\r\n    this._setColorSpace(space, stroke);\r\n\r\n    color = color.join(' ');\r\n    this.addContent(`${color} ${op}`);\r\n\r\n    return true;\r\n  },\r\n\r\n  _setColorSpace(space, stroke) {\r\n    const op = stroke ? 'CS' : 'cs';\r\n    return this.addContent(`/${space} ${op}`);\r\n  },\r\n\r\n  _getColorSpace(color) {\r\n    return color.length === 4 ? 'DeviceCMYK' : 'DeviceRGB';\r\n  },\r\n\r\n  fillColor(color, opacity) {\r\n    const set = this._setColor(color, false);\r\n    if (set) {\r\n      this.fillOpacity(opacity);\r\n    }\r\n\r\n    // save this for text wrapper, which needs to reset\r\n    // the fill color on new pages\r\n    this._fillColor = [color, opacity];\r\n    return this;\r\n  },\r\n\r\n  strokeColor(color, opacity) {\r\n    const set = this._setColor(color, true);\r\n    if (set) {\r\n      this.strokeOpacity(opacity);\r\n    }\r\n    return this;\r\n  },\r\n\r\n  opacity(opacity) {\r\n    this._doOpacity(opacity, opacity);\r\n    return this;\r\n  },\r\n\r\n  fillOpacity(opacity) {\r\n    this._doOpacity(opacity, null);\r\n    return this;\r\n  },\r\n\r\n  strokeOpacity(opacity) {\r\n    this._doOpacity(null, opacity);\r\n    return this;\r\n  },\r\n\r\n  _doOpacity(fillOpacity, strokeOpacity) {\r\n    let dictionary, name;\r\n    if (fillOpacity == null && strokeOpacity == null) {\r\n      return;\r\n    }\r\n\r\n    if (fillOpacity != null) {\r\n      fillOpacity = Math.max(0, Math.min(1, fillOpacity));\r\n    }\r\n    if (strokeOpacity != null) {\r\n      strokeOpacity = Math.max(0, Math.min(1, strokeOpacity));\r\n    }\r\n    const key = `${fillOpacity}_${strokeOpacity}`;\r\n\r\n    if (this._opacityRegistry[key]) {\r\n      [dictionary, name] = this._opacityRegistry[key];\r\n    } else {\r\n      dictionary = { Type: 'ExtGState' };\r\n\r\n      if (fillOpacity != null) {\r\n        dictionary.ca = fillOpacity;\r\n      }\r\n      if (strokeOpacity != null) {\r\n        dictionary.CA = strokeOpacity;\r\n      }\r\n\r\n      dictionary = this.ref(dictionary);\r\n      dictionary.end();\r\n      const id = ++this._opacityCount;\r\n      name = `Gs${id}`;\r\n      this._opacityRegistry[key] = [dictionary, name];\r\n    }\r\n\r\n    this.page.ext_gstates[name] = dictionary;\r\n    return this.addContent(`/${name} gs`);\r\n  },\r\n\r\n  linearGradient(x1, y1, x2, y2) {\r\n    return new PDFLinearGradient(this, x1, y1, x2, y2);\r\n  },\r\n\r\n  radialGradient(x1, y1, r1, x2, y2, r2) {\r\n    return new PDFRadialGradient(this, x1, y1, r1, x2, y2, r2);\r\n  },\r\n\r\n  pattern(bbox, xStep, yStep, stream) {\r\n    return new PDFTilingPattern(this, bbox, xStep, yStep, stream);\r\n  }\r\n};\r\n\r\nvar namedColors = {\r\n  aliceblue: [240, 248, 255],\r\n  antiquewhite: [250, 235, 215],\r\n  aqua: [0, 255, 255],\r\n  aquamarine: [127, 255, 212],\r\n  azure: [240, 255, 255],\r\n  beige: [245, 245, 220],\r\n  bisque: [255, 228, 196],\r\n  black: [0, 0, 0],\r\n  blanchedalmond: [255, 235, 205],\r\n  blue: [0, 0, 255],\r\n  blueviolet: [138, 43, 226],\r\n  brown: [165, 42, 42],\r\n  burlywood: [222, 184, 135],\r\n  cadetblue: [95, 158, 160],\r\n  chartreuse: [127, 255, 0],\r\n  chocolate: [210, 105, 30],\r\n  coral: [255, 127, 80],\r\n  cornflowerblue: [100, 149, 237],\r\n  cornsilk: [255, 248, 220],\r\n  crimson: [220, 20, 60],\r\n  cyan: [0, 255, 255],\r\n  darkblue: [0, 0, 139],\r\n  darkcyan: [0, 139, 139],\r\n  darkgoldenrod: [184, 134, 11],\r\n  darkgray: [169, 169, 169],\r\n  darkgreen: [0, 100, 0],\r\n  darkgrey: [169, 169, 169],\r\n  darkkhaki: [189, 183, 107],\r\n  darkmagenta: [139, 0, 139],\r\n  darkolivegreen: [85, 107, 47],\r\n  darkorange: [255, 140, 0],\r\n  darkorchid: [153, 50, 204],\r\n  darkred: [139, 0, 0],\r\n  darksalmon: [233, 150, 122],\r\n  darkseagreen: [143, 188, 143],\r\n  darkslateblue: [72, 61, 139],\r\n  darkslategray: [47, 79, 79],\r\n  darkslategrey: [47, 79, 79],\r\n  darkturquoise: [0, 206, 209],\r\n  darkviolet: [148, 0, 211],\r\n  deeppink: [255, 20, 147],\r\n  deepskyblue: [0, 191, 255],\r\n  dimgray: [105, 105, 105],\r\n  dimgrey: [105, 105, 105],\r\n  dodgerblue: [30, 144, 255],\r\n  firebrick: [178, 34, 34],\r\n  floralwhite: [255, 250, 240],\r\n  forestgreen: [34, 139, 34],\r\n  fuchsia: [255, 0, 255],\r\n  gainsboro: [220, 220, 220],\r\n  ghostwhite: [248, 248, 255],\r\n  gold: [255, 215, 0],\r\n  goldenrod: [218, 165, 32],\r\n  gray: [128, 128, 128],\r\n  grey: [128, 128, 128],\r\n  green: [0, 128, 0],\r\n  greenyellow: [173, 255, 47],\r\n  honeydew: [240, 255, 240],\r\n  hotpink: [255, 105, 180],\r\n  indianred: [205, 92, 92],\r\n  indigo: [75, 0, 130],\r\n  ivory: [255, 255, 240],\r\n  khaki: [240, 230, 140],\r\n  lavender: [230, 230, 250],\r\n  lavenderblush: [255, 240, 245],\r\n  lawngreen: [124, 252, 0],\r\n  lemonchiffon: [255, 250, 205],\r\n  lightblue: [173, 216, 230],\r\n  lightcoral: [240, 128, 128],\r\n  lightcyan: [224, 255, 255],\r\n  lightgoldenrodyellow: [250, 250, 210],\r\n  lightgray: [211, 211, 211],\r\n  lightgreen: [144, 238, 144],\r\n  lightgrey: [211, 211, 211],\r\n  lightpink: [255, 182, 193],\r\n  lightsalmon: [255, 160, 122],\r\n  lightseagreen: [32, 178, 170],\r\n  lightskyblue: [135, 206, 250],\r\n  lightslategray: [119, 136, 153],\r\n  lightslategrey: [119, 136, 153],\r\n  lightsteelblue: [176, 196, 222],\r\n  lightyellow: [255, 255, 224],\r\n  lime: [0, 255, 0],\r\n  limegreen: [50, 205, 50],\r\n  linen: [250, 240, 230],\r\n  magenta: [255, 0, 255],\r\n  maroon: [128, 0, 0],\r\n  mediumaquamarine: [102, 205, 170],\r\n  mediumblue: [0, 0, 205],\r\n  mediumorchid: [186, 85, 211],\r\n  mediumpurple: [147, 112, 219],\r\n  mediumseagreen: [60, 179, 113],\r\n  mediumslateblue: [123, 104, 238],\r\n  mediumspringgreen: [0, 250, 154],\r\n  mediumturquoise: [72, 209, 204],\r\n  mediumvioletred: [199, 21, 133],\r\n  midnightblue: [25, 25, 112],\r\n  mintcream: [245, 255, 250],\r\n  mistyrose: [255, 228, 225],\r\n  moccasin: [255, 228, 181],\r\n  navajowhite: [255, 222, 173],\r\n  navy: [0, 0, 128],\r\n  oldlace: [253, 245, 230],\r\n  olive: [128, 128, 0],\r\n  olivedrab: [107, 142, 35],\r\n  orange: [255, 165, 0],\r\n  orangered: [255, 69, 0],\r\n  orchid: [218, 112, 214],\r\n  palegoldenrod: [238, 232, 170],\r\n  palegreen: [152, 251, 152],\r\n  paleturquoise: [175, 238, 238],\r\n  palevioletred: [219, 112, 147],\r\n  papayawhip: [255, 239, 213],\r\n  peachpuff: [255, 218, 185],\r\n  peru: [205, 133, 63],\r\n  pink: [255, 192, 203],\r\n  plum: [221, 160, 221],\r\n  powderblue: [176, 224, 230],\r\n  purple: [128, 0, 128],\r\n  red: [255, 0, 0],\r\n  rosybrown: [188, 143, 143],\r\n  royalblue: [65, 105, 225],\r\n  saddlebrown: [139, 69, 19],\r\n  salmon: [250, 128, 114],\r\n  sandybrown: [244, 164, 96],\r\n  seagreen: [46, 139, 87],\r\n  seashell: [255, 245, 238],\r\n  sienna: [160, 82, 45],\r\n  silver: [192, 192, 192],\r\n  skyblue: [135, 206, 235],\r\n  slateblue: [106, 90, 205],\r\n  slategray: [112, 128, 144],\r\n  slategrey: [112, 128, 144],\r\n  snow: [255, 250, 250],\r\n  springgreen: [0, 255, 127],\r\n  steelblue: [70, 130, 180],\r\n  tan: [210, 180, 140],\r\n  teal: [0, 128, 128],\r\n  thistle: [216, 191, 216],\r\n  tomato: [255, 99, 71],\r\n  turquoise: [64, 224, 208],\r\n  violet: [238, 130, 238],\r\n  wheat: [245, 222, 179],\r\n  white: [255, 255, 255],\r\n  whitesmoke: [245, 245, 245],\r\n  yellow: [255, 255, 0],\r\n  yellowgreen: [154, 205, 50]\r\n};\r\n", "let cx, cy, px, py, sx, sy;\r\n\r\ncx = cy = px = py = sx = sy = 0;\r\n\r\nconst parameters = {\r\n  A: 7,\r\n  a: 7,\r\n  C: 6,\r\n  c: 6,\r\n  H: 1,\r\n  h: 1,\r\n  L: 2,\r\n  l: 2,\r\n  M: 2,\r\n  m: 2,\r\n  Q: 4,\r\n  q: 4,\r\n  S: 4,\r\n  s: 4,\r\n  T: 2,\r\n  t: 2,\r\n  V: 1,\r\n  v: 1,\r\n  Z: 0,\r\n  z: 0\r\n};\r\n\r\nconst parse = function(path) {\r\n  let cmd;\r\n  const ret = [];\r\n  let args = [];\r\n  let curArg = '';\r\n  let foundDecimal = false;\r\n  let params = 0;\r\n\r\n  for (let c of path) {\r\n    if (parameters[c] != null) {\r\n      params = parameters[c];\r\n      if (cmd) {\r\n        // save existing command\r\n        if (curArg.length > 0) {\r\n          args[args.length] = +curArg;\r\n        }\r\n        ret[ret.length] = { cmd, args };\r\n\r\n        args = [];\r\n        curArg = '';\r\n        foundDecimal = false;\r\n      }\r\n\r\n      cmd = c;\r\n    } else if (\r\n      [' ', ','].includes(c) ||\r\n      (c === '-' && curArg.length > 0 && curArg[curArg.length - 1] !== 'e') ||\r\n      (c === '.' && foundDecimal)\r\n    ) {\r\n      if (curArg.length === 0) {\r\n        continue;\r\n      }\r\n\r\n      if (args.length === params) {\r\n        // handle reused commands\r\n        ret[ret.length] = { cmd, args };\r\n        args = [+curArg];\r\n\r\n        // handle assumed commands\r\n        if (cmd === 'M') {\r\n          cmd = 'L';\r\n        }\r\n        if (cmd === 'm') {\r\n          cmd = 'l';\r\n        }\r\n      } else {\r\n        args[args.length] = +curArg;\r\n      }\r\n\r\n      foundDecimal = c === '.';\r\n\r\n      // fix for negative numbers or repeated decimals with no delimeter between commands\r\n      curArg = ['-', '.'].includes(c) ? c : '';\r\n    } else {\r\n      curArg += c;\r\n      if (c === '.') {\r\n        foundDecimal = true;\r\n      }\r\n    }\r\n  }\r\n\r\n  // add the last command\r\n  if (curArg.length > 0) {\r\n    if (args.length === params) {\r\n      // handle reused commands\r\n      ret[ret.length] = { cmd, args };\r\n      args = [+curArg];\r\n\r\n      // handle assumed commands\r\n      if (cmd === 'M') {\r\n        cmd = 'L';\r\n      }\r\n      if (cmd === 'm') {\r\n        cmd = 'l';\r\n      }\r\n    } else {\r\n      args[args.length] = +curArg;\r\n    }\r\n  }\r\n\r\n  ret[ret.length] = { cmd, args };\r\n\r\n  return ret;\r\n};\r\n\r\nconst apply = function(commands, doc) {\r\n  // current point, control point, and subpath starting point\r\n  cx = cy = px = py = sx = sy = 0;\r\n\r\n  // run the commands\r\n  for (let i = 0; i < commands.length; i++) {\r\n    const c = commands[i];\r\n    if (typeof runners[c.cmd] === 'function') {\r\n      runners[c.cmd](doc, c.args);\r\n    }\r\n  }\r\n};\r\n\r\nconst runners = {\r\n  M(doc, a) {\r\n    cx = a[0];\r\n    cy = a[1];\r\n    px = py = null;\r\n    sx = cx;\r\n    sy = cy;\r\n    return doc.moveTo(cx, cy);\r\n  },\r\n\r\n  m(doc, a) {\r\n    cx += a[0];\r\n    cy += a[1];\r\n    px = py = null;\r\n    sx = cx;\r\n    sy = cy;\r\n    return doc.moveTo(cx, cy);\r\n  },\r\n\r\n  C(doc, a) {\r\n    cx = a[4];\r\n    cy = a[5];\r\n    px = a[2];\r\n    py = a[3];\r\n    return doc.bezierCurveTo(...a);\r\n  },\r\n\r\n  c(doc, a) {\r\n    doc.bezierCurveTo(\r\n      a[0] + cx,\r\n      a[1] + cy,\r\n      a[2] + cx,\r\n      a[3] + cy,\r\n      a[4] + cx,\r\n      a[5] + cy\r\n    );\r\n    px = cx + a[2];\r\n    py = cy + a[3];\r\n    cx += a[4];\r\n    return (cy += a[5]);\r\n  },\r\n\r\n  S(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    }\r\n\r\n    doc.bezierCurveTo(cx - (px - cx), cy - (py - cy), a[0], a[1], a[2], a[3]);\r\n    px = a[0];\r\n    py = a[1];\r\n    cx = a[2];\r\n    return (cy = a[3]);\r\n  },\r\n\r\n  s(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    }\r\n\r\n    doc.bezierCurveTo(\r\n      cx - (px - cx),\r\n      cy - (py - cy),\r\n      cx + a[0],\r\n      cy + a[1],\r\n      cx + a[2],\r\n      cy + a[3]\r\n    );\r\n    px = cx + a[0];\r\n    py = cy + a[1];\r\n    cx += a[2];\r\n    return (cy += a[3]);\r\n  },\r\n\r\n  Q(doc, a) {\r\n    px = a[0];\r\n    py = a[1];\r\n    cx = a[2];\r\n    cy = a[3];\r\n    return doc.quadraticCurveTo(a[0], a[1], cx, cy);\r\n  },\r\n\r\n  q(doc, a) {\r\n    doc.quadraticCurveTo(a[0] + cx, a[1] + cy, a[2] + cx, a[3] + cy);\r\n    px = cx + a[0];\r\n    py = cy + a[1];\r\n    cx += a[2];\r\n    return (cy += a[3]);\r\n  },\r\n\r\n  T(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    } else {\r\n      px = cx - (px - cx);\r\n      py = cy - (py - cy);\r\n    }\r\n\r\n    doc.quadraticCurveTo(px, py, a[0], a[1]);\r\n    px = cx - (px - cx);\r\n    py = cy - (py - cy);\r\n    cx = a[0];\r\n    return (cy = a[1]);\r\n  },\r\n\r\n  t(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    } else {\r\n      px = cx - (px - cx);\r\n      py = cy - (py - cy);\r\n    }\r\n\r\n    doc.quadraticCurveTo(px, py, cx + a[0], cy + a[1]);\r\n    cx += a[0];\r\n    return (cy += a[1]);\r\n  },\r\n\r\n  A(doc, a) {\r\n    solveArc(doc, cx, cy, a);\r\n    cx = a[5];\r\n    return (cy = a[6]);\r\n  },\r\n\r\n  a(doc, a) {\r\n    a[5] += cx;\r\n    a[6] += cy;\r\n    solveArc(doc, cx, cy, a);\r\n    cx = a[5];\r\n    return (cy = a[6]);\r\n  },\r\n\r\n  L(doc, a) {\r\n    cx = a[0];\r\n    cy = a[1];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  l(doc, a) {\r\n    cx += a[0];\r\n    cy += a[1];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  H(doc, a) {\r\n    cx = a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  h(doc, a) {\r\n    cx += a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  V(doc, a) {\r\n    cy = a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  v(doc, a) {\r\n    cy += a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  Z(doc) {\r\n    doc.closePath();\r\n    cx = sx;\r\n    return (cy = sy);\r\n  },\r\n\r\n  z(doc) {\r\n    doc.closePath();\r\n    cx = sx;\r\n    return (cy = sy);\r\n  }\r\n};\r\n\r\nconst solveArc = function(doc, x, y, coords) {\r\n  const [rx, ry, rot, large, sweep, ex, ey] = coords;\r\n  const segs = arcToSegments(ex, ey, rx, ry, large, sweep, rot, x, y);\r\n\r\n  for (let seg of segs) {\r\n    const bez = segmentToBezier(...seg);\r\n    doc.bezierCurveTo(...bez);\r\n  }\r\n};\r\n\r\n// from Inkscape svgtopdf, thanks!\r\nconst arcToSegments = function(x, y, rx, ry, large, sweep, rotateX, ox, oy) {\r\n  const th = rotateX * (Math.PI / 180);\r\n  const sin_th = Math.sin(th);\r\n  const cos_th = Math.cos(th);\r\n  rx = Math.abs(rx);\r\n  ry = Math.abs(ry);\r\n  px = cos_th * (ox - x) * 0.5 + sin_th * (oy - y) * 0.5;\r\n  py = cos_th * (oy - y) * 0.5 - sin_th * (ox - x) * 0.5;\r\n  let pl = (px * px) / (rx * rx) + (py * py) / (ry * ry);\r\n  if (pl > 1) {\r\n    pl = Math.sqrt(pl);\r\n    rx *= pl;\r\n    ry *= pl;\r\n  }\r\n\r\n  const a00 = cos_th / rx;\r\n  const a01 = sin_th / rx;\r\n  const a10 = -sin_th / ry;\r\n  const a11 = cos_th / ry;\r\n  const x0 = a00 * ox + a01 * oy;\r\n  const y0 = a10 * ox + a11 * oy;\r\n  const x1 = a00 * x + a01 * y;\r\n  const y1 = a10 * x + a11 * y;\r\n\r\n  const d = (x1 - x0) * (x1 - x0) + (y1 - y0) * (y1 - y0);\r\n  let sfactor_sq = 1 / d - 0.25;\r\n  if (sfactor_sq < 0) {\r\n    sfactor_sq = 0;\r\n  }\r\n  let sfactor = Math.sqrt(sfactor_sq);\r\n  if (sweep === large) {\r\n    sfactor = -sfactor;\r\n  }\r\n\r\n  const xc = 0.5 * (x0 + x1) - sfactor * (y1 - y0);\r\n  const yc = 0.5 * (y0 + y1) + sfactor * (x1 - x0);\r\n\r\n  const th0 = Math.atan2(y0 - yc, x0 - xc);\r\n  const th1 = Math.atan2(y1 - yc, x1 - xc);\r\n\r\n  let th_arc = th1 - th0;\r\n  if (th_arc < 0 && sweep === 1) {\r\n    th_arc += 2 * Math.PI;\r\n  } else if (th_arc > 0 && sweep === 0) {\r\n    th_arc -= 2 * Math.PI;\r\n  }\r\n\r\n  const segments = Math.ceil(Math.abs(th_arc / (Math.PI * 0.5 + 0.001)));\r\n  const result = [];\r\n\r\n  for (let i = 0; i < segments; i++) {\r\n    const th2 = th0 + (i * th_arc) / segments;\r\n    const th3 = th0 + ((i + 1) * th_arc) / segments;\r\n    result[i] = [xc, yc, th2, th3, rx, ry, sin_th, cos_th];\r\n  }\r\n\r\n  return result;\r\n};\r\n\r\nconst segmentToBezier = function(cx, cy, th0, th1, rx, ry, sin_th, cos_th) {\r\n  const a00 = cos_th * rx;\r\n  const a01 = -sin_th * ry;\r\n  const a10 = sin_th * rx;\r\n  const a11 = cos_th * ry;\r\n\r\n  const th_half = 0.5 * (th1 - th0);\r\n  const t =\r\n    ((8 / 3) * Math.sin(th_half * 0.5) * Math.sin(th_half * 0.5)) /\r\n    Math.sin(th_half);\r\n  const x1 = cx + Math.cos(th0) - t * Math.sin(th0);\r\n  const y1 = cy + Math.sin(th0) + t * Math.cos(th0);\r\n  const x3 = cx + Math.cos(th1);\r\n  const y3 = cy + Math.sin(th1);\r\n  const x2 = x3 + t * Math.sin(th1);\r\n  const y2 = y3 - t * Math.cos(th1);\r\n\r\n  return [\r\n    a00 * x1 + a01 * y1,\r\n    a10 * x1 + a11 * y1,\r\n    a00 * x2 + a01 * y2,\r\n    a10 * x2 + a11 * y2,\r\n    a00 * x3 + a01 * y3,\r\n    a10 * x3 + a11 * y3\r\n  ];\r\n};\r\n\r\nclass SVGPath {\r\n  static apply(doc, path) {\r\n    const commands = parse(path);\r\n    apply(commands, doc);\r\n  }\r\n}\r\n\r\nexport default SVGPath;\r\n", "import SVGPath from '../path';\r\nimport PDFObject from '../object';\r\n\r\nconst { number } = PDFObject;\r\n\r\n// This constant is used to approximate a symmetrical arc using a cubic\r\n// Bezier curve.\r\nconst KAPPA = 4.0 * ((Math.sqrt(2) - 1.0) / 3.0);\r\nexport default {\r\n  initVector() {\r\n    this._ctm = [1, 0, 0, 1, 0, 0]; // current transformation matrix\r\n    return (this._ctmStack = []);\r\n  },\r\n\r\n  save() {\r\n    this._ctmStack.push(this._ctm.slice());\r\n    // TODO: save/restore colorspace and styles so not setting it unnessesarily all the time?\r\n    return this.addContent('q');\r\n  },\r\n\r\n  restore() {\r\n    this._ctm = this._ctmStack.pop() || [1, 0, 0, 1, 0, 0];\r\n    return this.addContent('Q');\r\n  },\r\n\r\n  closePath() {\r\n    return this.addContent('h');\r\n  },\r\n\r\n  lineWidth(w) {\r\n    return this.addContent(`${number(w)} w`);\r\n  },\r\n\r\n  _CAP_STYLES: {\r\n    BUTT: 0,\r\n    ROUND: 1,\r\n    SQUARE: 2\r\n  },\r\n\r\n  lineCap(c) {\r\n    if (typeof c === 'string') {\r\n      c = this._CAP_STYLES[c.toUpperCase()];\r\n    }\r\n    return this.addContent(`${c} J`);\r\n  },\r\n\r\n  _JOIN_STYLES: {\r\n    MITER: 0,\r\n    ROUND: 1,\r\n    BEVEL: 2\r\n  },\r\n\r\n  lineJoin(j) {\r\n    if (typeof j === 'string') {\r\n      j = this._JOIN_STYLES[j.toUpperCase()];\r\n    }\r\n    return this.addContent(`${j} j`);\r\n  },\r\n\r\n  miterLimit(m) {\r\n    return this.addContent(`${number(m)} M`);\r\n  },\r\n\r\n  dash(length, options = {}) {\r\n    const originalLength = length;\r\n    if (!Array.isArray(length)) {\r\n      length = [length, options.space || length];\r\n    }\r\n\r\n    const valid = length.every(x => Number.isFinite(x) && x > 0);\r\n    if (!valid) {\r\n      throw new Error(\r\n        `dash(${JSON.stringify(originalLength)}, ${JSON.stringify(\r\n          options\r\n        )}) invalid, lengths must be numeric and greater than zero`\r\n      );\r\n    }\r\n\r\n    length = length.map(number).join(' ');\r\n    return this.addContent(`[${length}] ${number(options.phase || 0)} d`);\r\n  },\r\n\r\n  undash() {\r\n    return this.addContent('[] 0 d');\r\n  },\r\n\r\n  moveTo(x, y) {\r\n    return this.addContent(`${number(x)} ${number(y)} m`);\r\n  },\r\n\r\n  lineTo(x, y) {\r\n    return this.addContent(`${number(x)} ${number(y)} l`);\r\n  },\r\n\r\n  bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y) {\r\n    return this.addContent(\r\n      `${number(cp1x)} ${number(cp1y)} ${number(cp2x)} ${number(cp2y)} ${number(\r\n        x\r\n      )} ${number(y)} c`\r\n    );\r\n  },\r\n\r\n  quadraticCurveTo(cpx, cpy, x, y) {\r\n    return this.addContent(\r\n      `${number(cpx)} ${number(cpy)} ${number(x)} ${number(y)} v`\r\n    );\r\n  },\r\n\r\n  rect(x, y, w, h) {\r\n    return this.addContent(\r\n      `${number(x)} ${number(y)} ${number(w)} ${number(h)} re`\r\n    );\r\n  },\r\n\r\n  roundedRect(x, y, w, h, r) {\r\n    if (r == null) {\r\n      r = 0;\r\n    }\r\n    r = Math.min(r, 0.5 * w, 0.5 * h);\r\n\r\n    // amount to inset control points from corners (see `ellipse`)\r\n    const c = r * (1.0 - KAPPA);\r\n\r\n    this.moveTo(x + r, y);\r\n    this.lineTo(x + w - r, y);\r\n    this.bezierCurveTo(x + w - c, y, x + w, y + c, x + w, y + r);\r\n    this.lineTo(x + w, y + h - r);\r\n    this.bezierCurveTo(x + w, y + h - c, x + w - c, y + h, x + w - r, y + h);\r\n    this.lineTo(x + r, y + h);\r\n    this.bezierCurveTo(x + c, y + h, x, y + h - c, x, y + h - r);\r\n    this.lineTo(x, y + r);\r\n    this.bezierCurveTo(x, y + c, x + c, y, x + r, y);\r\n    return this.closePath();\r\n  },\r\n\r\n  ellipse(x, y, r1, r2) {\r\n    // based on http://stackoverflow.com/questions/2172798/how-to-draw-an-oval-in-html5-canvas/2173084#2173084\r\n    if (r2 == null) {\r\n      r2 = r1;\r\n    }\r\n    x -= r1;\r\n    y -= r2;\r\n    const ox = r1 * KAPPA;\r\n    const oy = r2 * KAPPA;\r\n    const xe = x + r1 * 2;\r\n    const ye = y + r2 * 2;\r\n    const xm = x + r1;\r\n    const ym = y + r2;\r\n\r\n    this.moveTo(x, ym);\r\n    this.bezierCurveTo(x, ym - oy, xm - ox, y, xm, y);\r\n    this.bezierCurveTo(xm + ox, y, xe, ym - oy, xe, ym);\r\n    this.bezierCurveTo(xe, ym + oy, xm + ox, ye, xm, ye);\r\n    this.bezierCurveTo(xm - ox, ye, x, ym + oy, x, ym);\r\n    return this.closePath();\r\n  },\r\n\r\n  circle(x, y, radius) {\r\n    return this.ellipse(x, y, radius);\r\n  },\r\n\r\n  arc(x, y, radius, startAngle, endAngle, anticlockwise) {\r\n    if (anticlockwise == null) {\r\n      anticlockwise = false;\r\n    }\r\n    const TWO_PI = 2.0 * Math.PI;\r\n    const HALF_PI = 0.5 * Math.PI;\r\n\r\n    let deltaAng = endAngle - startAngle;\r\n\r\n    if (Math.abs(deltaAng) > TWO_PI) {\r\n      // draw only full circle if more than that is specified\r\n      deltaAng = TWO_PI;\r\n    } else if (deltaAng !== 0 && anticlockwise !== deltaAng < 0) {\r\n      // necessary to flip direction of rendering\r\n      const dir = anticlockwise ? -1 : 1;\r\n      deltaAng = dir * TWO_PI + deltaAng;\r\n    }\r\n\r\n    const numSegs = Math.ceil(Math.abs(deltaAng) / HALF_PI);\r\n    const segAng = deltaAng / numSegs;\r\n    const handleLen = (segAng / HALF_PI) * KAPPA * radius;\r\n    let curAng = startAngle;\r\n\r\n    // component distances between anchor point and control point\r\n    let deltaCx = -Math.sin(curAng) * handleLen;\r\n    let deltaCy = Math.cos(curAng) * handleLen;\r\n\r\n    // anchor point\r\n    let ax = x + Math.cos(curAng) * radius;\r\n    let ay = y + Math.sin(curAng) * radius;\r\n\r\n    // calculate and render segments\r\n    this.moveTo(ax, ay);\r\n\r\n    for (let segIdx = 0; segIdx < numSegs; segIdx++) {\r\n      // starting control point\r\n      const cp1x = ax + deltaCx;\r\n      const cp1y = ay + deltaCy;\r\n\r\n      // step angle\r\n      curAng += segAng;\r\n\r\n      // next anchor point\r\n      ax = x + Math.cos(curAng) * radius;\r\n      ay = y + Math.sin(curAng) * radius;\r\n\r\n      // next control point delta\r\n      deltaCx = -Math.sin(curAng) * handleLen;\r\n      deltaCy = Math.cos(curAng) * handleLen;\r\n\r\n      // ending control point\r\n      const cp2x = ax - deltaCx;\r\n      const cp2y = ay - deltaCy;\r\n\r\n      // render segment\r\n      this.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, ax, ay);\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  polygon(...points) {\r\n    this.moveTo(...(points.shift() || []));\r\n    for (let point of points) {\r\n      this.lineTo(...(point || []));\r\n    }\r\n    return this.closePath();\r\n  },\r\n\r\n  path(path) {\r\n    SVGPath.apply(this, path);\r\n    return this;\r\n  },\r\n\r\n  _windingRule(rule) {\r\n    if (/even-?odd/.test(rule)) {\r\n      return '*';\r\n    }\r\n\r\n    return '';\r\n  },\r\n\r\n  fill(color, rule) {\r\n    if (/(even-?odd)|(non-?zero)/.test(color)) {\r\n      rule = color;\r\n      color = null;\r\n    }\r\n\r\n    if (color) {\r\n      this.fillColor(color);\r\n    }\r\n    return this.addContent(`f${this._windingRule(rule)}`);\r\n  },\r\n\r\n  stroke(color) {\r\n    if (color) {\r\n      this.strokeColor(color);\r\n    }\r\n    return this.addContent('S');\r\n  },\r\n\r\n  fillAndStroke(fillColor, strokeColor, rule) {\r\n    if (strokeColor == null) {\r\n      strokeColor = fillColor;\r\n    }\r\n    const isFillRule = /(even-?odd)|(non-?zero)/;\r\n    if (isFillRule.test(fillColor)) {\r\n      rule = fillColor;\r\n      fillColor = null;\r\n    }\r\n\r\n    if (isFillRule.test(strokeColor)) {\r\n      rule = strokeColor;\r\n      strokeColor = fillColor;\r\n    }\r\n\r\n    if (fillColor) {\r\n      this.fillColor(fillColor);\r\n      this.strokeColor(strokeColor);\r\n    }\r\n\r\n    return this.addContent(`B${this._windingRule(rule)}`);\r\n  },\r\n\r\n  clip(rule) {\r\n    return this.addContent(`W${this._windingRule(rule)} n`);\r\n  },\r\n\r\n  transform(m11, m12, m21, m22, dx, dy) {\r\n    // keep track of the current transformation matrix\r\n    if (m11 === 1 && m12 === 0 && m21 === 0 && m22 === 1 && dx === 0 && dy === 0) {\r\n      // Ignore identity transforms\r\n      return this;\r\n    }\r\n    const m = this._ctm;\r\n    const [m0, m1, m2, m3, m4, m5] = m;\r\n    m[0] = m0 * m11 + m2 * m12;\r\n    m[1] = m1 * m11 + m3 * m12;\r\n    m[2] = m0 * m21 + m2 * m22;\r\n    m[3] = m1 * m21 + m3 * m22;\r\n    m[4] = m0 * dx + m2 * dy + m4;\r\n    m[5] = m1 * dx + m3 * dy + m5;\r\n\r\n    const values = [m11, m12, m21, m22, dx, dy].map(v => number(v)).join(' ');\r\n    return this.addContent(`${values} cm`);\r\n  },\r\n\r\n  translate(x, y) {\r\n    return this.transform(1, 0, 0, 1, x, y);\r\n  },\r\n\r\n  rotate(angle, options = {}) {\r\n    let y;\r\n    const rad = (angle * Math.PI) / 180;\r\n    const cos = Math.cos(rad);\r\n    const sin = Math.sin(rad);\r\n    let x = (y = 0);\r\n\r\n    if (options.origin != null) {\r\n      [x, y] = options.origin;\r\n      const x1 = x * cos - y * sin;\r\n      const y1 = x * sin + y * cos;\r\n      x -= x1;\r\n      y -= y1;\r\n    }\r\n\r\n    return this.transform(cos, sin, -sin, cos, x, y);\r\n  },\r\n\r\n  scale(xFactor, yFactor, options = {}) {\r\n    let y;\r\n    if (yFactor == null) {\r\n      yFactor = xFactor;\r\n    }\r\n    if (typeof yFactor === 'object') {\r\n      options = yFactor;\r\n      yFactor = xFactor;\r\n    }\r\n\r\n    let x = (y = 0);\r\n    if (options.origin != null) {\r\n      [x, y] = options.origin;\r\n      x -= xFactor * x;\r\n      y -= yFactor * y;\r\n    }\r\n\r\n    return this.transform(xFactor, 0, 0, yFactor, x, y);\r\n  }\r\n};\r\n", "import fs from 'fs';\r\n\r\nconst WIN_ANSI_MAP = {\r\n  402: 131,\r\n  8211: 150,\r\n  8212: 151,\r\n  8216: 145,\r\n  8217: 146,\r\n  8218: 130,\r\n  8220: 147,\r\n  8221: 148,\r\n  8222: 132,\r\n  8224: 134,\r\n  8225: 135,\r\n  8226: 149,\r\n  8230: 133,\r\n  8364: 128,\r\n  8240: 137,\r\n  8249: 139,\r\n  8250: 155,\r\n  710: 136,\r\n  8482: 153,\r\n  338: 140,\r\n  339: 156,\r\n  732: 152,\r\n  352: 138,\r\n  353: 154,\r\n  376: 159,\r\n  381: 142,\r\n  382: 158\r\n};\r\n\r\nconst characters = `\\\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n  \r\nspace         exclam         quotedbl       numbersign\r\ndollar        percent        ampersand      quotesingle\r\nparenleft     parenright     asterisk       plus\r\ncomma         hyphen         period         slash\r\nzero          one            two            three\r\nfour          five           six            seven\r\neight         nine           colon          semicolon\r\nless          equal          greater        question\r\n  \r\nat            A              B              C\r\nD             E              F              G\r\nH             I              J              K\r\nL             M              N              O\r\nP             Q              R              S\r\nT             U              V              W\r\nX             Y              Z              bracketleft\r\nbackslash     bracketright   asciicircum    underscore\r\n  \r\ngrave         a              b              c\r\nd             e              f              g\r\nh             i              j              k\r\nl             m              n              o\r\np             q              r              s\r\nt             u              v              w\r\nx             y              z              braceleft\r\nbar           braceright     asciitilde     .notdef\r\n  \r\nEuro          .notdef        quotesinglbase florin\r\nquotedblbase  ellipsis       dagger         daggerdbl\r\ncircumflex    perthousand    Scaron         guilsinglleft\r\nOE            .notdef        Zcaron         .notdef\r\n.notdef       quoteleft      quoteright     quotedblleft\r\nquotedblright bullet         endash         emdash\r\ntilde         trademark      scaron         guilsinglright\r\noe            .notdef        zcaron         ydieresis\r\n  \r\nspace         exclamdown     cent           sterling\r\ncurrency      yen            brokenbar      section\r\ndieresis      copyright      ordfeminine    guillemotleft\r\nlogicalnot    hyphen         registered     macron\r\ndegree        plusminus      twosuperior    threesuperior\r\nacute         mu             paragraph      periodcentered\r\ncedilla       onesuperior    ordmasculine   guillemotright\r\nonequarter    onehalf        threequarters  questiondown\r\n  \r\nAgrave        Aacute         Acircumflex    Atilde\r\nAdieresis     Aring          AE             Ccedilla\r\nEgrave        Eacute         Ecircumflex    Edieresis\r\nIgrave        Iacute         Icircumflex    Idieresis\r\nEth           Ntilde         Ograve         Oacute\r\nOcircumflex   Otilde         Odieresis      multiply\r\nOslash        Ugrave         Uacute         Ucircumflex\r\nUdieresis     Yacute         Thorn          germandbls\r\n  \r\nagrave        aacute         acircumflex    atilde\r\nadieresis     aring          ae             ccedilla\r\negrave        eacute         ecircumflex    edieresis\r\nigrave        iacute         icircumflex    idieresis\r\neth           ntilde         ograve         oacute\r\nocircumflex   otilde         odieresis      divide\r\noslash        ugrave         uacute         ucircumflex\r\nudieresis     yacute         thorn          ydieresis\\\r\n`.split(/\\s+/);\r\n\r\nclass AFMFont {\r\n  static open(filename) {\r\n    return new AFMFont(fs.readFileSync(filename, 'utf8'));\r\n  }\r\n\r\n  constructor(contents) {\r\n    this.contents = contents;\r\n    this.attributes = {};\r\n    this.glyphWidths = {};\r\n    this.boundingBoxes = {};\r\n    this.kernPairs = {};\r\n\r\n    this.parse();\r\n    // todo: remove charWidths since appears to not be used\r\n    this.charWidths = new Array(256);\r\n    for (let char = 0; char <= 255; char++) {\r\n      this.charWidths[char] = this.glyphWidths[characters[char]];\r\n    }\r\n\r\n    this.bbox = this.attributes['FontBBox'].split(/\\s+/).map(e => +e);\r\n    this.ascender = +(this.attributes['Ascender'] || 0);\r\n    this.descender = +(this.attributes['Descender'] || 0);\r\n    this.xHeight = +(this.attributes['XHeight'] || 0);\r\n    this.capHeight = +(this.attributes['CapHeight'] || 0);\r\n    this.lineGap =\r\n      this.bbox[3] - this.bbox[1] - (this.ascender - this.descender);\r\n  }\r\n\r\n  parse() {\r\n    let section = '';\r\n    for (let line of this.contents.split('\\n')) {\r\n      var match;\r\n      var a;\r\n      if ((match = line.match(/^Start(\\w+)/))) {\r\n        section = match[1];\r\n        continue;\r\n      } else if ((match = line.match(/^End(\\w+)/))) {\r\n        section = '';\r\n        continue;\r\n      }\r\n\r\n      switch (section) {\r\n        case 'FontMetrics':\r\n          match = line.match(/(^\\w+)\\s+(.*)/);\r\n          var key = match[1];\r\n          var value = match[2];\r\n\r\n          if ((a = this.attributes[key])) {\r\n            if (!Array.isArray(a)) {\r\n              a = this.attributes[key] = [a];\r\n            }\r\n            a.push(value);\r\n          } else {\r\n            this.attributes[key] = value;\r\n          }\r\n          break;\r\n\r\n        case 'CharMetrics':\r\n          if (!/^CH?\\s/.test(line)) {\r\n            continue;\r\n          }\r\n          var name = line.match(/\\bN\\s+(\\.?\\w+)\\s*;/)[1];\r\n          this.glyphWidths[name] = +line.match(/\\bWX\\s+(\\d+)\\s*;/)[1];\r\n          break;\r\n\r\n        case 'KernPairs':\r\n          match = line.match(/^KPX\\s+(\\.?\\w+)\\s+(\\.?\\w+)\\s+(-?\\d+)/);\r\n          if (match) {\r\n            this.kernPairs[match[1] + '\\0' + match[2]] = parseInt(match[3]);\r\n          }\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  encodeText(text) {\r\n    const res = [];\r\n    for (let i = 0, len = text.length; i < len; i++) {\r\n      let char = text.charCodeAt(i);\r\n      char = WIN_ANSI_MAP[char] || char;\r\n      res.push(char.toString(16));\r\n    }\r\n\r\n    return res;\r\n  }\r\n\r\n  glyphsForString(string) {\r\n    const glyphs = [];\r\n\r\n    for (let i = 0, len = string.length; i < len; i++) {\r\n      const charCode = string.charCodeAt(i);\r\n      glyphs.push(this.characterToGlyph(charCode));\r\n    }\r\n\r\n    return glyphs;\r\n  }\r\n\r\n  characterToGlyph(character) {\r\n    return characters[WIN_ANSI_MAP[character] || character] || '.notdef';\r\n  }\r\n\r\n  widthOfGlyph(glyph) {\r\n    return this.glyphWidths[glyph] || 0;\r\n  }\r\n\r\n  getKernPair(left, right) {\r\n    return this.kernPairs[left + '\\0' + right] || 0;\r\n  }\r\n\r\n  advancesForGlyphs(glyphs) {\r\n    const advances = [];\r\n\r\n    for (let index = 0; index < glyphs.length; index++) {\r\n      const left = glyphs[index];\r\n      const right = glyphs[index + 1];\r\n      advances.push(this.widthOfGlyph(left) + this.getKernPair(left, right));\r\n    }\r\n\r\n    return advances;\r\n  }\r\n}\r\n\r\nexport default AFMFont;\r\n", "class PDFFont {\r\n  constructor() {}\r\n\r\n  encode() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  widthOfString() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  ref() {\r\n    return this.dictionary != null\r\n      ? this.dictionary\r\n      : (this.dictionary = this.document.ref());\r\n  }\r\n\r\n  finalize() {\r\n    if (this.embedded || this.dictionary == null) {\r\n      return;\r\n    }\r\n\r\n    this.embed();\r\n    return (this.embedded = true);\r\n  }\r\n\r\n  embed() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  lineHeight(size, includeGap) {\r\n    if (includeGap == null) {\r\n      includeGap = false;\r\n    }\r\n    const gap = includeGap ? this.lineGap : 0;\r\n    return ((this.ascender + gap - this.descender) / 1000) * size;\r\n  }\r\n}\r\n\r\nexport default PDFFont;\r\n", "import AFMFont from './afm';\r\nimport PDFFont from '../font';\r\nimport fs from 'fs';\r\n\r\n// This insanity is so bundlers can inline the font files\r\nconst STANDARD_FONTS = {\r\n  Courier() {\r\n    return fs.readFileSync(__dirname + '/data/Courier.afm', 'utf8');\r\n  },\r\n  'Courier-Bold'() {\r\n    return fs.readFileSync(__dirname + '/data/Courier-Bold.afm', 'utf8');\r\n  },\r\n  'Courier-Oblique'() {\r\n    return fs.readFileSync(__dirname + '/data/Courier-Oblique.afm', 'utf8');\r\n  },\r\n  'Courier-BoldOblique'() {\r\n    return fs.readFileSync(__dirname + '/data/Courier-BoldOblique.afm', 'utf8');\r\n  },\r\n  Helvetica() {\r\n    return fs.readFileSync(__dirname + '/data/Helvetica.afm', 'utf8');\r\n  },\r\n  'Helvetica-Bold'() {\r\n    return fs.readFileSync(__dirname + '/data/Helvetica-Bold.afm', 'utf8');\r\n  },\r\n  'Helvetica-Oblique'() {\r\n    return fs.readFileSync(__dirname + '/data/Helvetica-Oblique.afm', 'utf8');\r\n  },\r\n  'Helvetica-BoldOblique'() {\r\n    return fs.readFileSync(\r\n      __dirname + '/data/Helvetica-BoldOblique.afm',\r\n      'utf8'\r\n    );\r\n  },\r\n  'Times-Roman'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-Roman.afm', 'utf8');\r\n  },\r\n  'Times-Bold'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-Bold.afm', 'utf8');\r\n  },\r\n  'Times-Italic'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-Italic.afm', 'utf8');\r\n  },\r\n  'Times-BoldItalic'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-BoldItalic.afm', 'utf8');\r\n  },\r\n  Symbol() {\r\n    return fs.readFileSync(__dirname + '/data/Symbol.afm', 'utf8');\r\n  },\r\n  ZapfDingbats() {\r\n    return fs.readFileSync(__dirname + '/data/ZapfDingbats.afm', 'utf8');\r\n  }\r\n};\r\n\r\nclass StandardFont extends PDFFont {\r\n  constructor(document, name, id) {\r\n    super();\r\n    this.document = document;\r\n    this.name = name;\r\n    this.id = id;\r\n    this.font = new AFMFont(STANDARD_FONTS[this.name]());\r\n    ({\r\n      ascender: this.ascender,\r\n      descender: this.descender,\r\n      bbox: this.bbox,\r\n      lineGap: this.lineGap,\r\n      xHeight: this.xHeight,\r\n      capHeight: this.capHeight\r\n    } = this.font);\r\n  }\r\n\r\n  embed() {\r\n    this.dictionary.data = {\r\n      Type: 'Font',\r\n      BaseFont: this.name,\r\n      Subtype: 'Type1',\r\n      Encoding: 'WinAnsiEncoding'\r\n    };\r\n\r\n    return this.dictionary.end();\r\n  }\r\n\r\n  encode(text) {\r\n    const encoded = this.font.encodeText(text);\r\n    const glyphs = this.font.glyphsForString(`${text}`);\r\n    const advances = this.font.advancesForGlyphs(glyphs);\r\n    const positions = [];\r\n    for (let i = 0; i < glyphs.length; i++) {\r\n      const glyph = glyphs[i];\r\n      positions.push({\r\n        xAdvance: advances[i],\r\n        yAdvance: 0,\r\n        xOffset: 0,\r\n        yOffset: 0,\r\n        advanceWidth: this.font.widthOfGlyph(glyph)\r\n      });\r\n    }\r\n\r\n    return [encoded, positions];\r\n  }\r\n\r\n  widthOfString(string, size) {\r\n    const glyphs = this.font.glyphsForString(`${string}`);\r\n    const advances = this.font.advancesForGlyphs(glyphs);\r\n\r\n    let width = 0;\r\n    for (let advance of advances) {\r\n      width += advance;\r\n    }\r\n\r\n    const scale = size / 1000;\r\n    return width * scale;\r\n  }\r\n\r\n  static isStandardFont(name) {\r\n    return name in STANDARD_FONTS;\r\n  }\r\n}\r\n\r\nexport default StandardFont;\r\n", "import PDFFont from '../font';\r\n\r\nconst toHex = function(num) {\r\n  return `0000${num.toString(16)}`.slice(-4);\r\n};\r\n\r\nclass EmbeddedFont extends PDFFont {\r\n  constructor(document, font, id) {\r\n    super();\r\n    this.document = document;\r\n    this.font = font;\r\n    this.id = id;\r\n    this.subset = this.font.createSubset();\r\n    this.unicode = [[0]];\r\n    this.widths = [this.font.getGlyph(0).advanceWidth];\r\n\r\n    this.name = this.font.postscriptName;\r\n    this.scale = 1000 / this.font.unitsPerEm;\r\n    this.ascender = this.font.ascent * this.scale;\r\n    this.descender = this.font.descent * this.scale;\r\n    this.xHeight = this.font.xHeight * this.scale;\r\n    this.capHeight = this.font.capHeight * this.scale;\r\n    this.lineGap = this.font.lineGap * this.scale;\r\n    this.bbox = this.font.bbox;\r\n\r\n    if (document.options.fontLayoutCache !== false) {\r\n      this.layoutCache = Object.create(null);\r\n    }\r\n  }\r\n\r\n  layoutRun(text, features) {\r\n    const run = this.font.layout(text, features);\r\n\r\n    // Normalize position values\r\n    for (let i = 0; i < run.positions.length; i++) {\r\n      const position = run.positions[i];\r\n      for (let key in position) {\r\n        position[key] *= this.scale;\r\n      }\r\n\r\n      position.advanceWidth = run.glyphs[i].advanceWidth * this.scale;\r\n    }\r\n\r\n    return run;\r\n  }\r\n\r\n  layoutCached(text) {\r\n    if (!this.layoutCache) {\r\n      return this.layoutRun(text);\r\n    }\r\n    let cached;\r\n    if ((cached = this.layoutCache[text])) {\r\n      return cached;\r\n    }\r\n\r\n    const run = this.layoutRun(text);\r\n    this.layoutCache[text] = run;\r\n    return run;\r\n  }\r\n\r\n  layout(text, features, onlyWidth) {\r\n    // Skip the cache if any user defined features are applied\r\n    if (features) {\r\n      return this.layoutRun(text, features);\r\n    }\r\n\r\n    let glyphs = onlyWidth ? null : [];\r\n    let positions = onlyWidth ? null : [];\r\n    let advanceWidth = 0;\r\n\r\n    // Split the string by words to increase cache efficiency.\r\n    // For this purpose, spaces and tabs are a good enough delimeter.\r\n    let last = 0;\r\n    let index = 0;\r\n    while (index <= text.length) {\r\n      var needle;\r\n      if (\r\n        (index === text.length && last < index) ||\r\n        ((needle = text.charAt(index)), [' ', '\\t'].includes(needle))\r\n      ) {\r\n        const run = this.layoutCached(text.slice(last, ++index));\r\n        if (!onlyWidth) {\r\n          glyphs = glyphs.concat(run.glyphs);\r\n          positions = positions.concat(run.positions);\r\n        }\r\n\r\n        advanceWidth += run.advanceWidth;\r\n        last = index;\r\n      } else {\r\n        index++;\r\n      }\r\n    }\r\n\r\n    return { glyphs, positions, advanceWidth };\r\n  }\r\n\r\n  encode(text, features) {\r\n    const { glyphs, positions } = this.layout(text, features);\r\n\r\n    const res = [];\r\n    for (let i = 0; i < glyphs.length; i++) {\r\n      const glyph = glyphs[i];\r\n      const gid = this.subset.includeGlyph(glyph.id);\r\n      res.push(`0000${gid.toString(16)}`.slice(-4));\r\n\r\n      if (this.widths[gid] == null) {\r\n        this.widths[gid] = glyph.advanceWidth * this.scale;\r\n      }\r\n      if (this.unicode[gid] == null) {\r\n        this.unicode[gid] = glyph.codePoints;\r\n      }\r\n    }\r\n\r\n    return [res, positions];\r\n  }\r\n\r\n  widthOfString(string, size, features) {\r\n    const width = this.layout(string, features, true).advanceWidth;\r\n    const scale = size / 1000;\r\n    return width * scale;\r\n  }\r\n\r\n  embed() {\r\n    const isCFF = this.subset.cff != null;\r\n    const fontFile = this.document.ref();\r\n\r\n    if (isCFF) {\r\n      fontFile.data.Subtype = 'CIDFontType0C';\r\n    }\r\n\r\n    this.subset\r\n      .encodeStream()\r\n      .on('data', data => fontFile.write(data))\r\n      .on('end', () => fontFile.end());\r\n\r\n    const familyClass =\r\n      ((this.font['OS/2'] != null\r\n        ? this.font['OS/2'].sFamilyClass\r\n        : undefined) || 0) >> 8;\r\n    let flags = 0;\r\n    if (this.font.post.isFixedPitch) {\r\n      flags |= 1 << 0;\r\n    }\r\n    if (1 <= familyClass && familyClass <= 7) {\r\n      flags |= 1 << 1;\r\n    }\r\n    flags |= 1 << 2; // assume the font uses non-latin characters\r\n    if (familyClass === 10) {\r\n      flags |= 1 << 3;\r\n    }\r\n    if (this.font.head.macStyle.italic) {\r\n      flags |= 1 << 6;\r\n    }\r\n\r\n    // generate a tag (6 uppercase letters. 17 is the char code offset from '0' to 'A'. 73 will map to 'Z')\r\n    const tag = [1, 2, 3, 4, 5, 6]\r\n      .map(i => String.fromCharCode((this.id.charCodeAt(i) || 73) + 17))\r\n      .join('');\r\n    const name = tag + '+' + this.font.postscriptName;\r\n\r\n    const { bbox } = this.font;\r\n    const descriptor = this.document.ref({\r\n      Type: 'FontDescriptor',\r\n      FontName: name,\r\n      Flags: flags,\r\n      FontBBox: [\r\n        bbox.minX * this.scale,\r\n        bbox.minY * this.scale,\r\n        bbox.maxX * this.scale,\r\n        bbox.maxY * this.scale\r\n      ],\r\n      ItalicAngle: this.font.italicAngle,\r\n      Ascent: this.ascender,\r\n      Descent: this.descender,\r\n      CapHeight: (this.font.capHeight || this.font.ascent) * this.scale,\r\n      XHeight: (this.font.xHeight || 0) * this.scale,\r\n      StemV: 0\r\n    }); // not sure how to calculate this\r\n\r\n    if (isCFF) {\r\n      descriptor.data.FontFile3 = fontFile;\r\n    } else {\r\n      descriptor.data.FontFile2 = fontFile;\r\n    }\r\n\r\n    if (this.document.subset) {\r\n      const CIDSet = Buffer.from('FFFFFFFFC0', 'hex');\r\n      const CIDSetRef = this.document.ref();\r\n      CIDSetRef.write(CIDSet);\r\n      CIDSetRef.end();\r\n\r\n      descriptor.data.CIDSet = CIDSetRef;\r\n    }\r\n\r\n    descriptor.end();\r\n\r\n    const descendantFontData = {\r\n      Type: 'Font',\r\n      Subtype: 'CIDFontType0',\r\n      BaseFont: name,\r\n      CIDSystemInfo: {\r\n        Registry: new String('Adobe'),\r\n        Ordering: new String('Identity'),\r\n        Supplement: 0\r\n      },\r\n      FontDescriptor: descriptor,\r\n      W: [0, this.widths]\r\n    };\r\n\r\n    if (!isCFF) {\r\n      descendantFontData.Subtype = 'CIDFontType2';\r\n      descendantFontData.CIDToGIDMap = 'Identity';\r\n    }\r\n\r\n    const descendantFont = this.document.ref(descendantFontData);\r\n\r\n    descendantFont.end();\r\n\r\n    this.dictionary.data = {\r\n      Type: 'Font',\r\n      Subtype: 'Type0',\r\n      BaseFont: name,\r\n      Encoding: 'Identity-H',\r\n      DescendantFonts: [descendantFont],\r\n      ToUnicode: this.toUnicodeCmap()\r\n    };\r\n\r\n    return this.dictionary.end();\r\n  }\r\n\r\n  // Maps the glyph ids encoded in the PDF back to unicode strings\r\n  // Because of ligature substitutions and the like, there may be one or more\r\n  // unicode characters represented by each glyph.\r\n  toUnicodeCmap() {\r\n    const cmap = this.document.ref();\r\n\r\n    const entries = [];\r\n    for (let codePoints of this.unicode) {\r\n      const encoded = [];\r\n\r\n      // encode codePoints to utf16\r\n      for (let value of codePoints) {\r\n        if (value > 0xffff) {\r\n          value -= 0x10000;\r\n          encoded.push(toHex(((value >>> 10) & 0x3ff) | 0xd800));\r\n          value = 0xdc00 | (value & 0x3ff);\r\n        }\r\n\r\n        encoded.push(toHex(value));\r\n      }\r\n\r\n      entries.push(`<${encoded.join(' ')}>`);\r\n    }\r\n\r\n    const chunkSize = 256;\r\n    const chunks = Math.ceil(entries.length / chunkSize);\r\n    const ranges = [];\r\n    for (let i = 0; i < chunks; i++) {\r\n      const start = i * chunkSize;\r\n      const end = Math.min((i + 1) * chunkSize, entries.length);\r\n      ranges.push(`<${toHex(start)}> <${toHex(end - 1)}> [${entries.slice(start, end).join(' ')}]`);\r\n    }\r\n\r\n    cmap.end(`\\\r\n/CIDInit /ProcSet findresource begin\r\n12 dict begin\r\nbegincmap\r\n/CIDSystemInfo <<\r\n  /Registry (Adobe)\r\n  /Ordering (UCS)\r\n  /Supplement 0\r\n>> def\r\n/CMapName /Adobe-Identity-UCS def\r\n/CMapType 2 def\r\n1 begincodespacerange\r\n<0000><ffff>\r\nendcodespacerange\r\n1 beginbfrange\r\n${ranges.join('\\n')}\r\nendbfrange\r\nendcmap\r\nCMapName currentdict /CMap defineresource pop\r\nend\r\nend\\\r\n`);\r\n\r\n    return cmap;\r\n  }\r\n}\r\n\r\nexport default EmbeddedFont;\r\n", "import fs from 'fs';\r\nimport fontkit from 'fontkit';\r\nimport StandardFont from './font/standard';\r\nimport EmbeddedFont from './font/embedded';\r\n\r\nclass PDFFontFactory {\r\n  static open(document, src, family, id) {\r\n    let font;\r\n    if (typeof src === 'string') {\r\n      if (StandardFont.isStandardFont(src)) {\r\n        return new StandardFont(document, src, id);\r\n      }\r\n\r\n      src = fs.readFileSync(src);\r\n    }\r\n    if (Buffer.isBuffer(src)) {\r\n      font = fontkit.create(src, family);\r\n    } else if (src instanceof Uint8Array) {\r\n      font = fontkit.create(Buffer.from(src), family);\r\n    } else if (src instanceof ArrayBuffer) {\r\n      font = fontkit.create(Buffer.from(new Uint8Array(src)), family);\r\n    }\r\n\r\n    if (font == null) {\r\n      throw new Error('Not a supported font format or standard PDF font.');\r\n    }\r\n\r\n    return new EmbeddedFont(document, font, id);\r\n  }\r\n}\r\n\r\nexport default PDFFontFactory;\r\n", "import PDFFontFactory from '../font_factory';\r\n\r\nexport default {\r\n  initFonts(defaultFont = 'Helvetica') {\r\n    // Lookup table for embedded fonts\r\n    this._fontFamilies = {};\r\n    this._fontCount = 0;\r\n\r\n    // Font state\r\n    this._fontSize = 12;\r\n    this._font = null;\r\n\r\n    this._registeredFonts = {};\r\n\r\n    // Set the default font\r\n    if (defaultFont) {\r\n      this.font(defaultFont);\r\n    }\r\n  },\r\n\r\n  font(src, family, size) {\r\n    let cacheKey, font;\r\n    if (typeof family === 'number') {\r\n      size = family;\r\n      family = null;\r\n    }\r\n\r\n    // check registered fonts if src is a string\r\n    if (typeof src === 'string' && this._registeredFonts[src]) {\r\n      cacheKey = src;\r\n      ({ src, family } = this._registeredFonts[src]);\r\n    } else {\r\n      cacheKey = family || src;\r\n      if (typeof cacheKey !== 'string') {\r\n        cacheKey = null;\r\n      }\r\n    }\r\n\r\n    if (size != null) {\r\n      this.fontSize(size);\r\n    }\r\n\r\n    // fast path: check if the font is already in the PDF\r\n    if ((font = this._fontFamilies[cacheKey])) {\r\n      this._font = font;\r\n      return this;\r\n    }\r\n\r\n    // load the font\r\n    const id = `F${++this._fontCount}`;\r\n    this._font = PDFFontFactory.open(this, src, family, id);\r\n\r\n    // check for existing font familes with the same name already in the PDF\r\n    // useful if the font was passed as a buffer\r\n    if ((font = this._fontFamilies[this._font.name])) {\r\n      this._font = font;\r\n      return this;\r\n    }\r\n\r\n    // save the font for reuse later\r\n    if (cacheKey) {\r\n      this._fontFamilies[cacheKey] = this._font;\r\n    }\r\n\r\n    if (this._font.name) {\r\n      this._fontFamilies[this._font.name] = this._font;\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  fontSize(_fontSize) {\r\n    this._fontSize = _fontSize;\r\n    return this;\r\n  },\r\n\r\n  currentLineHeight(includeGap) {\r\n    if (includeGap == null) {\r\n      includeGap = false;\r\n    }\r\n    return this._font.lineHeight(this._fontSize, includeGap);\r\n  },\r\n\r\n  registerFont(name, src, family) {\r\n    this._registeredFonts[name] = {\r\n      src,\r\n      family\r\n    };\r\n\r\n    return this;\r\n  }\r\n};\r\n", "import { EventEmitter } from 'events';\r\nimport LineBreaker from 'linebreak';\r\n\r\nconst SOFT_HYPHEN = '\\u00AD';\r\nconst HYPHEN = '-';\r\n\r\nclass LineWrapper extends EventEmitter {\r\n  constructor(document, options) {\r\n    super();\r\n    this.document = document;\r\n    this.indent = options.indent || 0;\r\n    this.characterSpacing = options.characterSpacing || 0;\r\n    this.wordSpacing = options.wordSpacing === 0;\r\n    this.columns = options.columns || 1;\r\n    this.columnGap = options.columnGap != null ? options.columnGap : 18; // 1/4 inch\r\n    this.lineWidth =\r\n      (options.width - this.columnGap * (this.columns - 1)) / this.columns;\r\n    this.spaceLeft = this.lineWidth;\r\n    this.startX = this.document.x;\r\n    this.startY = this.document.y;\r\n    this.column = 1;\r\n    this.ellipsis = options.ellipsis;\r\n    this.continuedX = 0;\r\n    this.features = options.features;\r\n\r\n    // calculate the maximum Y position the text can appear at\r\n    if (options.height != null) {\r\n      this.height = options.height;\r\n      this.maxY = this.startY + options.height;\r\n    } else {\r\n      this.maxY = this.document.page.maxY();\r\n    }\r\n\r\n    // handle paragraph indents\r\n    this.on('firstLine', options => {\r\n      // if this is the first line of the text segment, and\r\n      // we're continuing where we left off, indent that much\r\n      // otherwise use the user specified indent option\r\n      const indent = this.continuedX || this.indent;\r\n      this.document.x += indent;\r\n      this.lineWidth -= indent;\r\n\r\n      return this.once('line', () => {\r\n        this.document.x -= indent;\r\n        this.lineWidth += indent;\r\n        if (options.continued && !this.continuedX) {\r\n          this.continuedX = this.indent;\r\n        }\r\n        if (!options.continued) {\r\n          return (this.continuedX = 0);\r\n        }\r\n      });\r\n    });\r\n\r\n    // handle left aligning last lines of paragraphs\r\n    this.on('lastLine', options => {\r\n      const { align } = options;\r\n      if (align === 'justify') {\r\n        options.align = 'left';\r\n      }\r\n      this.lastLine = true;\r\n\r\n      return this.once('line', () => {\r\n        this.document.y += options.paragraphGap || 0;\r\n        options.align = align;\r\n        return (this.lastLine = false);\r\n      });\r\n    });\r\n  }\r\n\r\n  wordWidth(word) {\r\n    return (\r\n      this.document.widthOfString(word, this) +\r\n      this.characterSpacing +\r\n      this.wordSpacing\r\n    );\r\n  }\r\n\r\n  canFit(word, w) {\r\n    if (word[word.length - 1] != SOFT_HYPHEN) {\r\n      return w <= this.spaceLeft;\r\n    }\r\n    return w + this.wordWidth(HYPHEN) <= this.spaceLeft;\r\n  }\r\n\r\n  eachWord(text, fn) {\r\n    // setup a unicode line breaker\r\n    let bk;\r\n    const breaker = new LineBreaker(text);\r\n    let last = null;\r\n    const wordWidths = Object.create(null);\r\n\r\n    while ((bk = breaker.nextBreak())) {\r\n      var shouldContinue;\r\n      let word = text.slice(\r\n        (last != null ? last.position : undefined) || 0,\r\n        bk.position\r\n      );\r\n      let w =\r\n        wordWidths[word] != null\r\n          ? wordWidths[word]\r\n          : (wordWidths[word] = this.wordWidth(word));\r\n\r\n      // if the word is longer than the whole line, chop it up\r\n      // TODO: break by grapheme clusters, not JS string characters\r\n      if (w > this.lineWidth + this.continuedX) {\r\n        // make some fake break objects\r\n        let lbk = last;\r\n        const fbk = {};\r\n\r\n        while (word.length) {\r\n          // fit as much of the word as possible into the space we have\r\n          var l, mightGrow;\r\n          if (w > this.spaceLeft) {\r\n            // start our check at the end of our available space - this method is faster than a loop of each character and it resolves\r\n            // an issue with long loops when processing massive words, such as a huge number of spaces\r\n            l = Math.ceil(this.spaceLeft / (w / word.length));\r\n            w = this.wordWidth(word.slice(0, l));\r\n            mightGrow = w <= this.spaceLeft && l < word.length;\r\n          } else {\r\n            l = word.length;\r\n          }\r\n          let mustShrink = w > this.spaceLeft && l > 0;\r\n          // shrink or grow word as necessary after our near-guess above\r\n          while (mustShrink || mightGrow) {\r\n            if (mustShrink) {\r\n              w = this.wordWidth(word.slice(0, --l));\r\n              mustShrink = w > this.spaceLeft && l > 0;\r\n            } else {\r\n              w = this.wordWidth(word.slice(0, ++l));\r\n              mustShrink = w > this.spaceLeft && l > 0;\r\n              mightGrow = w <= this.spaceLeft && l < word.length;\r\n            }\r\n          }\r\n\r\n          // check for the edge case where a single character cannot fit into a line.\r\n          if (l === 0 && this.spaceLeft === this.lineWidth) {\r\n            l = 1;\r\n          }\r\n\r\n          // send a required break unless this is the last piece and a linebreak is not specified\r\n          fbk.required = bk.required || l < word.length;\r\n          shouldContinue = fn(word.slice(0, l), w, fbk, lbk);\r\n          lbk = { required: false };\r\n\r\n          // get the remaining piece of the word\r\n          word = word.slice(l);\r\n          w = this.wordWidth(word);\r\n\r\n          if (shouldContinue === false) {\r\n            break;\r\n          }\r\n        }\r\n      } else {\r\n        // otherwise just emit the break as it was given to us\r\n        shouldContinue = fn(word, w, bk, last);\r\n      }\r\n\r\n      if (shouldContinue === false) {\r\n        break;\r\n      }\r\n      last = bk;\r\n    }\r\n  }\r\n\r\n  wrap(text, options) {\r\n    // override options from previous continued fragments\r\n    if (options.indent != null) {\r\n      this.indent = options.indent;\r\n    }\r\n    if (options.characterSpacing != null) {\r\n      this.characterSpacing = options.characterSpacing;\r\n    }\r\n    if (options.wordSpacing != null) {\r\n      this.wordSpacing = options.wordSpacing;\r\n    }\r\n    if (options.ellipsis != null) {\r\n      this.ellipsis = options.ellipsis;\r\n    }\r\n\r\n    // make sure we're actually on the page\r\n    // and that the first line of is never by\r\n    // itself at the bottom of a page (orphans)\r\n    const nextY = this.document.y + this.document.currentLineHeight(true);\r\n    if (this.document.y > this.maxY || nextY > this.maxY) {\r\n      this.nextSection();\r\n    }\r\n\r\n    let buffer = '';\r\n    let textWidth = 0;\r\n    let wc = 0;\r\n    let lc = 0;\r\n\r\n    let { y } = this.document; // used to reset Y pos if options.continued (below)\r\n    const emitLine = () => {\r\n      options.textWidth = textWidth + this.wordSpacing * (wc - 1);\r\n      options.wordCount = wc;\r\n      options.lineWidth = this.lineWidth;\r\n      ({ y } = this.document);\r\n      this.emit('line', buffer, options, this);\r\n      return lc++;\r\n    };\r\n\r\n    this.emit('sectionStart', options, this);\r\n\r\n    this.eachWord(text, (word, w, bk, last) => {\r\n      if (last == null || last.required) {\r\n        this.emit('firstLine', options, this);\r\n        this.spaceLeft = this.lineWidth;\r\n      }\r\n\r\n      if (this.canFit(word, w)) {\r\n        buffer += word;\r\n        textWidth += w;\r\n        wc++;\r\n      }\r\n\r\n      if (bk.required || !this.canFit(word, w)) {\r\n        // if the user specified a max height and an ellipsis, and is about to pass the\r\n        // max height and max columns after the next line, append the ellipsis\r\n        const lh = this.document.currentLineHeight(true);\r\n        if (\r\n          this.height != null &&\r\n          this.ellipsis &&\r\n          this.document.y + lh * 2 > this.maxY &&\r\n          this.column >= this.columns\r\n        ) {\r\n          if (this.ellipsis === true) {\r\n            this.ellipsis = '…';\r\n          } // map default ellipsis character\r\n          buffer = buffer.replace(/\\s+$/, '');\r\n          textWidth = this.wordWidth(buffer + this.ellipsis);\r\n\r\n          // remove characters from the buffer until the ellipsis fits\r\n          // to avoid infinite loop need to stop while-loop if buffer is empty string\r\n          while (buffer && textWidth > this.lineWidth) {\r\n            buffer = buffer.slice(0, -1).replace(/\\s+$/, '');\r\n            textWidth = this.wordWidth(buffer + this.ellipsis);\r\n          }\r\n          // need to add ellipsis only if there is enough space for it\r\n          if (textWidth <= this.lineWidth) {\r\n            buffer = buffer + this.ellipsis;\r\n          }\r\n\r\n          textWidth = this.wordWidth(buffer);\r\n        }\r\n\r\n        if (bk.required) {\r\n          if (w > this.spaceLeft) {\r\n            emitLine();\r\n            buffer = word;\r\n            textWidth = w;\r\n            wc = 1;\r\n          }\r\n\r\n          this.emit('lastLine', options, this);\r\n        }\r\n\r\n        // Previous entry is a soft hyphen - add visible hyphen.\r\n        if (buffer[buffer.length - 1] == SOFT_HYPHEN) {\r\n          buffer = buffer.slice(0, -1) + HYPHEN;\r\n          this.spaceLeft -= this.wordWidth(HYPHEN);\r\n        }\r\n\r\n        emitLine();\r\n\r\n        // if we've reached the edge of the page,\r\n        // continue on a new page or column\r\n        if (this.document.y + lh > this.maxY) {\r\n          const shouldContinue = this.nextSection();\r\n\r\n          // stop if we reached the maximum height\r\n          if (!shouldContinue) {\r\n            wc = 0;\r\n            buffer = '';\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // reset the space left and buffer\r\n        if (bk.required) {\r\n          this.spaceLeft = this.lineWidth;\r\n          buffer = '';\r\n          textWidth = 0;\r\n          return (wc = 0);\r\n        } else {\r\n          // reset the space left and buffer\r\n          this.spaceLeft = this.lineWidth - w;\r\n          buffer = word;\r\n          textWidth = w;\r\n          return (wc = 1);\r\n        }\r\n      } else {\r\n        return (this.spaceLeft -= w);\r\n      }\r\n    });\r\n\r\n    if (wc > 0) {\r\n      this.emit('lastLine', options, this);\r\n      emitLine();\r\n    }\r\n\r\n    this.emit('sectionEnd', options, this);\r\n\r\n    // if the wrap is set to be continued, save the X position\r\n    // to start the first line of the next segment at, and reset\r\n    // the y position\r\n    if (options.continued === true) {\r\n      if (lc > 1) {\r\n        this.continuedX = 0;\r\n      }\r\n      this.continuedX += options.textWidth || 0;\r\n      return (this.document.y = y);\r\n    } else {\r\n      return (this.document.x = this.startX);\r\n    }\r\n  }\r\n\r\n  nextSection(options) {\r\n    this.emit('sectionEnd', options, this);\r\n\r\n    if (++this.column > this.columns) {\r\n      // if a max height was specified by the user, we're done.\r\n      // otherwise, the default is to make a new page at the bottom.\r\n      if (this.height != null) {\r\n        return false;\r\n      }\r\n\r\n      this.document.continueOnNewPage();\r\n      this.column = 1;\r\n      this.startY = this.document.page.margins.top;\r\n      this.maxY = this.document.page.maxY();\r\n      this.document.x = this.startX;\r\n      if (this.document._fillColor) {\r\n        this.document.fillColor(...this.document._fillColor);\r\n      }\r\n      this.emit('pageBreak', options, this);\r\n    } else {\r\n      this.document.x += this.lineWidth + this.columnGap;\r\n      this.document.y = this.startY;\r\n      this.emit('columnBreak', options, this);\r\n    }\r\n\r\n    this.emit('sectionStart', options, this);\r\n    return true;\r\n  }\r\n}\r\n\r\nexport default LineWrapper;\r\n", "import LineWrapper from '../line_wrapper';\r\nimport PDFObject from '../object';\r\n\r\nconst { number } = PDFObject;\r\n\r\nexport default {\r\n  initText() {\r\n    this._line = this._line.bind(this);\r\n    // Current coordinates\r\n    this.x = 0;\r\n    this.y = 0;\r\n    return (this._lineGap = 0);\r\n  },\r\n\r\n  lineGap(_lineGap) {\r\n    this._lineGap = _lineGap;\r\n    return this;\r\n  },\r\n\r\n  moveDown(lines) {\r\n    if (lines == null) {\r\n      lines = 1;\r\n    }\r\n    this.y += this.currentLineHeight(true) * lines + this._lineGap;\r\n    return this;\r\n  },\r\n\r\n  moveUp(lines) {\r\n    if (lines == null) {\r\n      lines = 1;\r\n    }\r\n    this.y -= this.currentLineHeight(true) * lines + this._lineGap;\r\n    return this;\r\n  },\r\n\r\n  _text(text, x, y, options, lineCallback) {\r\n    options = this._initOptions(x, y, options);\r\n\r\n    // Convert text to a string\r\n    text = text == null ? '' : `${text}`;\r\n\r\n    // if the wordSpacing option is specified, remove multiple consecutive spaces\r\n    if (options.wordSpacing) {\r\n      text = text.replace(/\\s{2,}/g, ' ');\r\n    }\r\n\r\n    const addStructure = () => {\r\n      if (options.structParent) {\r\n        options.structParent.add(this.struct(options.structType || 'P',\r\n          [ this.markStructureContent(options.structType || 'P') ]));\r\n      }\r\n    };\r\n\r\n    // word wrapping\r\n    if (options.width) {\r\n      let wrapper = this._wrapper;\r\n      if (!wrapper) {\r\n        wrapper = new LineWrapper(this, options);\r\n        wrapper.on('line', lineCallback);\r\n        wrapper.on('firstLine', addStructure);\r\n      }\r\n\r\n      this._wrapper = options.continued ? wrapper : null;\r\n      this._textOptions = options.continued ? options : null;\r\n      wrapper.wrap(text, options);\r\n\r\n      // render paragraphs as single lines\r\n    } else {\r\n      for (let line of text.split('\\n')) {\r\n        addStructure();\r\n        lineCallback(line, options);\r\n      }\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  text(text, x, y, options) {\r\n    return this._text(text, x, y, options, this._line);\r\n  },\r\n\r\n  widthOfString(string, options = {}) {\r\n    return (\r\n      this._font.widthOfString(string, this._fontSize, options.features) +\r\n      (options.characterSpacing || 0) * (string.length - 1)\r\n    );\r\n  },\r\n\r\n  heightOfString(text, options) {\r\n    const { x, y } = this;\r\n\r\n    options = this._initOptions(options);\r\n    options.height = Infinity; // don't break pages\r\n\r\n    const lineGap = options.lineGap || this._lineGap || 0;\r\n    this._text(text, this.x, this.y, options, () => {\r\n      return (this.y += this.currentLineHeight(true) + lineGap);\r\n    });\r\n\r\n    const height = this.y - y;\r\n    this.x = x;\r\n    this.y = y;\r\n\r\n    return height;\r\n  },\r\n\r\n  list(list, x, y, options, wrapper) {\r\n    options = this._initOptions(x, y, options);\r\n\r\n    const listType = options.listType || 'bullet';\r\n    const unit = Math.round((this._font.ascender / 1000) * this._fontSize);\r\n    const midLine = unit / 2;\r\n    const r = options.bulletRadius || unit / 3;\r\n    const indent =\r\n      options.textIndent || (listType === 'bullet' ? r * 5 : unit * 2);\r\n    const itemIndent =\r\n      options.bulletIndent || (listType === 'bullet' ? r * 8 : unit * 2);\r\n\r\n    let level = 1;\r\n    const items = [];\r\n    const levels = [];\r\n    const numbers = [];\r\n\r\n    var flatten = function(list) {\r\n      let n = 1;\r\n      for (let i = 0; i < list.length; i++) {\r\n        const item = list[i];\r\n        if (Array.isArray(item)) {\r\n          level++;\r\n          flatten(item);\r\n          level--;\r\n        } else {\r\n          items.push(item);\r\n          levels.push(level);\r\n          if (listType !== 'bullet') {\r\n            numbers.push(n++);\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    flatten(list);\r\n\r\n    const label = function(n) {\r\n      switch (listType) {\r\n        case 'numbered':\r\n          return `${n}.`;\r\n        case 'lettered':\r\n          var letter = String.fromCharCode(((n - 1) % 26) + 65);\r\n          var times = Math.floor((n - 1) / 26 + 1);\r\n          var text = Array(times + 1).join(letter);\r\n          return `${text}.`;\r\n      }\r\n    };\r\n\r\n    const drawListItem = function(listItem) {\r\n      wrapper = new LineWrapper(this, options);\r\n      wrapper.on('line', this._line);\r\n\r\n      level = 1;\r\n      let i = 0;\r\n      wrapper.once('firstLine', () => {\r\n        let item, itemType, labelType, bodyType;\r\n        if (options.structParent) {\r\n          if (options.structTypes) {\r\n            [itemType, labelType, bodyType] = options.structTypes;\r\n          } else {\r\n            [itemType, labelType, bodyType] = ['LI', 'Lbl', 'LBody'];\r\n          }\r\n        }\r\n\r\n        if (itemType) {\r\n          item = this.struct(itemType);\r\n          options.structParent.add(item);\r\n        } else if (options.structParent) {\r\n          item = options.structParent;\r\n        }\r\n\r\n        let l;\r\n        if ((l = levels[i++]) !== level) {\r\n          const diff = itemIndent * (l - level);\r\n          this.x += diff;\r\n          wrapper.lineWidth -= diff;\r\n          level = l;\r\n        }\r\n\r\n        if (item && (labelType || bodyType)) {\r\n          item.add(this.struct(labelType || bodyType,\r\n            [this.markStructureContent(labelType || bodyType)]));\r\n        }\r\n        switch (listType) {\r\n          case 'bullet':\r\n            this.circle(this.x - indent + r, this.y + midLine, r);\r\n            this.fill();\r\n            break;\r\n          case 'numbered':\r\n          case 'lettered':\r\n            var text = label(numbers[i - 1]);\r\n            this._fragment(text, this.x - indent, this.y, options);\r\n            break;\r\n        }\r\n\r\n        if (item && labelType && bodyType) {\r\n          item.add(this.struct(bodyType, [this.markStructureContent(bodyType)]));\r\n        }\r\n        if (item && item !== options.structParent) {\r\n          item.end();\r\n        }\r\n      });\r\n\r\n      wrapper.on('sectionStart', () => {\r\n        const pos = indent + itemIndent * (level - 1);\r\n        this.x += pos;\r\n        return (wrapper.lineWidth -= pos);\r\n      });\r\n\r\n      wrapper.on('sectionEnd', () => {\r\n        const pos = indent + itemIndent * (level - 1);\r\n        this.x -= pos;\r\n        return (wrapper.lineWidth += pos);\r\n      });\r\n\r\n      wrapper.wrap(listItem, options);\r\n    };\r\n\r\n\r\n    for (let i = 0; i < items.length; i++) {\r\n      drawListItem.call(this, items[i]);\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  _initOptions(x = {}, y, options = {}) {\r\n    if (typeof x === 'object') {\r\n      options = x;\r\n      x = null;\r\n    }\r\n\r\n    // clone options object\r\n    const result = Object.assign({}, options);\r\n\r\n    // extend options with previous values for continued text\r\n    if (this._textOptions) {\r\n      for (let key in this._textOptions) {\r\n        const val = this._textOptions[key];\r\n        if (key !== 'continued') {\r\n          if (result[key] === undefined) {\r\n            result[key] = val;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // Update the current position\r\n    if (x != null) {\r\n      this.x = x;\r\n    }\r\n    if (y != null) {\r\n      this.y = y;\r\n    }\r\n\r\n    // wrap to margins if no x or y position passed\r\n    if (result.lineBreak !== false) {\r\n      if (result.width == null) {\r\n        result.width = this.page.width - this.x - this.page.margins.right;\r\n      }\r\n      result.width = Math.max(result.width, 0);\r\n    }\r\n\r\n    if (!result.columns) {\r\n      result.columns = 0;\r\n    }\r\n    if (result.columnGap == null) {\r\n      result.columnGap = 18;\r\n    } // 1/4 inch\r\n\r\n    return result;\r\n  },\r\n\r\n  _line(text, options = {}, wrapper) {\r\n    this._fragment(text, this.x, this.y, options);\r\n    const lineGap = options.lineGap || this._lineGap || 0;\r\n\r\n    if (!wrapper) {\r\n      return (this.x += this.widthOfString(text));\r\n    } else {\r\n      return (this.y += this.currentLineHeight(true) + lineGap);\r\n    }\r\n  },\r\n\r\n  _fragment(text, x, y, options) {\r\n    let dy, encoded, i, positions, textWidth, words;\r\n    text = `${text}`.replace(/\\n/g, '');\r\n    if (text.length === 0) {\r\n      return;\r\n    }\r\n\r\n    // handle options\r\n    const align = options.align || 'left';\r\n    let wordSpacing = options.wordSpacing || 0;\r\n    const characterSpacing = options.characterSpacing || 0;\r\n\r\n    // text alignments\r\n    if (options.width) {\r\n      switch (align) {\r\n        case 'right':\r\n          textWidth = this.widthOfString(text.replace(/\\s+$/, ''), options);\r\n          x += options.lineWidth - textWidth;\r\n          break;\r\n\r\n        case 'center':\r\n          x += options.lineWidth / 2 - options.textWidth / 2;\r\n          break;\r\n\r\n        case 'justify':\r\n          // calculate the word spacing value\r\n          words = text.trim().split(/\\s+/);\r\n          textWidth = this.widthOfString(text.replace(/\\s+/g, ''), options);\r\n          var spaceWidth = this.widthOfString(' ') + characterSpacing;\r\n          wordSpacing = Math.max(\r\n            0,\r\n            (options.lineWidth - textWidth) / Math.max(1, words.length - 1) -\r\n              spaceWidth\r\n          );\r\n          break;\r\n      }\r\n    }\r\n\r\n    // text baseline alignments based on http://wiki.apache.org/xmlgraphics-fop/LineLayout/AlignmentHandling\r\n    if (typeof options.baseline === 'number') {\r\n      dy = -options.baseline;\r\n    } else {\r\n      switch (options.baseline) {\r\n        case 'svg-middle':\r\n          dy = 0.5 * this._font.xHeight;\r\n          break;\r\n        case 'middle':\r\n        case 'svg-central':\r\n          dy = 0.5 * (this._font.descender + this._font.ascender);\r\n          break;\r\n        case 'bottom':\r\n        case 'ideographic':\r\n          dy = this._font.descender;\r\n          break;\r\n        case 'alphabetic':\r\n          dy = 0;\r\n          break;\r\n        case 'mathematical':\r\n          dy = 0.5 * this._font.ascender;\r\n          break;\r\n        case 'hanging':\r\n          dy = 0.8 * this._font.ascender;\r\n          break;\r\n        case 'top':\r\n          dy = this._font.ascender;\r\n          break;\r\n        default:\r\n          dy = this._font.ascender;\r\n      }\r\n      dy = (dy / 1000) * this._fontSize;\r\n    }\r\n\r\n    // calculate the actual rendered width of the string after word and character spacing\r\n    const renderedWidth =\r\n      options.textWidth +\r\n      wordSpacing * (options.wordCount - 1) +\r\n      characterSpacing * (text.length - 1);\r\n\r\n    // create link annotations if the link option is given\r\n    if (options.link != null) {\r\n      this.link(x, y, renderedWidth, this.currentLineHeight(), options.link);\r\n    }\r\n    if (options.goTo != null) {\r\n      this.goTo(x, y, renderedWidth, this.currentLineHeight(), options.goTo);\r\n    }\r\n    if (options.destination != null) {\r\n      this.addNamedDestination(options.destination, 'XYZ', x, y, null);\r\n    }\r\n\r\n    // create underline\r\n    if (options.underline) {\r\n      this.save();\r\n      if (!options.stroke) {\r\n        this.strokeColor(...(this._fillColor || []));\r\n      }\r\n\r\n      const lineWidth =\r\n        this._fontSize < 10 ? 0.5 : Math.floor(this._fontSize / 10);\r\n      this.lineWidth(lineWidth);\r\n\r\n      let lineY = (y + this.currentLineHeight())  - lineWidth\r\n      this.moveTo(x, lineY);\r\n      this.lineTo(x + renderedWidth, lineY);\r\n      this.stroke();\r\n      this.restore();\r\n    }\r\n    \r\n    // create strikethrough line\r\n    if (options.strike) {\r\n      this.save();\r\n      if (!options.stroke) {\r\n        this.strokeColor(...(this._fillColor || []));\r\n      }\r\n\r\n      const lineWidth =\r\n        this._fontSize < 10 ? 0.5 : Math.floor(this._fontSize / 10);\r\n      this.lineWidth(lineWidth);\r\n\r\n      let lineY = y + this.currentLineHeight() / 2;\r\n      this.moveTo(x, lineY);\r\n      this.lineTo(x + renderedWidth, lineY);\r\n      this.stroke();\r\n      this.restore();\r\n    }\r\n\r\n    this.save();\r\n\r\n    // oblique (angle in degrees or boolean)\r\n    if (options.oblique) {\r\n      let skew;\r\n      if (typeof options.oblique === 'number') {\r\n        skew = -Math.tan((options.oblique * Math.PI) / 180);\r\n      } else {\r\n        skew = -0.25;\r\n      }\r\n      this.transform(1, 0, 0, 1, x, y);\r\n      this.transform(1, 0, skew, 1, -skew * dy, 0);\r\n      this.transform(1, 0, 0, 1, -x, -y);\r\n    }\r\n\r\n    // flip coordinate system\r\n    this.transform(1, 0, 0, -1, 0, this.page.height);\r\n    y = this.page.height - y - dy;\r\n\r\n    // add current font to page if necessary\r\n    if (this.page.fonts[this._font.id] == null) {\r\n      this.page.fonts[this._font.id] = this._font.ref();\r\n    }\r\n\r\n    // begin the text object\r\n    this.addContent('BT');\r\n\r\n    // text position\r\n    this.addContent(`1 0 0 1 ${number(x)} ${number(y)} Tm`);\r\n\r\n    // font and font size\r\n    this.addContent(`/${this._font.id} ${number(this._fontSize)} Tf`);\r\n\r\n    // rendering mode\r\n    const mode = options.fill && options.stroke ? 2 : options.stroke ? 1 : 0;\r\n    if (mode) {\r\n      this.addContent(`${mode} Tr`);\r\n    }\r\n\r\n    // Character spacing\r\n    if (characterSpacing) {\r\n      this.addContent(`${number(characterSpacing)} Tc`);\r\n    }\r\n\r\n    // Add the actual text\r\n    // If we have a word spacing value, we need to encode each word separately\r\n    // since the normal Tw operator only works on character code 32, which isn't\r\n    // used for embedded fonts.\r\n    if (wordSpacing) {\r\n      words = text.trim().split(/\\s+/);\r\n      wordSpacing += this.widthOfString(' ') + characterSpacing;\r\n      wordSpacing *= 1000 / this._fontSize;\r\n\r\n      encoded = [];\r\n      positions = [];\r\n      for (let word of words) {\r\n        const [encodedWord, positionsWord] = this._font.encode(\r\n          word,\r\n          options.features\r\n        );\r\n        encoded = encoded.concat(encodedWord);\r\n        positions = positions.concat(positionsWord);\r\n\r\n        // add the word spacing to the end of the word\r\n        // clone object because of cache\r\n        const space = {};\r\n        const object = positions[positions.length - 1];\r\n        for (let key in object) {\r\n          const val = object[key];\r\n          space[key] = val;\r\n        }\r\n        space.xAdvance += wordSpacing;\r\n        positions[positions.length - 1] = space;\r\n      }\r\n    } else {\r\n      [encoded, positions] = this._font.encode(text, options.features);\r\n    }\r\n\r\n    const scale = this._fontSize / 1000;\r\n    const commands = [];\r\n    let last = 0;\r\n    let hadOffset = false;\r\n\r\n    // Adds a segment of text to the TJ command buffer\r\n    const addSegment = cur => {\r\n      if (last < cur) {\r\n        const hex = encoded.slice(last, cur).join('');\r\n        const advance =\r\n          positions[cur - 1].xAdvance - positions[cur - 1].advanceWidth;\r\n        commands.push(`<${hex}> ${number(-advance)}`);\r\n      }\r\n\r\n      return (last = cur);\r\n    };\r\n\r\n    // Flushes the current TJ commands to the output stream\r\n    const flush = i => {\r\n      addSegment(i);\r\n\r\n      if (commands.length > 0) {\r\n        this.addContent(`[${commands.join(' ')}] TJ`);\r\n        return (commands.length = 0);\r\n      }\r\n    };\r\n\r\n    for (i = 0; i < positions.length; i++) {\r\n      // If we have an x or y offset, we have to break out of the current TJ command\r\n      // so we can move the text position.\r\n      const pos = positions[i];\r\n      if (pos.xOffset || pos.yOffset) {\r\n        // Flush the current buffer\r\n        flush(i);\r\n\r\n        // Move the text position and flush just the current character\r\n        this.addContent(\r\n          `1 0 0 1 ${number(x + pos.xOffset * scale)} ${number(\r\n            y + pos.yOffset * scale\r\n          )} Tm`\r\n        );\r\n        flush(i + 1);\r\n\r\n        hadOffset = true;\r\n      } else {\r\n        // If the last character had an offset, reset the text position\r\n        if (hadOffset) {\r\n          this.addContent(`1 0 0 1 ${number(x)} ${number(y)} Tm`);\r\n          hadOffset = false;\r\n        }\r\n\r\n        // Group segments that don't have any advance adjustments\r\n        if (pos.xAdvance - pos.advanceWidth !== 0) {\r\n          addSegment(i + 1);\r\n        }\r\n      }\r\n\r\n      x += pos.xAdvance * scale;\r\n    }\r\n\r\n    // Flush any remaining commands\r\n    flush(i);\r\n\r\n    // end the text object\r\n    this.addContent('ET');\r\n\r\n    // restore flipped coordinate system\r\n    return this.restore();\r\n  }\r\n};\r\n", "import exif from 'jpeg-exif';\r\n\r\nconst MARKERS = [\r\n  0xffc0,\r\n  0xffc1,\r\n  0xffc2,\r\n  0xffc3,\r\n  0xffc5,\r\n  0xffc6,\r\n  0xffc7,\r\n  0xffc8,\r\n  0xffc9,\r\n  0xffca,\r\n  0xffcb,\r\n  0xffcc,\r\n  0xffcd,\r\n  0xffce,\r\n  0xffcf\r\n];\r\n\r\nconst COLOR_SPACE_MAP = {\r\n  1: 'DeviceGray',\r\n  3: 'DeviceRGB',\r\n  4: 'DeviceCMYK'\r\n};\r\n\r\nclass JPEG {\r\n  constructor(data, label) {\r\n    let marker;\r\n    this.data = data;\r\n    this.label = label;\r\n    if (this.data.readUInt16BE(0) !== 0xffd8) {\r\n      throw 'SOI not found in JPEG';\r\n    }\r\n\r\n    // Parse the EXIF orientation\r\n    this.orientation = exif.fromBuffer(this.data).Orientation || 1;\r\n\r\n    let pos = 2;\r\n    while (pos < this.data.length) {\r\n      marker = this.data.readUInt16BE(pos);\r\n      pos += 2;\r\n      if (MARKERS.includes(marker)) {\r\n        break;\r\n      }\r\n      pos += this.data.readUInt16BE(pos);\r\n    }\r\n\r\n    if (!MARKERS.includes(marker)) {\r\n      throw 'Invalid JPEG.';\r\n    }\r\n    pos += 2;\r\n\r\n    this.bits = this.data[pos++];\r\n    this.height = this.data.readUInt16BE(pos);\r\n    pos += 2;\r\n\r\n    this.width = this.data.readUInt16BE(pos);\r\n    pos += 2;\r\n\r\n    const channels = this.data[pos++];\r\n    this.colorSpace = COLOR_SPACE_MAP[channels];\r\n\r\n    this.obj = null;\r\n  }\r\n\r\n  embed(document) {\r\n    if (this.obj) {\r\n      return;\r\n    }\r\n\r\n    this.obj = document.ref({\r\n      Type: 'XObject',\r\n      Subtype: 'Image',\r\n      BitsPerComponent: this.bits,\r\n      Width: this.width,\r\n      Height: this.height,\r\n      ColorSpace: this.colorSpace,\r\n      Filter: 'DCTDecode'\r\n    });\r\n\r\n    // add extra decode params for CMYK images. By swapping the\r\n    // min and max values from the default, we invert the colors. See\r\n    // section 4.8.4 of the spec.\r\n    if (this.colorSpace === 'DeviceCMYK') {\r\n      this.obj.data['Decode'] = [1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0];\r\n    }\r\n\r\n    this.obj.end(this.data);\r\n\r\n    // free memory\r\n    return (this.data = null);\r\n  }\r\n}\r\n\r\nexport default JPEG;\r\n", "import zlib from 'zlib';\r\nimport PNG from 'png-js';\r\n\r\nclass PNGImage {\r\n  constructor(data, label) {\r\n    this.label = label;\r\n    this.image = new PNG(data);\r\n    this.width = this.image.width;\r\n    this.height = this.image.height;\r\n    this.imgData = this.image.imgData;\r\n    this.obj = null;\r\n  }\r\n\r\n  embed(document) {\r\n    let dataDecoded = false;\r\n\r\n    this.document = document;\r\n    if (this.obj) {\r\n      return;\r\n    }\r\n\r\n    const hasAlphaChannel = this.image.hasAlphaChannel;\r\n    const isInterlaced = this.image.interlaceMethod === 1;\r\n\r\n    this.obj = this.document.ref({\r\n      Type: 'XObject',\r\n      Subtype: 'Image',\r\n      BitsPerComponent: hasAlphaChannel ? 8 : this.image.bits,\r\n      Width: this.width,\r\n      Height: this.height,\r\n      Filter: 'FlateDecode'\r\n    });\r\n\r\n    if (!hasAlphaChannel) {\r\n      const params = this.document.ref({\r\n        Predictor: isInterlaced ? 1 : 15,\r\n        Colors: this.image.colors,\r\n        BitsPerComponent: this.image.bits,\r\n        Columns: this.width\r\n      });\r\n\r\n      this.obj.data['DecodeParms'] = params;\r\n      params.end();\r\n    }\r\n\r\n    if (this.image.palette.length === 0) {\r\n      this.obj.data['ColorSpace'] = this.image.colorSpace;\r\n    } else {\r\n      // embed the color palette in the PDF as an object stream\r\n      const palette = this.document.ref();\r\n      palette.end(Buffer.from(this.image.palette));\r\n\r\n      // build the color space array for the image\r\n      this.obj.data['ColorSpace'] = [\r\n        'Indexed',\r\n        'DeviceRGB',\r\n        this.image.palette.length / 3 - 1,\r\n        palette\r\n      ];\r\n    }\r\n\r\n    // For PNG color types 0, 2 and 3, the transparency data is stored in\r\n    // a dedicated PNG chunk.\r\n    if (this.image.transparency.grayscale != null) {\r\n      // Use Color Key Masking (spec section 4.8.5)\r\n      // An array with N elements, where N is two times the number of color components.\r\n      const val = this.image.transparency.grayscale;\r\n      this.obj.data['Mask'] = [val, val];\r\n    } else if (this.image.transparency.rgb) {\r\n      // Use Color Key Masking (spec section 4.8.5)\r\n      // An array with N elements, where N is two times the number of color components.\r\n      const { rgb } = this.image.transparency;\r\n      const mask = [];\r\n      for (let x of rgb) {\r\n        mask.push(x, x);\r\n      }\r\n\r\n      this.obj.data['Mask'] = mask;\r\n    } else if (this.image.transparency.indexed) {\r\n      // Create a transparency SMask for the image based on the data\r\n      // in the PLTE and tRNS sections. See below for details on SMasks.\r\n      dataDecoded = true;\r\n      return this.loadIndexedAlphaChannel();\r\n    } else if (hasAlphaChannel) {\r\n      // For PNG color types 4 and 6, the transparency data is stored as a alpha\r\n      // channel mixed in with the main image data. Separate this data out into an\r\n      // SMask object and store it separately in the PDF.\r\n      dataDecoded = true;\r\n      return this.splitAlphaChannel();\r\n    }\r\n\r\n    if (isInterlaced && !dataDecoded) {\r\n      return this.decodeData();\r\n    }\r\n\r\n    this.finalize();\r\n  }\r\n\r\n  finalize() {\r\n    if (this.alphaChannel) {\r\n      const sMask = this.document.ref({\r\n        Type: 'XObject',\r\n        Subtype: 'Image',\r\n        Height: this.height,\r\n        Width: this.width,\r\n        BitsPerComponent: 8,\r\n        Filter: 'FlateDecode',\r\n        ColorSpace: 'DeviceGray',\r\n        Decode: [0, 1]\r\n      });\r\n\r\n      sMask.end(this.alphaChannel);\r\n      this.obj.data['SMask'] = sMask;\r\n    }\r\n\r\n    // add the actual image data\r\n    this.obj.end(this.imgData);\r\n\r\n    // free memory\r\n    this.image = null;\r\n    return (this.imgData = null);\r\n  }\r\n\r\n  splitAlphaChannel() {\r\n    return this.image.decodePixels(pixels => {\r\n      let a, p;\r\n      const colorCount = this.image.colors;\r\n      const pixelCount = this.width * this.height;\r\n      const imgData = Buffer.alloc(pixelCount * colorCount);\r\n      const alphaChannel = Buffer.alloc(pixelCount);\r\n\r\n      let i = (p = a = 0);\r\n      const len = pixels.length;\r\n      // For 16bit images copy only most significant byte (MSB) - PNG data is always stored in network byte order (MSB first)\r\n      const skipByteCount = this.image.bits === 16 ? 1 : 0;\r\n      while (i < len) {\r\n        for (let colorIndex = 0; colorIndex < colorCount; colorIndex++) {\r\n          imgData[p++] = pixels[i++];\r\n          i += skipByteCount;\r\n        }\r\n        alphaChannel[a++] = pixels[i++];\r\n        i += skipByteCount;\r\n      }\r\n\r\n      this.imgData = zlib.deflateSync(imgData);\r\n      this.alphaChannel = zlib.deflateSync(alphaChannel);\r\n      return this.finalize();\r\n    });\r\n  }\r\n\r\n  loadIndexedAlphaChannel() {\r\n    const transparency = this.image.transparency.indexed;\r\n    return this.image.decodePixels(pixels => {\r\n      const alphaChannel = Buffer.alloc(this.width * this.height);\r\n\r\n      let i = 0;\r\n      for (let j = 0, end = pixels.length; j < end; j++) {\r\n        alphaChannel[i++] = transparency[pixels[j]];\r\n      }\r\n\r\n      this.alphaChannel = zlib.deflateSync(alphaChannel);\r\n      return this.finalize();\r\n    });\r\n  }\r\n\r\n  decodeData() {\r\n    this.image.decodePixels(pixels => {\r\n      this.imgData = zlib.deflateSync(pixels);\r\n      this.finalize();\r\n    });\r\n  }\r\n}\r\n\r\nexport default PNGImage;\r\n", "/*\r\nPDFImage - embeds images in PDF documents\r\nBy Devon Govett\r\n*/\r\n\r\nimport fs from 'fs';\r\nimport JPEG from './image/jpeg';\r\nimport PNG from './image/png';\r\n\r\nclass PDFImage {\r\n  static open(src, label) {\r\n    let data;\r\n    if (Buffer.isBuffer(src)) {\r\n      data = src;\r\n    } else if (src instanceof ArrayBuffer) {\r\n      data = Buffer.from(new Uint8Array(src));\r\n    } else {\r\n      let match;\r\n      if ((match = /^data:.+?;base64,(.*)$/.exec(src))) {\r\n        data = Buffer.from(match[1], 'base64');\r\n      } else {\r\n        data = fs.readFileSync(src);\r\n        if (!data) {\r\n          return;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (data[0] === 0xff && data[1] === 0xd8) {\r\n      return new JPEG(data, label);\r\n    } else if (data[0] === 0x89 && data.toString('ascii', 1, 4) === 'PNG') {\r\n      return new PNG(data, label);\r\n    } else {\r\n      throw new Error('Unknown image format.');\r\n    }\r\n  }\r\n}\r\n\r\nexport default PDFImage;\r\n", "import PDFImage from '../image';\r\n\r\nexport default {\r\n  initImages() {\r\n    this._imageRegistry = {};\r\n    return (this._imageCount = 0);\r\n  },\r\n\r\n  image(src, x, y, options = {}) {\r\n    let bh, bp, bw, image, ip, left, left1, rotateAngle, originX, originY;\r\n    if (typeof x === 'object') {\r\n      options = x;\r\n      x = null;\r\n    }\r\n\r\n    // Ignore orientation based on document options or image options\r\n    const ignoreOrientation =\r\n      options.ignoreOrientation ||\r\n      (options.ignoreOrientation !== false && this.options.ignoreOrientation);\r\n\r\n    x = (left = x != null ? x : options.x) != null ? left : this.x;\r\n    y = (left1 = y != null ? y : options.y) != null ? left1 : this.y;\r\n\r\n    if (typeof src === 'string') {\r\n      image = this._imageRegistry[src];\r\n    }\r\n\r\n    if (!image) {\r\n      if (src.width && src.height) {\r\n        image = src;\r\n      } else {\r\n        image = this.openImage(src);\r\n      }\r\n    }\r\n\r\n    if (!image.obj) {\r\n      image.embed(this);\r\n    }\r\n\r\n    if (this.page.xobjects[image.label] == null) {\r\n      this.page.xobjects[image.label] = image.obj;\r\n    }\r\n\r\n    let { width, height } = image;\r\n\r\n    // If EXIF orientation calls for it, swap width and height\r\n    if (!ignoreOrientation && image.orientation > 4) {\r\n      [width, height] = [height, width];\r\n    }\r\n\r\n    let w = options.width || width;\r\n    let h = options.height || height;\r\n\r\n    if (options.width && !options.height) {\r\n      const wp = w / width;\r\n      w = width * wp;\r\n      h = height * wp;\r\n    } else if (options.height && !options.width) {\r\n      const hp = h / height;\r\n      w = width * hp;\r\n      h = height * hp;\r\n    } else if (options.scale) {\r\n      w = width * options.scale;\r\n      h = height * options.scale;\r\n    } else if (options.fit) {\r\n      [bw, bh] = options.fit;\r\n      bp = bw / bh;\r\n      ip = width / height;\r\n      if (ip > bp) {\r\n        w = bw;\r\n        h = bw / ip;\r\n      } else {\r\n        h = bh;\r\n        w = bh * ip;\r\n      }\r\n    } else if (options.cover) {\r\n      [bw, bh] = options.cover;\r\n      bp = bw / bh;\r\n      ip = width / height;\r\n      if (ip > bp) {\r\n        h = bh;\r\n        w = bh * ip;\r\n      } else {\r\n        w = bw;\r\n        h = bw / ip;\r\n      }\r\n    }\r\n\r\n    if (options.fit || options.cover) {\r\n      if (options.align === 'center') {\r\n        x = x + bw / 2 - w / 2;\r\n      } else if (options.align === 'right') {\r\n        x = x + bw - w;\r\n      }\r\n\r\n      if (options.valign === 'center') {\r\n        y = y + bh / 2 - h / 2;\r\n      } else if (options.valign === 'bottom') {\r\n        y = y + bh - h;\r\n      }\r\n    }\r\n\r\n    if (!ignoreOrientation) {\r\n      switch (image.orientation) {\r\n        // No orientation (need to flip image, though, because of the default transform matrix on the document)\r\n        default:\r\n        case 1:\r\n          h = -h;\r\n          y -= h;\r\n\r\n          rotateAngle = 0;\r\n          break;\r\n        // Flip Horizontal\r\n        case 2:\r\n          w = -w;\r\n          h = -h;\r\n          x -= w;\r\n          y -= h;\r\n\r\n          rotateAngle = 0;\r\n          break;\r\n        // Rotate 180 degrees\r\n        case 3:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          h = -h;\r\n          x -= w;\r\n\r\n          rotateAngle = 180;\r\n          break;\r\n        // Flip vertical\r\n        case 4:\r\n          // Do nothing, image will be flipped\r\n\r\n          break;\r\n        // Flip horizontally and rotate 270 degrees CW\r\n        case 5:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          [w, h] = [h, w];\r\n          y -= h;\r\n\r\n          rotateAngle = 90;\r\n          break;\r\n        // Rotate 90 degrees CW\r\n        case 6:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          [w, h] = [h, w];\r\n          h = -h;\r\n\r\n          rotateAngle = 90;\r\n          break;\r\n        // Flip horizontally and rotate 90 degrees CW\r\n        case 7:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          [w, h] = [h, w];\r\n          h = -h;\r\n          w = -w;\r\n          x -= w;\r\n\r\n          rotateAngle = 90;\r\n          break;\r\n        // Rotate 270 degrees CW\r\n        case 8:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          [w, h] = [h, w];\r\n          h = -h;\r\n          x -= w;\r\n          y -= h;\r\n\r\n          rotateAngle = -90;\r\n          break;\r\n      }\r\n    } else {\r\n      h = -h;\r\n      y -= h;\r\n      rotateAngle = 0;\r\n    }\r\n\r\n    // create link annotations if the link option is given\r\n    if (options.link != null) {\r\n      this.link(x, y, w, h, options.link);\r\n    }\r\n    if (options.goTo != null) {\r\n      this.goTo(x, y, w, h, options.goTo);\r\n    }\r\n    if (options.destination != null) {\r\n      this.addNamedDestination(options.destination, 'XYZ', x, y, null);\r\n    }\r\n\r\n    // Set the current y position to below the image if it is in the document flow\r\n    if (this.y === y) {\r\n      this.y += h;\r\n    }\r\n\r\n    this.save();\r\n\r\n    if (rotateAngle) {\r\n      this.rotate(rotateAngle, {\r\n        origin: [originX, originY]\r\n      });\r\n    }\r\n\r\n    this.transform(w, 0, 0, h, x, y);\r\n    this.addContent(`/${image.label} Do`);\r\n    this.restore();\r\n\r\n    return this;\r\n  },\r\n\r\n  openImage(src) {\r\n    let image;\r\n    if (typeof src === 'string') {\r\n      image = this._imageRegistry[src];\r\n    }\r\n\r\n    if (!image) {\r\n      image = PDFImage.open(src, `I${++this._imageCount}`);\r\n      if (typeof src === 'string') {\r\n        this._imageRegistry[src] = image;\r\n      }\r\n    }\r\n\r\n    return image;\r\n  }\r\n};\r\n", "export default {\r\n  annotate(x, y, w, h, options) {\r\n    options.Type = 'Annot';\r\n    options.Rect = this._convertRect(x, y, w, h);\r\n    options.Border = [0, 0, 0];\r\n\r\n    if (options.Subtype === 'Link' && typeof options.F === 'undefined') {\r\n      options.F = 1 << 2; // Print Annotation Flag\r\n    }\r\n\r\n    if (options.Subtype !== 'Link') {\r\n      if (options.C == null) {\r\n        options.C = this._normalizeColor(options.color || [0, 0, 0]);\r\n      }\r\n    } // convert colors\r\n    delete options.color;\r\n\r\n    if (typeof options.Dest === 'string') {\r\n      options.Dest = new String(options.Dest);\r\n    }\r\n\r\n    // Capitalize keys\r\n    for (let key in options) {\r\n      const val = options[key];\r\n      options[key[0].toUpperCase() + key.slice(1)] = val;\r\n    }\r\n\r\n    const ref = this.ref(options);\r\n    this.page.annotations.push(ref);\r\n    ref.end();\r\n    return this;\r\n  },\r\n\r\n  note(x, y, w, h, contents, options = {}) {\r\n    options.Subtype = 'Text';\r\n    options.Contents = new String(contents);\r\n    options.Name = 'Comment';\r\n    if (options.color == null) {\r\n      options.color = [243, 223, 92];\r\n    }\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  goTo(x, y, w, h, name, options = {}) {\r\n    options.Subtype = 'Link';\r\n    options.A = this.ref({\r\n      S: 'GoTo',\r\n      D: new String(name)\r\n    });\r\n    options.A.end();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  link(x, y, w, h, url, options = {}) {\r\n    options.Subtype = 'Link';\r\n\r\n    if (typeof url === 'number') {\r\n      // Link to a page in the document (the page must already exist)\r\n      const pages = this._root.data.Pages.data;\r\n      if (url >= 0 && url < pages.Kids.length) {\r\n        options.A = this.ref({\r\n          S: 'GoTo',\r\n          D: [pages.Kids[url], 'XYZ', null, null, null]\r\n        });\r\n        options.A.end();\r\n      } else {\r\n        throw new Error(`The document has no page ${url}`);\r\n      }\r\n    } else {\r\n      // Link to an external url\r\n      options.A = this.ref({\r\n        S: 'URI',\r\n        URI: new String(url)\r\n      });\r\n      options.A.end();\r\n    }\r\n\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  _markup(x, y, w, h, options = {}) {\r\n    const [x1, y1, x2, y2] = this._convertRect(x, y, w, h);\r\n    options.QuadPoints = [x1, y2, x2, y2, x1, y1, x2, y1];\r\n    options.Contents = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  highlight(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Highlight';\r\n    if (options.color == null) {\r\n      options.color = [241, 238, 148];\r\n    }\r\n    return this._markup(x, y, w, h, options);\r\n  },\r\n\r\n  underline(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Underline';\r\n    return this._markup(x, y, w, h, options);\r\n  },\r\n\r\n  strike(x, y, w, h, options = {}) {\r\n    options.Subtype = 'StrikeOut';\r\n    return this._markup(x, y, w, h, options);\r\n  },\r\n\r\n  lineAnnotation(x1, y1, x2, y2, options = {}) {\r\n    options.Subtype = 'Line';\r\n    options.Contents = new String();\r\n    options.L = [x1, this.page.height - y1, x2, this.page.height - y2];\r\n    return this.annotate(x1, y1, x2, y2, options);\r\n  },\r\n\r\n  rectAnnotation(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Square';\r\n    options.Contents = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  ellipseAnnotation(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Circle';\r\n    options.Contents = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  textAnnotation(x, y, w, h, text, options = {}) {\r\n    options.Subtype = 'FreeText';\r\n    options.Contents = new String(text);\r\n    options.DA = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  fileAnnotation(x, y, w, h, file = {}, options = {}) {\r\n    // create hidden file\r\n    const filespec = this.file(\r\n      file.src,\r\n      Object.assign({ hidden: true }, file)\r\n    );\r\n\r\n    options.Subtype = 'FileAttachment';\r\n    options.FS = filespec;\r\n\r\n    // add description from filespec unless description (Contents) has already been set\r\n    if (options.Contents) {\r\n      options.Contents = new String(options.Contents);\r\n    } else if (filespec.data.Desc) {\r\n      options.Contents = filespec.data.Desc;\r\n    }\r\n\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  _convertRect(x1, y1, w, h) {\r\n    // flip y1 and y2\r\n    let y2 = y1;\r\n    y1 += h;\r\n\r\n    // make x2\r\n    let x2 = x1 + w;\r\n\r\n    // apply current transformation matrix to points\r\n    const [m0, m1, m2, m3, m4, m5] = this._ctm;\r\n    x1 = m0 * x1 + m2 * y1 + m4;\r\n    y1 = m1 * x1 + m3 * y1 + m5;\r\n    x2 = m0 * x2 + m2 * y2 + m4;\r\n    y2 = m1 * x2 + m3 * y2 + m5;\r\n\r\n    return [x1, y1, x2, y2];\r\n  }\r\n};\r\n", "class PDFOutline {\r\n  constructor(document, parent, title, dest, options = { expanded: false }) {\r\n    this.document = document;\r\n    this.options = options;\r\n    this.outlineData = {};\r\n\r\n    if (dest !== null) {\r\n      this.outlineData['Dest'] = [dest.dictionary, 'Fit'];\r\n    }\r\n\r\n    if (parent !== null) {\r\n      this.outlineData['Parent'] = parent;\r\n    }\r\n\r\n    if (title !== null) {\r\n      this.outlineData['Title'] = new String(title);\r\n    }\r\n\r\n    this.dictionary = this.document.ref(this.outlineData);\r\n    this.children = [];\r\n  }\r\n\r\n  addItem(title, options = { expanded: false }) {\r\n    const result = new PDFOutline(\r\n      this.document,\r\n      this.dictionary,\r\n      title,\r\n      this.document.page,\r\n      options\r\n    );\r\n    this.children.push(result);\r\n\r\n    return result;\r\n  }\r\n\r\n  endOutline() {\r\n    if (this.children.length > 0) {\r\n      if (this.options.expanded) {\r\n        this.outlineData.Count = this.children.length;\r\n      }\r\n\r\n      const first = this.children[0],\r\n        last = this.children[this.children.length - 1];\r\n      this.outlineData.First = first.dictionary;\r\n      this.outlineData.Last = last.dictionary;\r\n\r\n      for (let i = 0, len = this.children.length; i < len; i++) {\r\n        const child = this.children[i];\r\n        if (i > 0) {\r\n          child.outlineData.Prev = this.children[i - 1].dictionary;\r\n        }\r\n        if (i < this.children.length - 1) {\r\n          child.outlineData.Next = this.children[i + 1].dictionary;\r\n        }\r\n        child.endOutline();\r\n      }\r\n    }\r\n\r\n    return this.dictionary.end();\r\n  }\r\n}\r\n\r\nexport default PDFOutline;\r\n", "import PDFOutline from '../outline';\r\n\r\nexport default {\r\n  initOutline() {\r\n    return (this.outline = new PDFOutline(this, null, null, null));\r\n  },\r\n\r\n  endOutline() {\r\n    this.outline.endOutline();\r\n    if (this.outline.children.length > 0) {\r\n      this._root.data.Outlines = this.outline.dictionary;\r\n      return (this._root.data.PageMode = 'UseOutlines');\r\n    }\r\n  }\r\n};\r\n", "/*\r\nPDFStructureContent - a reference to a marked structure content\r\nBy <PERSON>\r\n*/\r\n\r\nclass PDFStructureContent {\r\n  constructor(pageRef, mcid) {\r\n    this.refs = [{ pageRef, mcid }];\r\n  }\r\n\r\n  push(structContent) {\r\n    structContent.refs.forEach((ref) => this.refs.push(ref));\r\n  }\r\n}\r\n\r\nexport default PDFStructureContent;\r\n", "/*\r\nPDFStructureElement - represents an element in the PDF logical structure tree\r\nBy <PERSON>\r\n*/\r\n\r\nimport PDFStructureContent from \"./structure_content\";\r\n\r\nclass PDFStructureElement {\r\n  constructor(document, type, options = {}, children = null) {\r\n    this.document = document;\r\n\r\n    this._attached = false;\r\n    this._ended = false;\r\n    this._flushed = false;\r\n    this.dictionary = document.ref({\r\n      // Type: \"StructElem\",\r\n      S: type\r\n    });\r\n\r\n    const data = this.dictionary.data;\r\n\r\n    if (Array.isArray(options) || this._isValidChild(options)) {\r\n      children = options;\r\n      options = {};\r\n    }\r\n\r\n    if (typeof options.title !== 'undefined') {\r\n      data.T = new String(options.title);\r\n    }\r\n    if (typeof options.lang !== 'undefined') {\r\n      data.Lang = new String(options.lang);\r\n    }\r\n    if (typeof options.alt !== 'undefined') {\r\n      data.Alt = new String(options.alt);\r\n    }\r\n    if (typeof options.expanded !== 'undefined') {\r\n      data.E = new String(options.expanded);\r\n    }\r\n    if (typeof options.actual !== 'undefined') {\r\n      data.ActualText = new String(options.actual);\r\n    }\r\n\r\n    this._children = [];\r\n\r\n    if (children) {\r\n      if (!Array.isArray(children)) {\r\n        children = [children];\r\n      }\r\n      children.forEach((child) => this.add(child));\r\n      this.end();\r\n    }\r\n  }\r\n\r\n  add(child) {\r\n    if (this._ended) {\r\n      throw new Error(`Cannot add child to already-ended structure element`);\r\n    }\r\n\r\n    if (!this._isValidChild(child)) {\r\n      throw new Error(`Invalid structure element child`);\r\n    }\r\n\r\n    if (child instanceof PDFStructureElement) {\r\n      child.setParent(this.dictionary);\r\n      if (this._attached) {\r\n        child.setAttached();\r\n      }\r\n    }\r\n\r\n    if (child instanceof PDFStructureContent) {\r\n      this._addContentToParentTree(child);\r\n    }\r\n\r\n    if (typeof child === 'function' && this._attached) {\r\n      // _contentForClosure() adds the content to the parent tree\r\n      child = this._contentForClosure(child);\r\n    }\r\n\r\n    this._children.push(child);\r\n\r\n    return this;\r\n  }\r\n\r\n  _addContentToParentTree(content) {\r\n    content.refs.forEach(({ pageRef, mcid }) => {\r\n      const pageStructParents = this.document.getStructParentTree()\r\n        .get(pageRef.data.StructParents);\r\n      pageStructParents[mcid] = this.dictionary;\r\n    });\r\n  }\r\n\r\n  setParent(parentRef) {\r\n    if (this.dictionary.data.P) {\r\n      throw new Error(`Structure element added to more than one parent`);\r\n    }\r\n\r\n    this.dictionary.data.P = parentRef;\r\n\r\n    this._flush();\r\n  }\r\n\r\n  setAttached() {\r\n    if (this._attached) {\r\n      return;\r\n    }\r\n\r\n    this._children.forEach((child, index) => {\r\n      if (child instanceof PDFStructureElement) {\r\n        child.setAttached();\r\n      }\r\n      if (typeof child === 'function') {\r\n        this._children[index] = this._contentForClosure(child);\r\n      }\r\n    });\r\n\r\n    this._attached = true;\r\n\r\n    this._flush();\r\n  }\r\n\r\n  end() {\r\n    if (this._ended) {\r\n      return;\r\n    }\r\n\r\n    this._children\r\n      .filter((child) => child instanceof PDFStructureElement)\r\n      .forEach((child) => child.end());\r\n\r\n    this._ended = true;\r\n\r\n    this._flush();\r\n  }\r\n\r\n  _isValidChild(child) {\r\n    return child instanceof PDFStructureElement ||\r\n        child instanceof PDFStructureContent ||\r\n        typeof child === 'function';\r\n  }\r\n\r\n  _contentForClosure(closure) {\r\n    const content = this.document.markStructureContent(this.dictionary.data.S);\r\n    closure();\r\n    this.document.endMarkedContent();\r\n\r\n    this._addContentToParentTree(content);\r\n\r\n    return content;\r\n  }\r\n\r\n  _isFlushable() {\r\n    if (!this.dictionary.data.P || !this._ended) {\r\n      return false;\r\n    }\r\n\r\n    return this._children.every((child) => {\r\n      if (typeof child === 'function') {\r\n        return false;\r\n      }\r\n      if (child instanceof PDFStructureElement) {\r\n        return child._isFlushable();\r\n      }\r\n      return true;\r\n    });\r\n  }\r\n\r\n  _flush() {\r\n    if (this._flushed || !this._isFlushable()) {\r\n      return;\r\n    }\r\n\r\n    this.dictionary.data.K = [];\r\n\r\n    this._children.forEach((child) => this._flushChild(child));\r\n\r\n    this.dictionary.end();\r\n\r\n    // free memory used by children; the dictionary itself may still be\r\n    // referenced by a parent structure element or root, but we can\r\n    // at least trim the tree here\r\n    this._children = [];\r\n    this.dictionary.data.K = null;\r\n\r\n    this._flushed = true;\r\n  }\r\n\r\n  _flushChild(child) {\r\n    if (child instanceof PDFStructureElement) {\r\n      this.dictionary.data.K.push(child.dictionary);\r\n    }\r\n\r\n    if (child instanceof PDFStructureContent) {\r\n      child.refs.forEach(({ pageRef, mcid }) => {\r\n        if (!this.dictionary.data.Pg) {\r\n          this.dictionary.data.Pg = pageRef;\r\n        }\r\n\r\n        if (this.dictionary.data.Pg === pageRef) {\r\n          this.dictionary.data.K.push(mcid);\r\n        } else {\r\n          this.dictionary.data.K.push({\r\n            Type: \"MCR\",\r\n            Pg: pageRef,\r\n            MCID: mcid\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n\r\nexport default PDFStructureElement;\r\n", "/*\r\nPDFNumberTree - represents a number tree object\r\n*/\r\n\r\nimport PDFTree from \"./tree\";\r\n\r\nclass PDFNumberTree extends PDFTree {\r\n  _compareKeys(a, b) {\r\n    return parseInt(a) - parseInt(b);\r\n  }\r\n\r\n  _keysName() {\r\n    return \"Nums\";\r\n  }\r\n\r\n  _dataForKey(k) {\r\n    return parseInt(k);\r\n  }\r\n}\r\n\r\nexport default PDFNumberTree;\r\n", "/*\r\nMarkings mixin - support marked content sequences in content streams\r\nBy <PERSON>\r\n*/\r\n\r\nimport PDFStructureElement from \"../structure_element\";\r\nimport PDFStructureContent from \"../structure_content\";\r\nimport PDFNumberTree from \"../number_tree\";\r\nimport PDFObject from \"../object\";\r\n\r\nexport default {\r\n\r\n  initMarkings(options) {\r\n    this.structChildren = [];\r\n\r\n    if (options.tagged) {\r\n      this.getMarkInfoDictionary().data.Marked = true;\r\n      this.getStructTreeRoot();\r\n    }\r\n  },\r\n\r\n  markContent(tag, options = null) {\r\n    if (tag === 'Artifact' || (options && options.mcid)) {\r\n      let toClose = 0;\r\n      this.page.markings.forEach((marking) => {\r\n        if (toClose || marking.structContent || marking.tag === 'Artifact') {\r\n          toClose++;\r\n        }\r\n      });\r\n      while (toClose--) {\r\n        this.endMarkedContent();\r\n      }\r\n    }\r\n\r\n    if (!options) {\r\n      this.page.markings.push({ tag });\r\n      this.addContent(`/${tag} BMC`);\r\n      return this;\r\n    }\r\n\r\n    this.page.markings.push({ tag, options });\r\n\r\n    const dictionary = {};\r\n\r\n    if (typeof options.mcid !== 'undefined') {\r\n      dictionary.MCID = options.mcid;\r\n    }\r\n    if (tag === 'Artifact') {\r\n      if (typeof options.type === 'string') {\r\n        dictionary.Type = options.type;\r\n      }\r\n      if (Array.isArray(options.bbox)) {\r\n        dictionary.BBox = [options.bbox[0], this.page.height - options.bbox[3],\r\n          options.bbox[2], this.page.height - options.bbox[1]];\r\n      }\r\n      if (Array.isArray(options.attached) &&\r\n        options.attached.every(val => typeof val === 'string')) {\r\n        dictionary.Attached = options.attached;\r\n      }\r\n    }\r\n    if (tag === 'Span') {\r\n      if (options.lang) {\r\n        dictionary.Lang = new String(options.lang);\r\n      }\r\n      if (options.alt) {\r\n        dictionary.Alt = new String(options.alt);\r\n      }\r\n      if (options.expanded) {\r\n        dictionary.E = new String(options.expanded);\r\n      }\r\n      if (options.actual) {\r\n        dictionary.ActualText = new String(options.actual);\r\n      }\r\n    }\r\n\r\n    this.addContent(`/${tag} ${PDFObject.convert(dictionary)} BDC`);\r\n    return this;\r\n  },\r\n\r\n  markStructureContent(tag, options = {}) {\r\n    const pageStructParents = this.getStructParentTree().get(this.page.structParentTreeKey);\r\n    const mcid = pageStructParents.length;\r\n    pageStructParents.push(null);\r\n\r\n    this.markContent(tag, { ...options, mcid });\r\n\r\n    const structContent = new PDFStructureContent(this.page.dictionary, mcid);\r\n    this.page.markings.slice(-1)[0].structContent = structContent;\r\n    return structContent;\r\n  },\r\n\r\n  endMarkedContent() {\r\n    this.page.markings.pop();\r\n    this.addContent('EMC');\r\n    return this;\r\n  },\r\n\r\n  struct(type, options = {}, children = null) {\r\n    return new PDFStructureElement(this, type, options, children);\r\n  },\r\n\r\n  addStructure(structElem) {\r\n    const structTreeRoot = this.getStructTreeRoot();\r\n    structElem.setParent(structTreeRoot);\r\n    structElem.setAttached();\r\n    this.structChildren.push(structElem);\r\n    if (!structTreeRoot.data.K) {\r\n      structTreeRoot.data.K = [];\r\n    }\r\n    structTreeRoot.data.K.push(structElem.dictionary);\r\n    return this;\r\n  },\r\n\r\n  initPageMarkings(pageMarkings) {\r\n    pageMarkings.forEach((marking) => {\r\n      if (marking.structContent) {\r\n        const structContent = marking.structContent;\r\n        const newStructContent = this.markStructureContent(marking.tag, marking.options);\r\n        structContent.push(newStructContent);\r\n        this.page.markings.slice(-1)[0].structContent = structContent;\r\n      } else {\r\n        this.markContent(marking.tag, marking.options);\r\n      }\r\n    });\r\n  },\r\n\r\n  endPageMarkings(page) {\r\n    const pageMarkings = page.markings;\r\n    pageMarkings.forEach(() => page.write('EMC'));\r\n    page.markings = [];\r\n    return pageMarkings;\r\n  },\r\n\r\n  getMarkInfoDictionary() {\r\n    if (!this._root.data.MarkInfo) {\r\n      this._root.data.MarkInfo = this.ref({});\r\n    }\r\n    return this._root.data.MarkInfo;\r\n  },\r\n\r\n  getStructTreeRoot() {\r\n    if (!this._root.data.StructTreeRoot) {\r\n      this._root.data.StructTreeRoot = this.ref({\r\n        Type: 'StructTreeRoot',\r\n        ParentTree: new PDFNumberTree(),\r\n        ParentTreeNextKey: 0\r\n      });\r\n    }\r\n    return this._root.data.StructTreeRoot;\r\n  },\r\n\r\n  getStructParentTree() {\r\n    return this.getStructTreeRoot().data.ParentTree;\r\n  },\r\n\r\n  createStructParentTreeNextKey() {\r\n    // initialise the MarkInfo dictionary\r\n    this.getMarkInfoDictionary();\r\n\r\n    const structTreeRoot = this.getStructTreeRoot();\r\n    const key = structTreeRoot.data.ParentTreeNextKey++;\r\n    structTreeRoot.data.ParentTree.add(key, []);\r\n    return key;\r\n  },\r\n\r\n  endMarkings() {\r\n    const structTreeRoot = this._root.data.StructTreeRoot;\r\n    if (structTreeRoot) {\r\n      structTreeRoot.end();\r\n      this.structChildren.forEach((structElem) => structElem.end());\r\n    }\r\n    if (this._root.data.MarkInfo) {\r\n      this._root.data.MarkInfo.end();\r\n    }\r\n  }\r\n\r\n};\r\n", "const FIELD_FLAGS = {\r\n  readOnly: 1,\r\n  required: 2,\r\n  noExport: 4,\r\n  multiline: 0x1000,\r\n  password: 0x2000,\r\n  toggleToOffButton: 0x4000,\r\n  radioButton: 0x8000,\r\n  pushButton: 0x10000,\r\n  combo: 0x20000,\r\n  edit: 0x40000,\r\n  sort: 0x80000,\r\n  multiSelect: 0x200000,\r\n  noSpell: 0x400000\r\n};\r\nconst FIELD_JUSTIFY = {\r\n  left: 0,\r\n  center: 1,\r\n  right: 2\r\n};\r\nconst VALUE_MAP = { value: 'V', defaultValue: 'DV' };\r\nconst FORMAT_SPECIAL = {\r\n  zip: '0',\r\n  zipPlus4: '1',\r\n  zip4: '1',\r\n  phone: '2',\r\n  ssn: '3'\r\n};\r\nconst FORMAT_DEFAULT = {\r\n  number: {\r\n    nDec: 0,\r\n    sepComma: false,\r\n    negStyle: 'MinusBlack',\r\n    currency: '',\r\n    currencyPrepend: true\r\n  },\r\n  percent: {\r\n    nDec: 0,\r\n    sepComma: false\r\n  }\r\n};\r\n\r\nexport default {\r\n  /**\r\n   * Must call if adding AcroForms to a document. Must also call font() before\r\n   * this method to set the default font.\r\n   */\r\n  initForm() {\r\n    if (!this._font) {\r\n      throw new Error('Must set a font before calling initForm method');\r\n    }\r\n    this._acroform = {\r\n      fonts: {},\r\n      defaultFont: this._font.name\r\n    };\r\n    this._acroform.fonts[this._font.id] = this._font.ref();\r\n\r\n    let data = {\r\n      Fields: [],\r\n      NeedAppearances: true,\r\n      DA: new String(`/${this._font.id} 0 Tf 0 g`),\r\n      DR: {\r\n        Font: {}\r\n      }\r\n    };\r\n    data.DR.Font[this._font.id] = this._font.ref();\r\n    const AcroForm = this.ref(data);\r\n    this._root.data.AcroForm = AcroForm;\r\n    return this;\r\n  },\r\n\r\n  /**\r\n   * Called automatically by document.js\r\n   */\r\n  endAcroForm() {\r\n    if (this._root.data.AcroForm) {\r\n      if (\r\n        !Object.keys(this._acroform.fonts).length &&\r\n        !this._acroform.defaultFont\r\n      ) {\r\n        throw new Error('No fonts specified for PDF form');\r\n      }\r\n      let fontDict = this._root.data.AcroForm.data.DR.Font;\r\n      Object.keys(this._acroform.fonts).forEach(name => {\r\n        fontDict[name] = this._acroform.fonts[name];\r\n      });\r\n      this._root.data.AcroForm.data.Fields.forEach(fieldRef => {\r\n        this._endChild(fieldRef);\r\n      });\r\n      this._root.data.AcroForm.end();\r\n    }\r\n    return this;\r\n  },\r\n\r\n  _endChild(ref) {\r\n    if (Array.isArray(ref.data.Kids)) {\r\n      ref.data.Kids.forEach(childRef => {\r\n        this._endChild(childRef);\r\n      });\r\n      ref.end();\r\n    }\r\n    return this;\r\n  },\r\n\r\n  /**\r\n   * Creates and adds a form field to the document. Form fields are intermediate\r\n   * nodes in a PDF form that are used to specify form name heirarchy and form\r\n   * value defaults.\r\n   * @param {string} name - field name (T attribute in field dictionary)\r\n   * @param {object} options  - other attributes to include in field dictionary\r\n   */\r\n  formField(name, options = {}) {\r\n    let fieldDict = this._fieldDict(name, null, options);\r\n    let fieldRef = this.ref(fieldDict);\r\n    this._addToParent(fieldRef);\r\n    return fieldRef;\r\n  },\r\n\r\n  /**\r\n   * Creates and adds a Form Annotation to the document. Form annotations are\r\n   * called Widget annotations internally within a PDF file.\r\n   * @param {string} name - form field name (T attribute of widget annotation\r\n   * dictionary)\r\n   * @param {number} x\r\n   * @param {number} y\r\n   * @param {number} w\r\n   * @param {number} h\r\n   * @param {object} options\r\n   */\r\n  formAnnotation(name, type, x, y, w, h, options = {}) {\r\n    let fieldDict = this._fieldDict(name, type, options);\r\n    fieldDict.Subtype = 'Widget';\r\n    if (fieldDict.F === undefined) {\r\n      fieldDict.F = 4; // print the annotation\r\n    }\r\n\r\n    // Add Field annot to page, and get it's ref\r\n    this.annotate(x, y, w, h, fieldDict);\r\n    let annotRef = this.page.annotations[this.page.annotations.length - 1];\r\n\r\n    return this._addToParent(annotRef);\r\n  },\r\n\r\n  formText(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'text', x, y, w, h, options);\r\n  },\r\n\r\n  formPushButton(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'pushButton', x, y, w, h, options);\r\n  },\r\n\r\n  formCombo(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'combo', x, y, w, h, options);\r\n  },\r\n\r\n  formList(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'list', x, y, w, h, options);\r\n  },\r\n\r\n  formRadioButton(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'radioButton', x, y, w, h, options);\r\n  },\r\n\r\n  formCheckbox(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'checkbox', x, y, w, h, options);\r\n  },\r\n\r\n  _addToParent(fieldRef) {\r\n    let parent = fieldRef.data.Parent;\r\n    if (parent) {\r\n      if (!parent.data.Kids) {\r\n        parent.data.Kids = [];\r\n      }\r\n      parent.data.Kids.push(fieldRef);\r\n    } else {\r\n      this._root.data.AcroForm.data.Fields.push(fieldRef);\r\n    }\r\n    return this;\r\n  },\r\n\r\n  _fieldDict(name, type, options = {}) {\r\n    if (!this._acroform) {\r\n      throw new Error(\r\n        'Call document.initForms() method before adding form elements to document'\r\n      );\r\n    }\r\n    let opts = Object.assign({}, options);\r\n    if (type !== null) {\r\n      opts = this._resolveType(type, options);\r\n    }\r\n    opts = this._resolveFlags(opts);\r\n    opts = this._resolveJustify(opts);\r\n    opts = this._resolveFont(opts);\r\n    opts = this._resolveStrings(opts);\r\n    opts = this._resolveColors(opts);\r\n    opts = this._resolveFormat(opts);\r\n    opts.T = new String(name);\r\n    if (opts.parent) {\r\n      opts.Parent = opts.parent;\r\n      delete opts.parent;\r\n    }\r\n    return opts;\r\n  },\r\n\r\n  _resolveType(type, opts) {\r\n    if (type === 'text') {\r\n      opts.FT = 'Tx';\r\n    } else if (type === 'pushButton') {\r\n      opts.FT = 'Btn';\r\n      opts.pushButton = true;\r\n    } else if (type === 'radioButton') {\r\n      opts.FT = 'Btn';\r\n      opts.radioButton = true;\r\n    } else if (type === 'checkbox') {\r\n      opts.FT = 'Btn';\r\n    } else if (type === 'combo') {\r\n      opts.FT = 'Ch';\r\n      opts.combo = true;\r\n    } else if (type === 'list') {\r\n      opts.FT = 'Ch';\r\n    } else {\r\n      throw new Error(`Invalid form annotation type '${type}'`);\r\n    }\r\n    return opts;\r\n  },\r\n\r\n  _resolveFormat(opts) {\r\n    const f = opts.format;\r\n    if (f && f.type) {\r\n      let fnKeystroke;\r\n      let fnFormat;\r\n      let params = '';\r\n      if (FORMAT_SPECIAL[f.type] !== undefined) {\r\n        fnKeystroke = `AFSpecial_Keystroke`;\r\n        fnFormat = `AFSpecial_Format`;\r\n        params = FORMAT_SPECIAL[f.type];\r\n      } else {\r\n        let format = f.type.charAt(0).toUpperCase() + f.type.slice(1);\r\n        fnKeystroke = `AF${format}_Keystroke`;\r\n        fnFormat = `AF${format}_Format`;\r\n\r\n        if (f.type === 'date') {\r\n          fnKeystroke += 'Ex';\r\n          params = String(f.param);\r\n        } else if (f.type === 'time') {\r\n          params = String(f.param);\r\n        } else if (f.type === 'number') {\r\n          let p = Object.assign({}, FORMAT_DEFAULT.number, f);\r\n          params = String(\r\n            [\r\n              String(p.nDec),\r\n              p.sepComma ? '0' : '1',\r\n              '\"' + p.negStyle + '\"',\r\n              'null',\r\n              '\"' + p.currency + '\"',\r\n              String(p.currencyPrepend)\r\n            ].join(',')\r\n          );\r\n        } else if (f.type === 'percent') {\r\n          let p = Object.assign({}, FORMAT_DEFAULT.percent, f);\r\n          params = String([String(p.nDec), p.sepComma ? '0' : '1'].join(','));\r\n        }\r\n      }\r\n      opts.AA = opts.AA ? opts.AA : {};\r\n      opts.AA.K = {\r\n        S: 'JavaScript',\r\n        JS: new String(`${fnKeystroke}(${params});`)\r\n      };\r\n      opts.AA.F = {\r\n        S: 'JavaScript',\r\n        JS: new String(`${fnFormat}(${params});`)\r\n      };\r\n    }\r\n    delete opts.format;\r\n    return opts;\r\n  },\r\n\r\n  _resolveColors(opts) {\r\n    let color = this._normalizeColor(opts.backgroundColor);\r\n    if (color) {\r\n      if (!opts.MK) {\r\n        opts.MK = {};\r\n      }\r\n      opts.MK.BG = color;\r\n    }\r\n    color = this._normalizeColor(opts.borderColor);\r\n    if (color) {\r\n      if (!opts.MK) {\r\n        opts.MK = {};\r\n      }\r\n      opts.MK.BC = color;\r\n    }\r\n    delete opts.backgroundColor;\r\n    delete opts.borderColor;\r\n    return opts;\r\n  },\r\n\r\n  _resolveFlags(options) {\r\n    let result = 0;\r\n    Object.keys(options).forEach(key => {\r\n      if (FIELD_FLAGS[key]) {\r\n        if (options[key]) {\r\n          result |= FIELD_FLAGS[key];\r\n        }\r\n        delete options[key];\r\n      }\r\n    });\r\n    if (result !== 0) {\r\n      options.Ff = options.Ff ? options.Ff : 0;\r\n      options.Ff |= result;\r\n    }\r\n    return options;\r\n  },\r\n\r\n  _resolveJustify(options) {\r\n    let result = 0;\r\n    if (options.align !== undefined) {\r\n      if (typeof FIELD_JUSTIFY[options.align] === 'number') {\r\n        result = FIELD_JUSTIFY[options.align];\r\n      }\r\n      delete options.align;\r\n    }\r\n    if (result !== 0) {\r\n      options.Q = result; // default\r\n    }\r\n    return options;\r\n  },\r\n\r\n  _resolveFont(options) {\r\n    // add current font to document-level AcroForm dict if necessary\r\n    if (this._acroform.fonts[this._font.id] === null) {\r\n      this._acroform.fonts[this._font.id] = this._font.ref();\r\n    }\r\n\r\n    // add current font to field's resource dict (RD) if not the default acroform font\r\n    if (this._acroform.defaultFont !== this._font.name) {\r\n      options.DR = { Font: {} };\r\n\r\n      // Get the fontSize option. If not set use auto sizing\r\n      const fontSize = options.fontSize || 0;\r\n\r\n      options.DR.Font[this._font.id] = this._font.ref();\r\n      options.DA = new String(`/${this._font.id} ${fontSize} Tf 0 g`);\r\n    }\r\n    return options;\r\n  },\r\n\r\n  _resolveStrings(options) {\r\n    let select = [];\r\n    function appendChoices(a) {\r\n      if (Array.isArray(a)) {\r\n        for (let idx = 0; idx < a.length; idx++) {\r\n          if (typeof a[idx] === 'string') {\r\n            select.push(new String(a[idx]));\r\n          } else {\r\n            select.push(a[idx]);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    appendChoices(options.Opt);\r\n    if (options.select) {\r\n      appendChoices(options.select);\r\n      delete options.select;\r\n    }\r\n    if (select.length) {\r\n      options.Opt = select;\r\n    }\r\n\r\n    Object.keys(VALUE_MAP).forEach(key => {\r\n      if (options[key] !== undefined) {\r\n        options[VALUE_MAP[key]] = options[key];\r\n        delete options[key];\r\n      }\r\n    });\r\n    ['V', 'DV'].forEach(key => {\r\n      if (typeof options[key] === 'string') {\r\n        options[key] = new String(options[key]);\r\n      }\r\n    });\r\n\r\n    if (options.MK && options.MK.CA) {\r\n      options.MK.CA = new String(options.MK.CA);\r\n    }\r\n    if (options.label) {\r\n      options.MK = options.MK ? options.MK : {};\r\n      options.MK.CA = new String(options.label);\r\n      delete options.label;\r\n    }\r\n    return options;\r\n  }\r\n};\r\n", "import fs from 'fs';\r\nimport CryptoJS from 'crypto-js';\r\n\r\nexport default {\r\n  /**\r\n   * Embed contents of `src` in PDF\r\n   * @param {Buffer | ArrayBuffer | string} src input Buffer, ArrayBuffer, base64 encoded string or path to file\r\n   * @param {object} options\r\n   *  * options.name: filename to be shown in PDF, will use `src` if none set\r\n   *  * options.type: filetype to be shown in PDF\r\n   *  * options.description: description to be shown in PDF\r\n   *  * options.hidden: if true, do not add attachment to EmbeddedFiles dictionary. Useful for file attachment annotations\r\n   *  * options.creationDate: override creation date\r\n   *  * options.modifiedDate: override modified date\r\n   * @returns filespec reference\r\n   */\r\n  file(src, options = {}) {\r\n    options.name = options.name || src;\r\n\r\n    const refBody = {\r\n      Type: 'EmbeddedFile',\r\n      Params: {}\r\n    };\r\n    let data;\r\n\r\n    if (!src) {\r\n      throw new Error('No src specified');\r\n    }\r\n    if (Buffer.isBuffer(src)) {\r\n      data = src;\r\n    } else if (src instanceof ArrayBuffer) {\r\n      data = Buffer.from(new Uint8Array(src));\r\n    } else {\r\n      let match;\r\n      if ((match = /^data:(.*?);base64,(.*)$/.exec(src))) {\r\n        if (match[1]) {\r\n          refBody.Subtype = match[1].replace('/', '#2F');\r\n        }\r\n        data = Buffer.from(match[2], 'base64');\r\n      } else {\r\n        data = fs.readFileSync(src);\r\n        if (!data) {\r\n          throw new Error(`Could not read contents of file at filepath ${src}`);\r\n        }\r\n\r\n        // update CreationDate and ModDate\r\n        const { birthtime, ctime } = fs.statSync(src);\r\n        refBody.Params.CreationDate = birthtime;\r\n        refBody.Params.ModDate = ctime;\r\n      }\r\n    }\r\n\r\n    // override creation date and modified date\r\n    if (options.creationDate instanceof Date) {\r\n      refBody.Params.CreationDate = options.creationDate;\r\n    }\r\n    if (options.modifiedDate instanceof Date) {\r\n      refBody.Params.ModDate = options.modifiedDate;\r\n    }\r\n    // add optional subtype\r\n    if (options.type) {\r\n      refBody.Subtype = options.type.replace('/', '#2F');\r\n    }\r\n\r\n    // add checksum and size information\r\n    const checksum = CryptoJS.MD5(\r\n      CryptoJS.lib.WordArray.create(new Uint8Array(data))\r\n    );\r\n    refBody.Params.CheckSum = new String(checksum);\r\n    refBody.Params.Size = data.byteLength;\r\n\r\n    // save some space when embedding the same file again\r\n    // if a file with the same name and metadata exists, reuse its reference\r\n    let ref;\r\n    if (!this._fileRegistry) this._fileRegistry = {};\r\n    let file = this._fileRegistry[options.name];\r\n    if (file && isEqual(refBody, file)) {\r\n      ref = file.ref;\r\n    } else {\r\n      ref = this.ref(refBody);\r\n      ref.end(data);\r\n\r\n      this._fileRegistry[options.name] = { ...refBody, ref };\r\n    }\r\n    // add filespec for embedded file\r\n    const fileSpecBody = {\r\n      Type: 'Filespec',\r\n      F: new String(options.name),\r\n      EF: { F: ref },\r\n      UF: new String(options.name)\r\n    };\r\n    if (options.description) {\r\n      fileSpecBody.Desc = new String(options.description);\r\n    }\r\n    const filespec = this.ref(fileSpecBody);\r\n    filespec.end();\r\n\r\n    if (!options.hidden) {\r\n      this.addNamedEmbeddedFile(options.name, filespec);\r\n    }\r\n\r\n    return filespec;\r\n  }\r\n};\r\n\r\n/** check two embedded file metadata objects for equality */\r\nfunction isEqual(a, b) {\r\n  return (\r\n    a.Subtype === b.Subtype &&\r\n    a.Params.CheckSum.toString() === b.Params.CheckSum.toString() &&\r\n    a.Params.Size === b.Params.Size &&\r\n    a.Params.CreationDate === b.Params.CreationDate &&\r\n    a.Params.ModDate === b.Params.ModDate\r\n  );\r\n}\r\n", "import fs from 'fs';\r\n\r\nexport default {\r\n\r\n    initPDFA(pSubset) {\r\n        if (pSubset.charAt(pSubset.length - 3) === '-') {\r\n            this.subset_conformance = pSubset.charAt(pSubset.length - 1).toUpperCase();\r\n            this.subset = parseInt(pSubset.charAt(pSubset.length - 2));\r\n        } else {\r\n            // Default to Basic conformance when user doesn't specify\r\n            this.subset_conformance = 'B';\r\n            this.subset = parseInt(pSubset.charAt(pSubset.length - 1));\r\n        }\r\n    },\r\n\r\n    endSubset() {\r\n        this._addPdfaMetadata();\r\n        const jsPath = `${__dirname}/data/sRGB_IEC61966_2_1.icc`\r\n        const jestPath = `${__dirname}/../color_profiles/sRGB_IEC61966_2_1.icc`\r\n        this._addColorOutputIntent(fs.existsSync(jsPath) ? jsPath : jestPath);\r\n    },\r\n\r\n    _addColorOutputIntent(pICCPath) {\r\n        const iccProfile = fs.readFileSync(pICCPath);\r\n\r\n        const colorProfileRef = this.ref({\r\n            Length: iccProfile.length,\r\n            N: 3\r\n        });\r\n        colorProfileRef.write(iccProfile);\r\n        colorProfileRef.end();\r\n\r\n        const intentRef = this.ref({\r\n            Type: 'OutputIntent',\r\n            S: 'GTS_PDFA1',\r\n            Info: new String('sRGB IEC61966-2.1'),\r\n            OutputConditionIdentifier: new String('sRGB IEC61966-2.1'),\r\n            DestOutputProfile: colorProfileRef,\r\n        });\r\n        intentRef.end();\r\n\r\n        this._root.data.OutputIntents = [intentRef];\r\n    },\r\n\r\n    _getPdfaid() {\r\n        return `\r\n        <rdf:Description xmlns:pdfaid=\"http://www.aiim.org/pdfa/ns/id/\" rdf:about=\"\">\r\n            <pdfaid:part>${this.subset}</pdfaid:part>\r\n            <pdfaid:conformance>${this.subset_conformance}</pdfaid:conformance>\r\n        </rdf:Description>\r\n        `;\r\n    },\r\n\r\n    _addPdfaMetadata() {\r\n        this.appendXML(this._getPdfaid());\r\n    },\r\n\r\n}", "\r\nexport default {\r\n\r\n    initPDFUA() {\r\n        this.subset = 1;\r\n    },\r\n\r\n    endSubset() {\r\n        this._addPdfuaMetadata();\r\n    },\r\n\r\n    _addPdfuaMetadata() {\r\n        this.appendXML(this._getPdfuaid());\r\n    },\r\n\r\n    _getPdfuaid() {\r\n        return `\r\n        <rdf:Description xmlns:pdfuaid=\"http://www.aiim.org/pdfua/ns/id/\" rdf:about=\"\">\r\n            <pdfuaid:part>${this.subset}</pdfuaid:part>\r\n        </rdf:Description>\r\n        `;\r\n    },\r\n\r\n}", "import PDFA from './pdfa';\r\nimport PDFUA from './pdfua';\r\n\r\nexport default {\r\n    _importSubset(subset) {\r\n        Object.assign(this, subset)\r\n    },\r\n\r\n    initSubset(options) {\r\n\r\n        switch (options.subset) {\r\n            case 'PDF/A-1':\r\n            case 'PDF/A-1a':\r\n            case 'PDF/A-1b':\r\n            case 'PDF/A-2':\r\n            case 'PDF/A-2a':\r\n            case 'PDF/A-2b':\r\n            case 'PDF/A-3':\r\n            case 'PDF/A-3a':\r\n            case 'PDF/A-3b':\r\n                this._importSubset(PDFA);\r\n                this.initPDFA(options.subset);\r\n                break;\r\n            case 'PDF/UA':\r\n                this._importSubset(PDFUA);\r\n                this.initPDFUA();\r\n                break;\r\n        }\r\n    }\r\n}", "\r\nclass PDFMetadata {\r\n    constructor() {\r\n        this._metadata = `\r\n        <?xpacket begin=\"\\ufeff\" id=\"W5M0MpCehiHzreSzNTczkc9d\"?>\r\n            <x:xmpmeta xmlns:x=\"adobe:ns:meta/\">\r\n                <rdf:RDF xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\r\n        `;\r\n    }\r\n    \r\n    _closeTags() {\r\n        this._metadata = this._metadata.concat(`\r\n                </rdf:RDF>\r\n            </x:xmpmeta>\r\n        <?xpacket end=\"w\"?>\r\n        `);\r\n    }\r\n\r\n    append(xml, newline=true) {\r\n        this._metadata = this._metadata.concat(xml); \r\n        if (newline)\r\n            this._metadata = this._metadata.concat('\\n'); \r\n    }\r\n\r\n    getXML() { return this._metadata; }\r\n\r\n    getLength() { return this._metadata.length; }\r\n\r\n    end() {\r\n        this._closeTags();\r\n        this._metadata = this._metadata.trim();\r\n    }\r\n}\r\n\r\nexport default PDFMetadata;", "import PDFMetadata from \"../metadata\"\r\n\r\nexport default {\r\n    initMetadata() {\r\n        this.metadata = new PDFMetadata();\r\n    },\r\n\r\n    appendXML(xml, newline=true) { this.metadata.append(xml,newline); },\r\n\r\n    _addInfo() {\r\n        this.appendXML(`\r\n        <rdf:Description rdf:about=\"\" xmlns:xmp=\"http://ns.adobe.com/xap/1.0/\">\r\n            <xmp:CreateDate>${this.info.CreationDate.toISOString().split('.')[0]+\"Z\"}</xmp:CreateDate>\r\n            <xmp:CreatorTool>${this.info.Creator}</xmp:CreatorTool>\r\n        </rdf:Description>\r\n        `\r\n        );\r\n\r\n        if (this.info.Title || this.info.Author || this.info.Subject) {\r\n            this.appendXML(`\r\n            <rdf:Description rdf:about=\"\" xmlns:dc=\"http://purl.org/dc/elements/1.1/\">\r\n            `);\r\n            \r\n            if (this.info.Title) {\r\n                this.appendXML(`\r\n                <dc:title>\r\n                    <rdf:Alt>\r\n                        <rdf:li xml:lang=\"x-default\">${this.info.Title}</rdf:li>\r\n                    </rdf:Alt>\r\n                </dc:title>\r\n                `);\r\n            }\r\n\r\n            if (this.info.Author) {\r\n                this.appendXML(`\r\n                <dc:creator>\r\n                    <rdf:Seq>\r\n                        <rdf:li>${this.info.Author}</rdf:li>\r\n                    </rdf:Seq>\r\n                </dc:creator>\r\n                `);\r\n            }\r\n\r\n            if (this.info.Subject) {\r\n                this.appendXML(`\r\n                <dc:description>\r\n                    <rdf:Alt>\r\n                        <rdf:li xml:lang=\"x-default\">${this.info.Subject}</rdf:li>\r\n                    </rdf:Alt>\r\n                </dc:description>\r\n                `);\r\n            }\r\n\r\n            this.appendXML(`\r\n            </rdf:Description>\r\n            `);\r\n        }\r\n\r\n        this.appendXML(`\r\n        <rdf:Description rdf:about=\"\" xmlns:pdf=\"http://ns.adobe.com/pdf/1.3/\">\r\n            <pdf:Producer>${this.info.Creator}</pdf:Producer>`, false);\r\n\r\n        if (this.info.Keywords) {\r\n            this.appendXML(`\r\n            <pdf:Keywords>${this.info.Keywords}</pdf:Keywords>`, false);\r\n        }\r\n\r\n        this.appendXML(`\r\n        </rdf:Description>\r\n        `);\r\n    },\r\n\r\n    endMetadata() {\r\n        this._addInfo();\r\n    \r\n        this.metadata.end();\r\n\r\n        /*\r\n        Metadata was introduced in PDF 1.4, so adding it to 1.3 \r\n        will likely only take up more space.\r\n        */\r\n        if (this.version != 1.3) {\r\n            this.metadataRef = this.ref({\r\n                length: this.metadata.getLength(),\r\n                Type: 'Metadata',\r\n                Subtype: 'XML'\r\n            });\r\n            this.metadataRef.compress = false;\r\n            this.metadataRef.write(Buffer.from(this.metadata.getXML(), 'utf-8'));\r\n            this.metadataRef.end();\r\n            this._root.data.Metadata = this.metadataRef;\r\n        }\r\n    }\r\n}", "/*\r\nPDFDocument - represents an entire PDF document\r\nBy <PERSON>\r\n*/\r\n\r\nimport stream from 'stream';\r\nimport fs from 'fs';\r\nimport PDFObject from './object';\r\nimport PDFReference from './reference';\r\nimport PDFPage from './page';\r\nimport PDFNameTree from './name_tree';\r\nimport PDFSecurity from './security';\r\nimport ColorMixin from './mixins/color';\r\nimport VectorMixin from './mixins/vector';\r\nimport FontsMixin from './mixins/fonts';\r\nimport TextMixin from './mixins/text';\r\nimport ImagesMixin from './mixins/images';\r\nimport AnnotationsMixin from './mixins/annotations';\r\nimport OutlineMixin from './mixins/outline';\r\nimport MarkingsMixin from './mixins/markings';\r\nimport AcroFormMixin from './mixins/acroform';\r\nimport AttachmentsMixin from './mixins/attachments';\r\nimport LineWrapper from './line_wrapper';\r\nimport SubsetMixin from './mixins/subsets';\r\nimport MetadataMixin from './mixins/metadata';\r\n\r\nclass PDFDocument extends stream.Readable {\r\n  constructor(options = {}) {\r\n    super(options);\r\n    this.options = options;\r\n\r\n    // PDF version\r\n    switch (options.pdfVersion) {\r\n      case '1.4':\r\n        this.version = 1.4;\r\n        break;\r\n      case '1.5':\r\n        this.version = 1.5;\r\n        break;\r\n      case '1.6':\r\n        this.version = 1.6;\r\n        break;\r\n      case '1.7':\r\n      case '1.7ext3':\r\n        this.version = 1.7;\r\n        break;\r\n      default:\r\n        this.version = 1.3;\r\n        break;\r\n    }\r\n\r\n    // Whether streams should be compressed\r\n    this.compress =\r\n      this.options.compress != null ? this.options.compress : true;\r\n\r\n    this._pageBuffer = [];\r\n    this._pageBufferStart = 0;\r\n\r\n    // The PDF object store\r\n    this._offsets = [];\r\n    this._waiting = 0;\r\n    this._ended = false;\r\n    this._offset = 0;\r\n    const Pages = this.ref({\r\n      Type: 'Pages',\r\n      Count: 0,\r\n      Kids: []\r\n    });\r\n\r\n    const Names = this.ref({\r\n      Dests: new PDFNameTree()\r\n    });\r\n\r\n    this._root = this.ref({\r\n      Type: 'Catalog',\r\n      Pages,\r\n      Names\r\n    });\r\n\r\n    if (this.options.lang) {\r\n      this._root.data.Lang = new String(this.options.lang);\r\n    }\r\n\r\n    // The current page\r\n    this.page = null;\r\n\r\n    // Initialize mixins\r\n    this.initMetadata();\r\n    this.initColor();\r\n    this.initVector();\r\n    this.initFonts(options.font);\r\n    this.initText();\r\n    this.initImages();\r\n    this.initOutline();\r\n    this.initMarkings(options);\r\n    this.initSubset(options);\r\n\r\n    // Initialize the metadata\r\n    this.info = {\r\n      Producer: 'PDFKit',\r\n      Creator: 'PDFKit',\r\n      CreationDate: new Date()\r\n    };\r\n\r\n    if (this.options.info) {\r\n      for (let key in this.options.info) {\r\n        const val = this.options.info[key];\r\n        this.info[key] = val;\r\n      }\r\n    }\r\n\r\n    if (this.options.displayTitle) {\r\n      this._root.data.ViewerPreferences = this.ref({\r\n        DisplayDocTitle: true\r\n      });\r\n    }\r\n\r\n    // Generate file ID\r\n    this._id = PDFSecurity.generateFileID(this.info);\r\n\r\n    // Initialize security settings\r\n    this._security = PDFSecurity.create(this, options);\r\n\r\n    // Write the header\r\n    // PDF version\r\n    this._write(`%PDF-${this.version}`);\r\n\r\n    // 4 binary chars, as recommended by the spec\r\n    this._write('%\\xFF\\xFF\\xFF\\xFF');\r\n\r\n    // Add the first page\r\n    if (this.options.autoFirstPage !== false) {\r\n      this.addPage();\r\n    }\r\n  }\r\n\r\n  addPage(options) {\r\n    if (options == null) {\r\n      ({ options } = this);\r\n    }\r\n\r\n    // end the current page if needed\r\n    if (!this.options.bufferPages) {\r\n      this.flushPages();\r\n    }\r\n\r\n    // create a page object\r\n    this.page = new PDFPage(this, options);\r\n    this._pageBuffer.push(this.page);\r\n\r\n    // add the page to the object store\r\n    const pages = this._root.data.Pages.data;\r\n    pages.Kids.push(this.page.dictionary);\r\n    pages.Count++;\r\n\r\n    // reset x and y coordinates\r\n    this.x = this.page.margins.left;\r\n    this.y = this.page.margins.top;\r\n\r\n    // flip PDF coordinate system so that the origin is in\r\n    // the top left rather than the bottom left\r\n    this._ctm = [1, 0, 0, 1, 0, 0];\r\n    this.transform(1, 0, 0, -1, 0, this.page.height);\r\n\r\n    this.emit('pageAdded');\r\n\r\n    return this;\r\n  }\r\n\r\n  continueOnNewPage(options) {\r\n    const pageMarkings = this.endPageMarkings(this.page);\r\n\r\n    this.addPage(options);\r\n\r\n    this.initPageMarkings(pageMarkings);\r\n\r\n    return this;\r\n  }\r\n\r\n  bufferedPageRange() {\r\n    return { start: this._pageBufferStart, count: this._pageBuffer.length };\r\n  }\r\n\r\n  switchToPage(n) {\r\n    let page;\r\n    if (!(page = this._pageBuffer[n - this._pageBufferStart])) {\r\n      throw new Error(\r\n        `switchToPage(${n}) out of bounds, current buffer covers pages ${\r\n          this._pageBufferStart\r\n        } to ${this._pageBufferStart + this._pageBuffer.length - 1}`\r\n      );\r\n    }\r\n\r\n    return (this.page = page);\r\n  }\r\n\r\n  flushPages() {\r\n    // this local variable exists so we're future-proof against\r\n    // reentrant calls to flushPages.\r\n    const pages = this._pageBuffer;\r\n    this._pageBuffer = [];\r\n    this._pageBufferStart += pages.length;\r\n    for (let page of pages) {\r\n      this.endPageMarkings(page);\r\n      page.end();\r\n    }\r\n  }\r\n\r\n  addNamedDestination(name, ...args) {\r\n    if (args.length === 0) {\r\n      args = ['XYZ', null, null, null];\r\n    }\r\n    if (args[0] === 'XYZ' && args[2] !== null) {\r\n      args[2] = this.page.height - args[2];\r\n    }\r\n    args.unshift(this.page.dictionary);\r\n    this._root.data.Names.data.Dests.add(name, args);\r\n  }\r\n\r\n  addNamedEmbeddedFile(name, ref) {\r\n    if (!this._root.data.Names.data.EmbeddedFiles) {\r\n      // disabling /Limits for this tree fixes attachments not showing in Adobe Reader\r\n      this._root.data.Names.data.EmbeddedFiles = new PDFNameTree({ limits: false });\r\n    }\r\n\r\n    // add filespec to EmbeddedFiles\r\n    this._root.data.Names.data.EmbeddedFiles.add(name, ref);\r\n  }\r\n\r\n  addNamedJavaScript(name, js) {\r\n    if (!this._root.data.Names.data.JavaScript) {\r\n      this._root.data.Names.data.JavaScript = new PDFNameTree();\r\n    }\r\n    let data = {\r\n      JS: new String(js),\r\n      S: 'JavaScript'\r\n    };\r\n    this._root.data.Names.data.JavaScript.add(name, data);\r\n  }\r\n\r\n  ref(data) {\r\n    const ref = new PDFReference(this, this._offsets.length + 1, data);\r\n    this._offsets.push(null); // placeholder for this object's offset once it is finalized\r\n    this._waiting++;\r\n    return ref;\r\n  }\r\n\r\n  _read() {}\r\n  // do nothing, but this method is required by node\r\n\r\n  _write(data) {\r\n    if (!Buffer.isBuffer(data)) {\r\n      data = Buffer.from(data + '\\n', 'binary');\r\n    }\r\n\r\n    this.push(data);\r\n    return (this._offset += data.length);\r\n  }\r\n\r\n  addContent(data) {\r\n    this.page.write(data);\r\n    return this;\r\n  }\r\n\r\n  _refEnd(ref) {\r\n    this._offsets[ref.id - 1] = ref.offset;\r\n    if (--this._waiting === 0 && this._ended) {\r\n      this._finalize();\r\n      return (this._ended = false);\r\n    }\r\n  }\r\n\r\n  write(filename, fn) {\r\n    // print a deprecation warning with a stacktrace\r\n    const err = new Error(`\\\r\nPDFDocument#write is deprecated, and will be removed in a future version of PDFKit. \\\r\nPlease pipe the document into a Node stream.\\\r\n`);\r\n\r\n    console.warn(err.stack);\r\n\r\n    this.pipe(fs.createWriteStream(filename));\r\n    this.end();\r\n    return this.once('end', fn);\r\n  }\r\n\r\n  end() {\r\n    this.flushPages();\r\n\r\n    this._info = this.ref();\r\n    for (let key in this.info) {\r\n      let val = this.info[key];\r\n      if (typeof val === 'string') {\r\n        val = new String(val);\r\n      }\r\n\r\n      let entry = this.ref(val);\r\n      entry.end();\r\n\r\n      this._info.data[key] = entry;\r\n    }\r\n\r\n    this._info.end();\r\n\r\n    for (let name in this._fontFamilies) {\r\n      const font = this._fontFamilies[name];\r\n      font.finalize();\r\n    }\r\n\r\n    this.endOutline();\r\n    this.endMarkings();\r\n\r\n    if (this.subset) {\r\n      this.endSubset();\r\n    }\r\n\r\n    this.endMetadata();\r\n\r\n    this._root.end();\r\n    this._root.data.Pages.end();\r\n    this._root.data.Names.end();\r\n    this.endAcroForm();\r\n\r\n    if (this._root.data.ViewerPreferences) {\r\n      this._root.data.ViewerPreferences.end();\r\n    }\r\n\r\n    if (this._security) {\r\n      this._security.end();\r\n    }\r\n\r\n    if (this._waiting === 0) {\r\n      return this._finalize();\r\n    } else {\r\n      return (this._ended = true);\r\n    }\r\n  }\r\n\r\n  _finalize() {\r\n    // generate xref\r\n    const xRefOffset = this._offset;\r\n    this._write('xref');\r\n    this._write(`0 ${this._offsets.length + 1}`);\r\n    this._write('0000000000 65535 f ');\r\n\r\n    for (let offset of this._offsets) {\r\n      offset = `0000000000${offset}`.slice(-10);\r\n      this._write(offset + ' 00000 n ');\r\n    }\r\n\r\n    // trailer\r\n    const trailer = {\r\n      Size: this._offsets.length + 1,\r\n      Root: this._root,\r\n      Info: this._info,\r\n      ID: [this._id, this._id]\r\n    };\r\n    if (this._security) {\r\n      trailer.Encrypt = this._security.dictionary;\r\n    }\r\n\r\n    this._write('trailer');\r\n    this._write(PDFObject.convert(trailer));\r\n\r\n    this._write('startxref');\r\n    this._write(`${xRefOffset}`);\r\n    this._write('%%EOF');\r\n\r\n    // end the stream\r\n    return this.push(null);\r\n  }\r\n\r\n  toString() {\r\n    return '[object PDFDocument]';\r\n  }\r\n}\r\n\r\nconst mixin = methods => {\r\n  Object.assign(PDFDocument.prototype, methods);\r\n};\r\n\r\nmixin(MetadataMixin);\r\nmixin(ColorMixin);\r\nmixin(VectorMixin);\r\nmixin(FontsMixin);\r\nmixin(TextMixin);\r\nmixin(ImagesMixin);\r\nmixin(AnnotationsMixin);\r\nmixin(OutlineMixin);\r\nmixin(MarkingsMixin);\r\nmixin(AcroFormMixin);\r\nmixin(AttachmentsMixin);\r\nmixin(SubsetMixin);\r\n\r\nPDFDocument.LineWrapper = LineWrapper;\r\n\r\nexport default PDFDocument;\r\n"], "names": ["PDFAbstractReference", "Error", "PDFTree", "options", "_items", "limits", "key", "val", "sortedKeys", "Object", "keys", "sort", "a", "b", "_compareKeys", "out", "length", "first", "last", "push", "PDFObject", "convert", "_dataForKey", "_keysName", "join", "pad", "str", "Array", "slice", "escapableRe", "escapable", "swapBytes", "buff", "l", "i", "end", "object", "encryptFn", "String", "string", "isUnicode", "charCodeAt", "stringBuffer", "<PERSON><PERSON><PERSON>", "from", "valueOf", "toString", "replace", "c", "<PERSON><PERSON><PERSON><PERSON>", "Date", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "isArray", "items", "map", "e", "call", "number", "n", "Math", "round", "PDFReference", "document", "id", "data", "gen", "compress", "Filter", "uncompressedLength", "buffer", "chunk", "Length", "write", "finalize", "offset", "_offset", "_security", "getEncryptFn", "concat", "zlib", "deflateSync", "_write", "_refEnd", "DEFAULT_MARGINS", "top", "left", "bottom", "right", "SIZES", "A0", "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9", "A10", "B0", "B1", "B2", "B3", "B4", "B5", "B6", "B7", "B8", "B9", "B10", "C0", "C1", "C2", "C3", "C4", "C5", "C6", "C7", "C8", "C9", "C10", "RA0", "RA1", "RA2", "RA3", "RA4", "SRA0", "SRA1", "SRA2", "SRA3", "SRA4", "EXECUTIVE", "FOLIO", "LEGAL", "LETTER", "TABLOID", "PDFPage", "size", "layout", "margin", "margins", "dimensions", "toUpperCase", "width", "height", "content", "ref", "resources", "ProcSet", "dictionary", "Type", "Parent", "_root", "Pages", "MediaBox", "Contents", "Resources", "markings", "Font", "XObject", "ExtGState", "Pattern", "ColorSpace", "Ann<PERSON>", "StructParents", "createStructParentTreeNextKey", "PDFNameTree", "localeCompare", "k", "inRange", "value", "rangeGroup", "startRange", "endRange", "<PERSON><PERSON><PERSON><PERSON>", "floor", "arrayIndex", "unassigned_code_points", "isUnassignedCodePoint", "character", "commonly_mapped_to_nothing", "isCommonlyMappedToNothing", "non_ASCII_space_characters", "isNonASCIISpaceCharacter", "non_ASCII_controls_characters", "non_character_codepoints", "prohibited_characters", "isProhibitedCharacter", "bidirectional_r_al", "isBidirectionalRAL", "bidirectional_l", "isBidirectionalL", "mapping2space", "mapping2nothing", "getCodePoint", "codePointAt", "x", "toCodePoints", "input", "codepoints", "before", "next", "saslprep", "opts", "TypeError", "mapped_input", "filter", "normalized_input", "fromCodePoint", "apply", "normalize", "normalized_map", "hasProhibited", "some", "allowUnassigned", "hasUnassigned", "hasBidiRAL", "hasBidiL", "isFirstBidiRAL", "isLastBidiRAL", "PDFSecurity", "info", "infoStr", "CreationDate", "getTime", "hasOwnProperty", "wordArrayToBuffer", "CryptoJS", "MD5", "bytes", "lib", "WordArray", "random", "ownerPassword", "userPassword", "_setupEncryption", "pdfVersion", "version", "encDict", "_setupEncryptionV1V2V4", "_setupEncryptionV5", "v", "r", "permissions", "keyBits", "getPermissionsR2", "getPermissionsR3", "paddedUserPassword", "processPasswordR2R3R4", "paddedOwnerPassword", "ownerPasswordEntry", "getOwnerPasswordR2R3R4", "<PERSON><PERSON><PERSON>", "getEncryptionKeyR2R3R4", "_id", "userPasswordEntry", "getUserPasswordR2", "getUserPasswordR3R4", "V", "CF", "StdCF", "AuthEvent", "CFM", "StmF", "StrF", "R", "O", "U", "P", "processedUserPassword", "processPasswordR5", "processedOwnerPassword", "getEncryptionKeyR5", "generateRandomWordArray", "getUserPasswordR5", "userKeySalt", "create", "words", "userEncryptionKeyEntry", "getUserEncryptionKeyR5", "getOwnerPasswordR5", "ownerKeySalt", "ownerEncryptionKeyEntry", "getOwnerEncryptionKeyR5", "permsEntry", "getEncryptedPermissionsR5", "OE", "UE", "Perms", "obj", "digest", "clone", "sigBytes", "min", "RC4", "encrypt", "ciphertext", "iv", "mode", "CBC", "padding", "Pkcs7", "AES", "permissionObject", "printing", "modifying", "copying", "annotating", "fillingForms", "contentAccessibility", "documentAssembly", "documentId", "cipher", "xorRound", "ceil", "j", "lsbFirstWord", "validationSalt", "keySalt", "SHA256", "NoPadding", "ECB", "password", "alloc", "index", "code", "PASSWORD_PADDING", "unescape", "encodeURIComponent", "wordArray", "byteArray", "PDFGradient", "doc", "stops", "embedded", "transform", "pos", "color", "opacity", "_normalizeColor", "_colorSpace", "max", "m11", "m12", "m21", "m22", "dx", "dy", "m", "fn", "<PERSON><PERSON><PERSON><PERSON>", "matrix", "bounds", "encode", "FunctionType", "Domain", "N", "Functions", "Bounds", "Encode", "_gradCount", "shader", "pattern", "PatternType", "Shading", "Matrix", "stop", "grad", "opacityGradient", "embed", "pageBBox", "page", "form", "Subtype", "FormType", "BBox", "Group", "S", "CS", "Sh1", "gstate", "SMask", "G", "opacityPattern", "PaintType", "TilingType", "XStep", "YStep", "Gs1", "patterns", "stroke", "_ctm", "m0", "m1", "m2", "m3", "m4", "m5", "_setColorSpace", "op", "addContent", "PDFLinearGradient", "x1", "y1", "x2", "y2", "ShadingType", "<PERSON><PERSON><PERSON>", "Function", "Extend", "PDFRadialGradient", "r1", "r2", "underlyingColorSpaces", "PDFTilingPattern", "bBox", "xStep", "yStep", "stream", "toFixed", "for<PERSON>ach", "csName", "csId", "getPatternColorSpaceId", "colorSpaces", "cs", "underlyingColorspace", "_patternCount", "createPattern", "patternColor", "embedPatternColorSpaces", "normalizedColor", "_getColorSpace", "Gradient", "initColor", "_opacityRegistry", "_opacityCount", "char<PERSON>t", "hex", "parseInt", "namedColors", "part", "_setColor", "_setColorCore", "space", "fillColor", "set", "fillOpacity", "_fillColor", "strokeColor", "strokeOpacity", "_doOpacity", "name", "ca", "CA", "ext_gstates", "linearGradient", "radialGradient", "bbox", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "grey", "green", "greenyellow", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "cx", "cy", "px", "py", "sx", "sy", "parameters", "A", "C", "H", "h", "L", "M", "Q", "q", "s", "T", "t", "Z", "z", "parse", "path", "cmd", "ret", "args", "curArg", "foundDecimal", "params", "includes", "commands", "runners", "moveTo", "bezierCurveTo", "quadraticCurveTo", "solveArc", "lineTo", "closePath", "y", "coords", "rx", "ry", "rot", "large", "sweep", "ex", "ey", "segs", "arcToSegments", "seg", "bez", "segmentToBezier", "rotateX", "ox", "oy", "th", "PI", "sin_th", "sin", "cos_th", "cos", "abs", "pl", "sqrt", "a00", "a01", "a10", "a11", "x0", "y0", "d", "sfactor_sq", "sfactor", "xc", "yc", "th0", "atan2", "th1", "th_arc", "segments", "result", "th2", "th3", "th_half", "x3", "y3", "SVGPath", "KAPPA", "initVector", "_ctmStack", "save", "restore", "pop", "lineWidth", "w", "_CAP_STYLES", "BUTT", "ROUND", "SQUARE", "lineCap", "_JOIN_STYLES", "MITER", "BEVEL", "lineJoin", "miterLimit", "dash", "original<PERSON>ength", "valid", "every", "Number", "isFinite", "JSON", "stringify", "phase", "undash", "cp1x", "cp1y", "cp2x", "cp2y", "cpx", "cpy", "rect", "roundedRect", "ellipse", "xe", "ye", "xm", "ym", "circle", "radius", "arc", "startAngle", "endAngle", "anticlockwise", "TWO_PI", "HALF_PI", "deltaAng", "dir", "numSegs", "segAng", "handleLen", "curAng", "deltaCx", "deltaCy", "ax", "ay", "segIdx", "polygon", "points", "shift", "point", "_windingRule", "rule", "test", "fill", "fillAndStroke", "isFillRule", "clip", "values", "translate", "rotate", "angle", "rad", "origin", "scale", "xFactor", "yFactor", "WIN_ANSI_MAP", "characters", "split", "AFMFont", "filename", "fs", "readFileSync", "contents", "attributes", "glyphWidths", "boundingBoxes", "kernPairs", "char<PERSON><PERSON><PERSON>", "char", "ascender", "descender", "xHeight", "capHeight", "lineGap", "section", "line", "match", "text", "res", "len", "glyphs", "charCode", "characterToGlyph", "glyph", "advances", "widthOfGlyph", "getKernPair", "PDFFont", "includeGap", "gap", "STANDARD_FONTS", "Courier", "__dirname", "Helvetica", "Symbol", "ZapfDingbats", "StandardFont", "font", "BaseFont", "Encoding", "encoded", "encodeText", "glyphsForString", "advancesForGlyphs", "positions", "xAdvance", "yAdvance", "xOffset", "yOffset", "advanceWidth", "advance", "toHex", "num", "EmbeddedFont", "subset", "createSubset", "unicode", "widths", "getGlyph", "postscriptName", "unitsPerEm", "ascent", "descent", "fontLayoutCache", "layoutCache", "features", "run", "position", "layoutRun", "cached", "only<PERSON><PERSON><PERSON>", "needle", "layoutCached", "gid", "includeGlyph", "codePoints", "isCFF", "cff", "fontFile", "encodeStream", "on", "familyClass", "sFamilyClass", "undefined", "flags", "post", "isFixedPitch", "head", "macStyle", "italic", "tag", "fromCharCode", "descriptor", "FontName", "Flags", "FontBBox", "minX", "minY", "maxX", "maxY", "ItalicAngle", "italicAngle", "Ascent", "Descent", "CapHeight", "XHeight", "StemV", "FontFile3", "FontFile2", "CIDSet", "CIDSetRef", "descendantFontData", "CIDSystemInfo", "Registry", "Ordering", "Supplement", "FontDescriptor", "W", "CIDToGIDMap", "descendantFont", "DescendantFonts", "ToUnicode", "toUnicodeCmap", "cmap", "entries", "chunkSize", "chunks", "ranges", "start", "PDFFontFactory", "src", "family", "isStandardFont", "fontkit", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initFonts", "defaultFont", "_fontFamilies", "_fontCount", "_fontSize", "_font", "_registeredFonts", "cache<PERSON>ey", "fontSize", "open", "currentLineHeight", "lineHeight", "registerFont", "SOFT_HYPHEN", "HYPHEN", "LineWrapper", "indent", "characterSpacing", "wordSpacing", "columns", "columnGap", "spaceLeft", "startX", "startY", "column", "ellipsis", "continuedX", "once", "continued", "align", "lastLine", "paragraphGap", "word", "widthOfString", "wordWidth", "bk", "breaker", "LineBreaker", "wordWidths", "nextBreak", "shouldC<PERSON><PERSON>ue", "lbk", "fbk", "<PERSON><PERSON>row", "mustShrink", "required", "nextY", "nextSection", "textWidth", "wc", "lc", "emitLine", "wordCount", "emit", "eachWord", "canFit", "lh", "continueOnNewPage", "EventEmitter", "initText", "_line", "bind", "_lineGap", "moveDown", "lines", "moveUp", "_text", "lineCallback", "_initOptions", "addStructure", "structParent", "add", "struct", "structType", "mark<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "wrapper", "_wrapper", "_textOptions", "wrap", "heightOfString", "Infinity", "list", "listType", "unit", "midLine", "bulletRadius", "textIndent", "itemIndent", "bulletIndent", "level", "levels", "numbers", "flatten", "item", "label", "letter", "times", "drawListItem", "listItem", "itemType", "labelType", "bodyType", "structTypes", "diff", "_fragment", "assign", "lineBreak", "trim", "spaceWidth", "baseline", "rendered<PERSON><PERSON><PERSON>", "link", "goTo", "destination", "addNamedDestination", "underline", "lineY", "strike", "oblique", "skew", "fonts", "encodedWord", "positionsWord", "hadOffset", "addSegment", "cur", "flush", "MARKERS", "COLOR_SPACE_MAP", "JPEG", "marker", "readUInt16BE", "orientation", "exif", "fromBuffer", "Orientation", "bits", "channels", "colorSpace", "BitsPerComponent", "<PERSON><PERSON><PERSON>", "Height", "PNGImage", "image", "PNG", "imgData", "dataDecoded", "hasAlphaChannel", "isInterlaced", "interlace<PERSON>ethod", "Predictor", "Colors", "colors", "Columns", "palette", "transparency", "grayscale", "rgb", "mask", "indexed", "loadIndexedAlphaChannel", "splitAlphaChannel", "decodeData", "alphaChannel", "sMask", "Decode", "decodePixels", "pixels", "p", "colorCount", "pixelCount", "skipByteCount", "colorIndex", "PDFImage", "exec", "initImages", "_imageRegistry", "_imageCount", "bh", "bp", "bw", "ip", "left1", "rotateAngle", "originX", "originY", "ignoreOrientation", "openImage", "xobjects", "wp", "hp", "fit", "cover", "valign", "annotate", "Rect", "_convertRect", "Border", "F", "Dest", "annotations", "note", "Name", "D", "url", "pages", "Kids", "URI", "_markup", "QuadPoints", "highlight", "lineAnnotation", "rectAnnotation", "ellipseAnnotation", "textAnnotation", "DA", "fileAnnotation", "file", "filespec", "hidden", "FS", "Desc", "PDFOutline", "parent", "title", "dest", "expanded", "outlineData", "children", "Count", "First", "Last", "child", "Prev", "Next", "endOutline", "initOutline", "outline", "Outlines", "PageMode", "PDFStructureContent", "pageRef", "mcid", "refs", "structContent", "PDFStructureElement", "type", "_attached", "_ended", "_flushed", "_is<PERSON><PERSON>d<PERSON><PERSON><PERSON>", "lang", "<PERSON>", "alt", "Alt", "E", "actual", "ActualText", "_children", "setParent", "setAttached", "_addContentToParentTree", "_contentForClosure", "pageStructParents", "getStructParentTree", "get", "parentRef", "_flush", "closure", "endMarkedContent", "_is<PERSON><PERSON>hable", "K", "_<PERSON><PERSON><PERSON>d", "Pg", "MCID", "PDFNumberTree", "initMarkings", "struct<PERSON><PERSON><PERSON><PERSON>", "tagged", "getMarkInfoDictionary", "Marked", "getStructTreeRoot", "<PERSON><PERSON><PERSON><PERSON>", "toClose", "marking", "attached", "Attached", "structParentTreeKey", "structElem", "structTreeRoot", "initPageMarkings", "pageMarkings", "newStructContent", "endPageMarkings", "MarkInfo", "StructTreeRoot", "<PERSON><PERSON><PERSON><PERSON>", "ParentTreeNextKey", "endMarkings", "FIELD_FLAGS", "readOnly", "noExport", "multiline", "toggleToOffButton", "radioButton", "pushButton", "combo", "edit", "multiSelect", "noSpell", "FIELD_JUSTIFY", "center", "VALUE_MAP", "defaultValue", "FORMAT_SPECIAL", "zip", "zipPlus4", "zip4", "phone", "ssn", "FORMAT_DEFAULT", "nDec", "sepComma", "negStyle", "currency", "currencyPrepend", "percent", "initForm", "_acroform", "Fields", "NeedAppearances", "DR", "AcroForm", "endAcroForm", "fontDict", "fieldRef", "_endChild", "childRef", "formField", "fieldDict", "_fieldDict", "_addToParent", "formAnnotation", "annotRef", "formText", "formPushButton", "formCombo", "formList", "formRadioButton", "formCheckbox", "_resolveType", "_resolveFlags", "_resolveJustify", "_resolveFont", "_resolveStrings", "_resolveColors", "_resolveFormat", "FT", "f", "format", "fnKeystroke", "fnFormat", "param", "AA", "JS", "backgroundColor", "MK", "BG", "borderColor", "BC", "Ff", "select", "appendChoices", "idx", "<PERSON><PERSON>", "refBody", "Params", "statSync", "birthtime", "ctime", "ModDate", "creationDate", "modifiedDate", "checksum", "CheckSum", "Size", "byteLength", "_fileRegistry", "isEqual", "fileSpecBody", "EF", "UF", "description", "addNamedEmbeddedFile", "initPDFA", "pSubset", "subset_conformance", "endSubset", "_addPdfaMetadata", "jsPath", "jest<PERSON><PERSON>", "_addColorOutputIntent", "existsSync", "p<PERSON><PERSON>ath", "iccProfile", "colorProfileRef", "intentRef", "Info", "OutputConditionIdentifier", "DestOutputProfile", "OutputIntents", "_get<PERSON>d<PERSON>id", "appendXML", "initPDFUA", "_addPdfuaMetadata", "_getPdfuaid", "_importSubset", "initSubset", "PDFA", "PDFUA", "PDFMetadata", "_metadata", "xml", "newline", "_closeTags", "initMetadata", "metadata", "append", "_addInfo", "toISOString", "Creator", "Title", "Author", "Subject", "Keywords", "endMetadata", "metadataRef", "<PERSON><PERSON><PERSON><PERSON>", "getXML", "<PERSON><PERSON><PERSON>", "PDFDocument", "_pageBuffer", "_pageBufferStart", "_offsets", "_waiting", "Names", "Des<PERSON>", "Producer", "displayTitle", "ViewerPreferences", "DisplayDocTitle", "generateFileID", "autoFirstPage", "addPage", "bufferPages", "flushPages", "count", "unshift", "EmbeddedFiles", "js", "JavaScript", "_finalize", "err", "console", "warn", "stack", "pipe", "createWriteStream", "_info", "entry", "xRefOffset", "trailer", "Root", "ID", "Encrypt", "Readable", "mixin", "methods", "prototype", "MetadataMixin", "ColorMixin", "VectorMixin", "FontsMixin", "TextMixin", "ImagesMixin", "AnnotationsMixin", "OutlineMixin", "MarkingsMixin", "AcroFormMixin", "AttachmentsMixin", "SubsetMixin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;IAIMA;;;;;;;+BACO;YACH,IAAIC,KAAJ,CAAU,mCAAV,CAAN;;;;;;;ICAEC;qBACsB;QAAdC,OAAc,uEAAJ,EAAI;;;;SACnBC,MAAL,GAAc,EAAd,CADwB;;SAGnBC,MAAL,GACE,OAAOF,OAAO,CAACE,MAAf,KAA0B,SAA1B,GAAsCF,OAAO,CAACE,MAA9C,GAAuD,IADzD;;;;;wBAIEC,KAAKC,KAAK;aACJ,KAAKH,MAAL,CAAYE,GAAZ,IAAmBC,GAA3B;;;;wBAGED,KAAK;aACA,KAAKF,MAAL,CAAYE,GAAZ,CAAP;;;;+BAGS;;;;UAEHE,UAAU,GAAGC,MAAM,CAACC,IAAP,CAAY,KAAKN,MAAjB,EAAyBO,IAAzB,CAA8B,UAACC,CAAD,EAAIC,CAAJ;eAC/C,KAAI,CAACC,YAAL,CAAkBF,CAAlB,EAAqBC,CAArB,CAD+C;OAA9B,CAAnB;UAIME,GAAG,GAAG,CAAC,IAAD,CAAZ;;UACI,KAAKV,MAAL,IAAeG,UAAU,CAACQ,MAAX,GAAoB,CAAvC,EAA0C;YAClCC,KAAK,GAAGT,UAAU,CAAC,CAAD,CAAxB;YACEU,IAAI,GAAGV,UAAU,CAACA,UAAU,CAACQ,MAAX,GAAoB,CAArB,CADnB;QAEAD,GAAG,CAACI,IAAJ,qBACeC,SAAS,CAACC,OAAV,CAAkB,CAAC,KAAKC,WAAL,CAAiBL,KAAjB,CAAD,EAA0B,KAAKK,WAAL,CAAiBJ,IAAjB,CAA1B,CAAlB,CADf;;;MAIFH,GAAG,CAACI,IAAJ,cAAe,KAAKI,SAAL,EAAf;;iDACgBf,UAfP;;;;4DAemB;cAAnBF,GAAmB;UAC1BS,GAAG,CAACI,IAAJ,eACSC,SAAS,CAACC,OAAV,CAAkB,KAAKC,WAAL,CAAiBhB,GAAjB,CAAlB,CADT,cACqDc,SAAS,CAACC,OAAV,CACjD,KAAKjB,MAAL,CAAYE,GAAZ,CADiD,CADrD;;;;;;;;MAMFS,GAAG,CAACI,IAAJ,CAAS,GAAT;MACAJ,GAAG,CAACI,IAAJ,CAAS,IAAT;aACOJ,GAAG,CAACS,IAAJ,CAAS,IAAT,CAAP;;;;;;;YAIM,IAAIvB,KAAJ,CAAU,mCAAV,CAAN;;;;gCAGU;YACJ,IAAIA,KAAJ,CAAU,mCAAV,CAAN;;;;;;;YAIM,IAAIA,KAAJ,CAAU,mCAAV,CAAN;;;;;;;AClDJ,IAAMwB,GAAG,GAAG,SAANA,GAAM,CAACC,GAAD,EAAMV,MAAN;SAAiB,CAACW,KAAK,CAACX,MAAM,GAAG,CAAV,CAAL,CAAkBQ,IAAlB,CAAuB,GAAvB,IAA8BE,GAA/B,EAAoCE,KAApC,CAA0C,CAACZ,MAA3C,CAAjB;CAAZ;;AAEA,IAAMa,WAAW,GAAG,mBAApB;AACA,IAAMC,SAAS,GAAG;QACV,KADU;QAEV,KAFU;QAGV,KAHU;QAIV,KAJU;QAKV,KALU;QAMV,MANU;OAOX,KAPW;OAQX;CARP;;AAYA,IAAMC,SAAS,GAAG,SAAZA,SAAY,CAASC,IAAT,EAAe;MACzBC,CAAC,GAAGD,IAAI,CAAChB,MAAf;;MACIiB,CAAC,GAAG,IAAR,EAAc;UACN,IAAIhC,KAAJ,CAAU,4BAAV,CAAN;GADF,MAEO;SACA,IAAIiC,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,CAAC,GAAG,CAA1B,EAA6BC,CAAC,GAAGC,GAAjC,EAAsCD,CAAC,IAAI,CAA3C,EAA8C;UACtCtB,CAAC,GAAGoB,IAAI,CAACE,CAAD,CAAd;MACAF,IAAI,CAACE,CAAD,CAAJ,GAAUF,IAAI,CAACE,CAAC,GAAG,CAAL,CAAd;MACAF,IAAI,CAACE,CAAC,GAAG,CAAL,CAAJ,GAActB,CAAd;;;;SAIGoB,IAAP;CAZF;;IAeMZ;;;;;;;4BACWgB,QAA0B;UAAlBC,SAAkB,uEAAN,IAAM;;;UAEnC,OAAOD,MAAP,KAAkB,QAAtB,EAAgC;0BACnBA,MAAX,EAD8B;OAAhC,MAIO,IAAIA,MAAM,YAAYE,MAAtB,EAA8B;YAC/BC,MAAM,GAAGH,MAAb,CADmC;;YAG/BI,SAAS,GAAG,KAAhB;;aACK,IAAIN,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGI,MAAM,CAACvB,MAA7B,EAAqCkB,CAAC,GAAGC,GAAzC,EAA8CD,CAAC,EAA/C,EAAmD;cAC7CK,MAAM,CAACE,UAAP,CAAkBP,CAAlB,IAAuB,IAA3B,EAAiC;YAC/BM,SAAS,GAAG,IAAZ;;;SAN+B;;;YAY/BE,YAAJ;;YACIF,SAAJ,EAAe;UACbE,YAAY,GAAGX,SAAS,CAACY,MAAM,CAACC,IAAP,iBAAqBL,MAArB,GAA+B,SAA/B,CAAD,CAAxB;SADF,MAEO;UACLG,YAAY,GAAGC,MAAM,CAACC,IAAP,CAAYL,MAAM,CAACM,OAAP,EAAZ,EAA8B,OAA9B,CAAf;SAhBiC;;;YAoB/BR,SAAJ,EAAe;UACbE,MAAM,GAAGF,SAAS,CAACK,YAAD,CAAT,CAAwBI,QAAxB,CAAiC,QAAjC,CAAT;SADF,MAEO;UACLP,MAAM,GAAGG,YAAY,CAACI,QAAb,CAAsB,QAAtB,CAAT;SAvBiC;;;QA2BnCP,MAAM,GAAGA,MAAM,CAACQ,OAAP,CAAelB,WAAf,EAA4B,UAAAmB,CAAC;iBAAIlB,SAAS,CAACkB,CAAD,CAAb;SAA7B,CAAT;0BAEWT,MAAX,OA7BmC;OAA9B,MAgCA,IAAII,MAAM,CAACM,QAAP,CAAgBb,MAAhB,CAAJ,EAA6B;0BACvBA,MAAM,CAACU,QAAP,CAAgB,KAAhB,CAAX;OADK,MAEA,IACLV,MAAM,YAAYpC,oBAAlB,IACAoC,MAAM,YAAYlC,OAFb,EAGL;eACOkC,MAAM,CAACU,QAAP,EAAP;OAJK,MAKA,IAAIV,MAAM,YAAYc,IAAtB,EAA4B;YAC7BX,OAAM,GACR,YAAKd,GAAG,CAACW,MAAM,CAACe,cAAP,EAAD,EAA0B,CAA1B,CAAR,IACA1B,GAAG,CAACW,MAAM,CAACgB,WAAP,KAAuB,CAAxB,EAA2B,CAA3B,CADH,GAEA3B,GAAG,CAACW,MAAM,CAACiB,UAAP,EAAD,EAAsB,CAAtB,CAFH,GAGA5B,GAAG,CAACW,MAAM,CAACkB,WAAP,EAAD,EAAuB,CAAvB,CAHH,GAIA7B,GAAG,CAACW,MAAM,CAACmB,aAAP,EAAD,EAAyB,CAAzB,CAJH,GAKA9B,GAAG,CAACW,MAAM,CAACoB,aAAP,EAAD,EAAyB,CAAzB,CALH,GAMA,GAPF,CADiC;;;YAW7BnB,SAAJ,EAAe;UACbE,OAAM,GAAGF,SAAS,CAACM,MAAM,CAACC,IAAP,CAAYL,OAAZ,EAAoB,OAApB,CAAD,CAAT,CAAwCO,QAAxC,CAAiD,QAAjD,CAAT,CADa;;UAIbP,OAAM,GAAGA,OAAM,CAACQ,OAAP,CAAelB,WAAf,EAA4B,UAAAmB,CAAC;mBAAIlB,SAAS,CAACkB,CAAD,CAAb;WAA7B,CAAT;;;0BAGST,OAAX;OAlBK,MAmBA,IAAIZ,KAAK,CAAC8B,OAAN,CAAcrB,MAAd,CAAJ,EAA2B;YAC1BsB,KAAK,GAAGtB,MAAM,CAACuB,GAAP,CAAW,UAAAC,CAAC;iBAAIxC,SAAS,CAACC,OAAV,CAAkBuC,CAAlB,EAAqBvB,SAArB,CAAJ;SAAZ,EAAiDb,IAAjD,CAAsD,GAAtD,CAAd;0BACWkC,KAAX;OAFK,MAGA,IAAI,GAAGZ,QAAH,CAAYe,IAAZ,CAAiBzB,MAAjB,MAA6B,iBAAjC,EAAoD;YACnDrB,GAAG,GAAG,CAAC,IAAD,CAAZ;;aACK,IAAIT,GAAT,IAAgB8B,MAAhB,EAAwB;cAChB7B,GAAG,GAAG6B,MAAM,CAAC9B,GAAD,CAAlB;UACAS,GAAG,CAACI,IAAJ,YAAab,GAAb,cAAoBc,SAAS,CAACC,OAAV,CAAkBd,GAAlB,EAAuB8B,SAAvB,CAApB;;;QAGFtB,GAAG,CAACI,IAAJ,CAAS,IAAT;eACOJ,GAAG,CAACS,IAAJ,CAAS,IAAT,CAAP;OARK,MASA,IAAI,OAAOY,MAAP,KAAkB,QAAtB,EAAgC;eAC9BhB,SAAS,CAAC0C,MAAV,CAAiB1B,MAAjB,CAAP;OADK,MAEA;yBACKA,MAAV;;;;;2BAIU2B,GAAG;UACXA,CAAC,GAAG,CAAC,IAAL,IAAaA,CAAC,GAAG,IAArB,EAA2B;eAClBC,IAAI,CAACC,KAAL,CAAWF,CAAC,GAAG,GAAf,IAAsB,GAA7B;;;YAGI,IAAI9D,KAAJ,+BAAiC8D,CAAjC,EAAN;;;;;;;ICtHEG;;;;;wBACQC,QAAZ,EAAsBC,EAAtB,EAAqC;;;QAAXC,IAAW,uEAAJ,EAAI;;;;;UAE9BF,QAAL,GAAgBA,QAAhB;UACKC,EAAL,GAAUA,EAAV;UACKC,IAAL,GAAYA,IAAZ;UACKC,GAAL,GAAW,CAAX;UACKC,QAAL,GAAgB,MAAKJ,QAAL,CAAcI,QAAd,IAA0B,CAAC,MAAKF,IAAL,CAAUG,MAArD;UACKC,kBAAL,GAA0B,CAA1B;UACKC,MAAL,GAAc,EAAd;;;;;;0BAGIC,OAAO;UACP,CAAChC,MAAM,CAACM,QAAP,CAAgB0B,KAAhB,CAAL,EAA6B;QAC3BA,KAAK,GAAGhC,MAAM,CAACC,IAAP,CAAY+B,KAAK,GAAG,IAApB,EAA0B,QAA1B,CAAR;;;WAGGF,kBAAL,IAA2BE,KAAK,CAAC3D,MAAjC;;UACI,KAAKqD,IAAL,CAAUO,MAAV,IAAoB,IAAxB,EAA8B;aACvBP,IAAL,CAAUO,MAAV,GAAmB,CAAnB;;;WAEGF,MAAL,CAAYvD,IAAZ,CAAiBwD,KAAjB;WACKN,IAAL,CAAUO,MAAV,IAAoBD,KAAK,CAAC3D,MAA1B;;UACI,KAAKuD,QAAT,EAAmB;eACT,KAAKF,IAAL,CAAUG,MAAV,GAAmB,aAA3B;;;;;wBAIAG,OAAO;UACLA,KAAJ,EAAW;aACJE,KAAL,CAAWF,KAAX;;;aAEK,KAAKG,QAAL,EAAP;;;;+BAGS;WACJC,MAAL,GAAc,KAAKZ,QAAL,CAAca,OAA5B;UAEM3C,SAAS,GAAG,KAAK8B,QAAL,CAAcc,SAAd,GACd,KAAKd,QAAL,CAAcc,SAAd,CAAwBC,YAAxB,CAAqC,KAAKd,EAA1C,EAA8C,KAAKE,GAAnD,CADc,GAEd,IAFJ;;UAII,KAAKI,MAAL,CAAY1D,MAAhB,EAAwB;aACjB0D,MAAL,GAAc/B,MAAM,CAACwC,MAAP,CAAc,KAAKT,MAAnB,CAAd;;YACI,KAAKH,QAAT,EAAmB;eACZG,MAAL,GAAcU,IAAI,CAACC,WAAL,CAAiB,KAAKX,MAAtB,CAAd;;;YAGErC,SAAJ,EAAe;eACRqC,MAAL,GAAcrC,SAAS,CAAC,KAAKqC,MAAN,CAAvB;;;aAGGL,IAAL,CAAUO,MAAV,GAAmB,KAAKF,MAAL,CAAY1D,MAA/B;;;WAGGmD,QAAL,CAAcmB,MAAd,WAAwB,KAAKlB,EAA7B,cAAmC,KAAKE,GAAxC;;WACKH,QAAL,CAAcmB,MAAd,CAAqBlE,SAAS,CAACC,OAAV,CAAkB,KAAKgD,IAAvB,EAA6BhC,SAA7B,CAArB;;UAEI,KAAKqC,MAAL,CAAY1D,MAAhB,EAAwB;aACjBmD,QAAL,CAAcmB,MAAd,CAAqB,QAArB;;aACKnB,QAAL,CAAcmB,MAAd,CAAqB,KAAKZ,MAA1B;;aAEKA,MAAL,GAAc,EAAd,CAJsB;;aAKjBP,QAAL,CAAcmB,MAAd,CAAqB,aAArB;;;WAGGnB,QAAL,CAAcmB,MAAd,CAAqB,QAArB;;WACKnB,QAAL,CAAcoB,OAAd,CAAsB,IAAtB;;;;+BAES;uBACC,KAAKnB,EAAf,cAAqB,KAAKE,GAA1B;;;;;EAtEuBtE;;ACT3B;;;;AAKA,IAAMwF,eAAe,GAAG;EACtBC,GAAG,EAAE,EADiB;EAEtBC,IAAI,EAAE,EAFgB;EAGtBC,MAAM,EAAE,EAHc;EAItBC,KAAK,EAAE;CAJT;AAOA,IAAMC,KAAK,GAAG;SACL,CAAC,OAAD,EAAU,OAAV,CADK;SAEL,CAAC,OAAD,EAAU,OAAV,CAFK;EAGZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAHQ;EAIZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAJQ;EAKZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CALQ;EAMZC,EAAE,EAAE,CAAC,MAAD,EAAS,OAAT,CANQ;EAOZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAPQ;EAQZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CARQ;EASZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CATQ;EAUZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAVQ;EAWZC,EAAE,EAAE,CAAC,KAAD,EAAQ,MAAR,CAXQ;EAYZC,EAAE,EAAE,CAAC,MAAD,EAAS,KAAT,CAZQ;EAaZC,GAAG,EAAE,CAAC,IAAD,EAAO,MAAP,CAbO;EAcZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAdQ;EAeZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAfQ;EAgBZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAhBQ;EAiBZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAjBQ;EAkBZC,EAAE,EAAE,CAAC,MAAD,EAAS,OAAT,CAlBQ;EAmBZC,EAAE,EAAE,CAAC,KAAD,EAAQ,MAAR,CAnBQ;EAoBZC,EAAE,EAAE,CAAC,MAAD,EAAS,KAAT,CApBQ;EAqBZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CArBQ;EAsBZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAtBQ;EAuBZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAvBQ;EAwBZC,GAAG,EAAE,CAAC,KAAD,EAAQ,MAAR,CAxBO;EAyBZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAzBQ;EA0BZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CA1BQ;EA2BZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CA3BQ;EA4BZC,EAAE,EAAE,CAAC,MAAD,EAAS,OAAT,CA5BQ;EA6BZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CA7BQ;EA8BZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CA9BQ;EA+BZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CA/BQ;EAgCZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAhCQ;EAiCZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAjCQ;EAkCZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAlCQ;EAmCZC,GAAG,EAAE,CAAC,KAAD,EAAQ,MAAR,CAnCO;EAoCZC,GAAG,EAAE,CAAC,MAAD,EAAS,OAAT,CApCO;EAqCZC,GAAG,EAAE,CAAC,OAAD,EAAU,MAAV,CArCO;EAsCZC,GAAG,EAAE,CAAC,MAAD,EAAS,OAAT,CAtCO;EAuCZC,GAAG,EAAE,CAAC,MAAD,EAAS,MAAT,CAvCO;EAwCZC,GAAG,EAAE,CAAC,MAAD,EAAS,MAAT,CAxCO;EAyCZC,IAAI,EAAE,CAAC,OAAD,EAAU,OAAV,CAzCM;EA0CZC,IAAI,EAAE,CAAC,OAAD,EAAU,OAAV,CA1CM;EA2CZC,IAAI,EAAE,CAAC,OAAD,EAAU,OAAV,CA3CM;EA4CZC,IAAI,EAAE,CAAC,MAAD,EAAS,OAAT,CA5CM;EA6CZC,IAAI,EAAE,CAAC,KAAD,EAAQ,MAAR,CA7CM;EA8CZC,SAAS,EAAE,CAAC,MAAD,EAAS,KAAT,CA9CC;EA+CZC,KAAK,EAAE,CAAC,KAAD,EAAQ,KAAR,CA/CK;EAgDZC,KAAK,EAAE,CAAC,KAAD,EAAQ,MAAR,CAhDK;EAiDZC,MAAM,EAAE,CAAC,KAAD,EAAQ,KAAR,CAjDI;EAkDZC,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR;CAlDX;;IAqDMC;mBACQ3E,QAAZ,EAAoC;QAAdhE,OAAc,uEAAJ,EAAI;;;;SAC7BgE,QAAL,GAAgBA,QAAhB;SACK4E,IAAL,GAAY5I,OAAO,CAAC4I,IAAR,IAAgB,QAA5B;SACKC,MAAL,GAAc7I,OAAO,CAAC6I,MAAR,IAAkB,UAAhC,CAHkC;;QAM9B,OAAO7I,OAAO,CAAC8I,MAAf,KAA0B,QAA9B,EAAwC;WACjCC,OAAL,GAAe;QACbzD,GAAG,EAAEtF,OAAO,CAAC8I,MADA;QAEbvD,IAAI,EAAEvF,OAAO,CAAC8I,MAFD;QAGbtD,MAAM,EAAExF,OAAO,CAAC8I,MAHH;QAIbrD,KAAK,EAAEzF,OAAO,CAAC8I;OAJjB,CADsC;KAAxC,MASO;WACAC,OAAL,GAAe/I,OAAO,CAAC+I,OAAR,IAAmB1D,eAAlC;KAhBgC;;;QAoB5B2D,UAAU,GAAGxH,KAAK,CAAC8B,OAAN,CAAc,KAAKsF,IAAnB,IACf,KAAKA,IADU,GAEflD,KAAK,CAAC,KAAKkD,IAAL,CAAUK,WAAV,EAAD,CAFT;SAGKC,KAAL,GAAaF,UAAU,CAAC,KAAKH,MAAL,KAAgB,UAAhB,GAA6B,CAA7B,GAAiC,CAAlC,CAAvB;SACKM,MAAL,GAAcH,UAAU,CAAC,KAAKH,MAAL,KAAgB,UAAhB,GAA6B,CAA7B,GAAiC,CAAlC,CAAxB;SAEKO,OAAL,GAAe,KAAKpF,QAAL,CAAcqF,GAAd,EAAf,CA1BkC;;SA6B7BC,SAAL,GAAiB,KAAKtF,QAAL,CAAcqF,GAAd,CAAkB;MACjCE,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR,EAAgB,QAAhB,EAA0B,QAA1B,EAAoC,QAApC;KADM,CAAjB,CA7BkC;;SAkC7BC,UAAL,GAAkB,KAAKxF,QAAL,CAAcqF,GAAd,CAAkB;MAClCI,IAAI,EAAE,MAD4B;MAElCC,MAAM,EAAE,KAAK1F,QAAL,CAAc2F,KAAd,CAAoBzF,IAApB,CAAyB0F,KAFC;MAGlCC,QAAQ,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,KAAKX,KAAZ,EAAmB,KAAKC,MAAxB,CAHwB;MAIlCW,QAAQ,EAAE,KAAKV,OAJmB;MAKlCW,SAAS,EAAE,KAAKT;KALA,CAAlB;SAQKU,QAAL,GAAgB,EAAhB;;;;;;2BAyCK;aACE,KAAKb,MAAL,GAAc,KAAKJ,OAAL,CAAavD,MAAlC;;;;0BAGIhB,OAAO;aACJ,KAAK4E,OAAL,CAAa1E,KAAb,CAAmBF,KAAnB,CAAP;;;;0BAGI;WACCgF,UAAL,CAAgBxH,GAAhB;WACKsH,SAAL,CAAetH,GAAf;aACO,KAAKoH,OAAL,CAAapH,GAAb,EAAP;;;;wBAhDU;UACJkC,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;aACOA,IAAI,CAAC+F,IAAL,IAAa,IAAb,GAAoB/F,IAAI,CAAC+F,IAAzB,GAAiC/F,IAAI,CAAC+F,IAAL,GAAY,EAApD;;;;wBAGa;UACP/F,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;aACOA,IAAI,CAACgG,OAAL,IAAgB,IAAhB,GAAuBhG,IAAI,CAACgG,OAA5B,GAAuChG,IAAI,CAACgG,OAAL,GAAe,EAA7D;;;;wBAGgB;UACVhG,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;aACOA,IAAI,CAACiG,SAAL,IAAkB,IAAlB,GAAyBjG,IAAI,CAACiG,SAA9B,GAA2CjG,IAAI,CAACiG,SAAL,GAAiB,EAAnE;;;;wBAGa;UACPjG,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;aACOA,IAAI,CAACkG,OAAL,IAAgB,IAAhB,GAAuBlG,IAAI,CAACkG,OAA5B,GAAuClG,IAAI,CAACkG,OAAL,GAAe,EAA7D;;;;wBAGgB;UACVlG,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;aACOA,IAAI,CAACmG,UAAL,KAAoBnG,IAAI,CAACmG,UAAL,GAAkB,EAAtC,CAAP;;;;wBAGgB;UACVnG,IAAI,GAAG,KAAKsF,UAAL,CAAgBtF,IAA7B;aACOA,IAAI,CAACoG,MAAL,IAAe,IAAf,GAAsBpG,IAAI,CAACoG,MAA3B,GAAqCpG,IAAI,CAACoG,MAAL,GAAc,EAA1D;;;;wBAGwB;UAClBpG,IAAI,GAAG,KAAKsF,UAAL,CAAgBtF,IAA7B;aACOA,IAAI,CAACqG,aAAL,IAAsB,IAAtB,GACHrG,IAAI,CAACqG,aADF,GAEFrG,IAAI,CAACqG,aAAL,GAAqB,KAAKvG,QAAL,CAAcwG,6BAAd,EAF1B;;;;;;;IC1IEC;;;;;;;;;;;;;iCACShK,GAAGC,GAAG;aACVD,CAAC,CAACiK,aAAF,CAAgBhK,CAAhB,CAAP;;;;gCAGU;aACH,OAAP;;;;gCAGUiK,GAAG;aACN,IAAIxI,MAAJ,CAAWwI,CAAX,CAAP;;;;;EAVsB5K;;ACN1B;;;;;;AAMA,SAAS6K,OAAT,CAAiBC,KAAjB,EAAwBC,UAAxB,EAAoC;MAC9BD,KAAK,GAAGC,UAAU,CAAC,CAAD,CAAtB,EAA2B,OAAO,KAAP;MACvBC,UAAU,GAAG,CAAjB;MACIC,QAAQ,GAAGF,UAAU,CAACjK,MAAX,GAAoB,CAAnC;;SACOkK,UAAU,IAAIC,QAArB,EAA+B;QACvBC,WAAW,GAAGpH,IAAI,CAACqH,KAAL,CAAW,CAACH,UAAU,GAAGC,QAAd,IAA0B,CAArC,CAApB,CAD6B;;QAIvBG,UAAU,GAAGF,WAAW,GAAG,CAAjC,CAJ6B;;QAQ3BJ,KAAK,IAAIC,UAAU,CAACK,UAAD,CAAnB,IACAN,KAAK,IAAIC,UAAU,CAACK,UAAU,GAAG,CAAd,CAFrB,EAGE;aACO,IAAP;;;QAGEN,KAAK,GAAGC,UAAU,CAACK,UAAU,GAAG,CAAd,CAAtB,EAAwC;;MAEtCJ,UAAU,GAAGE,WAAW,GAAG,CAA3B;KAFF,MAGO;;MAELD,QAAQ,GAAGC,WAAW,GAAG,CAAzB;;;;SAGG,KAAP;;;AC7BF;;;;;AAIA,IAAMG,sBAAsB,GAAG,CAC7B,MAD6B,EAE7B,MAF6B,EAG7B,MAH6B,EAI7B,MAJ6B,EAK7B,MAL6B,EAM7B,MAN6B,EAO7B,MAP6B,EAQ7B,MAR6B,EAS7B,MAT6B,EAU7B,MAV6B,EAW7B,MAX6B,EAY7B,MAZ6B,EAa7B,MAb6B,EAc7B,MAd6B,EAe7B,MAf6B,EAgB7B,MAhB6B,EAiB7B,MAjB6B,EAkB7B,MAlB6B,EAmB7B,MAnB6B,EAoB7B,MApB6B,EAqB7B,MArB6B,EAsB7B,MAtB6B,EAuB7B,MAvB6B,EAwB7B,MAxB6B,EAyB7B,MAzB6B,EA0B7B,MA1B6B,EA2B7B,MA3B6B,EA4B7B,MA5B6B,EA6B7B,MA7B6B,EA8B7B,MA9B6B,EA+B7B,MA/B6B,EAgC7B,MAhC6B,EAiC7B,MAjC6B,EAkC7B,MAlC6B,EAmC7B,MAnC6B,EAoC7B,MApC6B,EAqC7B,MArC6B,EAsC7B,MAtC6B,EAuC7B,MAvC6B,EAwC7B,MAxC6B,EAyC7B,MAzC6B,EA0C7B,MA1C6B,EA2C7B,MA3C6B,EA4C7B,MA5C6B,EA6C7B,MA7C6B,EA8C7B,MA9C6B,EA+C7B,MA/C6B,EAgD7B,MAhD6B,EAiD7B,MAjD6B,EAkD7B,MAlD6B,EAmD7B,MAnD6B,EAoD7B,MApD6B,EAqD7B,MArD6B,EAsD7B,MAtD6B,EAuD7B,MAvD6B,EAwD7B,MAxD6B,EAyD7B,MAzD6B,EA0D7B,MA1D6B,EA2D7B,MA3D6B,EA4D7B,MA5D6B,EA6D7B,MA7D6B,EA8D7B,MA9D6B,EA+D7B,MA/D6B,EAgE7B,MAhE6B,EAiE7B,MAjE6B,EAkE7B,MAlE6B,EAmE7B,MAnE6B,EAoE7B,MApE6B,EAqE7B,MArE6B,EAsE7B,MAtE6B,EAuE7B,MAvE6B,EAwE7B,MAxE6B,EAyE7B,MAzE6B,EA0E7B,MA1E6B,EA2E7B,MA3E6B,EA4E7B,MA5E6B,EA6E7B,MA7E6B,EA8E7B,MA9E6B,EA+E7B,MA/E6B,EAgF7B,MAhF6B,EAiF7B,MAjF6B,EAkF7B,MAlF6B,EAmF7B,MAnF6B,EAoF7B,MApF6B,EAqF7B,MArF6B,EAsF7B,MAtF6B,EAuF7B,MAvF6B,EAwF7B,MAxF6B,EAyF7B,MAzF6B,EA0F7B,MA1F6B,EA2F7B,MA3F6B,EA4F7B,MA5F6B,EA6F7B,MA7F6B,EA8F7B,MA9F6B,EA+F7B,MA/F6B,EAgG7B,MAhG6B,EAiG7B,MAjG6B,EAkG7B,MAlG6B,EAmG7B,MAnG6B,EAoG7B,MApG6B,EAqG7B,MArG6B,EAsG7B,MAtG6B,EAuG7B,MAvG6B,EAwG7B,MAxG6B,EAyG7B,MAzG6B,EA0G7B,MA1G6B,EA2G7B,MA3G6B,EA4G7B,MA5G6B,EA6G7B,MA7G6B,EA8G7B,MA9G6B,EA+G7B,MA/G6B,EAgH7B,MAhH6B,EAiH7B,MAjH6B,EAkH7B,MAlH6B,EAmH7B,MAnH6B,EAoH7B,MApH6B,EAqH7B,MArH6B,EAsH7B,MAtH6B,EAuH7B,MAvH6B,EAwH7B,MAxH6B,EAyH7B,MAzH6B,EA0H7B,MA1H6B,EA2H7B,MA3H6B,EA4H7B,MA5H6B,EA6H7B,MA7H6B,EA8H7B,MA9H6B,EA+H7B,MA/H6B,EAgI7B,MAhI6B,EAiI7B,MAjI6B,EAkI7B,MAlI6B,EAmI7B,MAnI6B,EAoI7B,MApI6B,EAqI7B,MArI6B,EAsI7B,MAtI6B,EAuI7B,MAvI6B,EAwI7B,MAxI6B,EAyI7B,MAzI6B,EA0I7B,MA1I6B,EA2I7B,MA3I6B,EA4I7B,MA5I6B,EA6I7B,MA7I6B,EA8I7B,MA9I6B,EA+I7B,MA/I6B,EAgJ7B,MAhJ6B,EAiJ7B,MAjJ6B,EAkJ7B,MAlJ6B,EAmJ7B,MAnJ6B,EAoJ7B,MApJ6B,EAqJ7B,MArJ6B,EAsJ7B,MAtJ6B,EAuJ7B,MAvJ6B,EAwJ7B,MAxJ6B,EAyJ7B,MAzJ6B,EA0J7B,MA1J6B,EA2J7B,MA3J6B,EA4J7B,MA5J6B,EA6J7B,MA7J6B,EA8J7B,MA9J6B,EA+J7B,MA/J6B,EAgK7B,MAhK6B,EAiK7B,MAjK6B,EAkK7B,MAlK6B,EAmK7B,MAnK6B,EAoK7B,MApK6B,EAqK7B,MArK6B,EAsK7B,MAtK6B,EAuK7B,MAvK6B,EAwK7B,MAxK6B,EAyK7B,MAzK6B,EA0K7B,MA1K6B,EA2K7B,MA3K6B,EA4K7B,MA5K6B,EA6K7B,MA7K6B,EA8K7B,MA9K6B,EA+K7B,MA/K6B,EAgL7B,MAhL6B,EAiL7B,MAjL6B,EAkL7B,MAlL6B,EAmL7B,MAnL6B,EAoL7B,MApL6B,EAqL7B,MArL6B,EAsL7B,MAtL6B,EAuL7B,MAvL6B,EAwL7B,MAxL6B,EAyL7B,MAzL6B,EA0L7B,MA1L6B,EA2L7B,MA3L6B,EA4L7B,MA5L6B,EA6L7B,MA7L6B,EA8L7B,MA9L6B,EA+L7B,MA/L6B,EAgM7B,MAhM6B,EAiM7B,MAjM6B,EAkM7B,MAlM6B,EAmM7B,MAnM6B,EAoM7B,MApM6B,EAqM7B,MArM6B,EAsM7B,MAtM6B,EAuM7B,MAvM6B,EAwM7B,MAxM6B,EAyM7B,MAzM6B,EA0M7B,MA1M6B,EA2M7B,MA3M6B,EA4M7B,MA5M6B,EA6M7B,MA7M6B,EA8M7B,MA9M6B,EA+M7B,MA/M6B,EAgN7B,MAhN6B,EAiN7B,MAjN6B,EAkN7B,MAlN6B,EAmN7B,MAnN6B,EAoN7B,MApN6B,EAqN7B,MArN6B,EAsN7B,MAtN6B,EAuN7B,MAvN6B,EAwN7B,MAxN6B,EAyN7B,MAzN6B,EA0N7B,MA1N6B,EA2N7B,MA3N6B,EA4N7B,MA5N6B,EA6N7B,MA7N6B,EA8N7B,MA9N6B,EA+N7B,MA/N6B,EAgO7B,MAhO6B,EAiO7B,MAjO6B,EAkO7B,MAlO6B,EAmO7B,MAnO6B,EAoO7B,MApO6B,EAqO7B,MArO6B,EAsO7B,MAtO6B,EAuO7B,MAvO6B,EAwO7B,MAxO6B,EAyO7B,MAzO6B,EA0O7B,MA1O6B,EA2O7B,MA3O6B,EA4O7B,MA5O6B,EA6O7B,MA7O6B,EA8O7B,MA9O6B,EA+O7B,MA/O6B,EAgP7B,MAhP6B,EAiP7B,MAjP6B,EAkP7B,MAlP6B,EAmP7B,MAnP6B,EAoP7B,MApP6B,EAqP7B,MArP6B,EAsP7B,MAtP6B,EAuP7B,MAvP6B,EAwP7B,MAxP6B,EAyP7B,MAzP6B,EA0P7B,MA1P6B,EA2P7B,MA3P6B,EA4P7B,MA5P6B,EA6P7B,MA7P6B,EA8P7B,MA9P6B,EA+P7B,MA/P6B,EAgQ7B,MAhQ6B,EAiQ7B,MAjQ6B,EAkQ7B,MAlQ6B,EAmQ7B,MAnQ6B,EAoQ7B,MApQ6B,EAqQ7B,MArQ6B,EAsQ7B,MAtQ6B,EAuQ7B,MAvQ6B,EAwQ7B,MAxQ6B,EAyQ7B,MAzQ6B,EA0Q7B,MA1Q6B,EA2Q7B,MA3Q6B,EA4Q7B,MA5Q6B,EA6Q7B,MA7Q6B,EA8Q7B,MA9Q6B,EA+Q7B,MA/Q6B,EAgR7B,MAhR6B,EAiR7B,MAjR6B,EAkR7B,MAlR6B,EAmR7B,MAnR6B,EAoR7B,MApR6B,EAqR7B,MArR6B,EAsR7B,MAtR6B,EAuR7B,MAvR6B,EAwR7B,MAxR6B,EAyR7B,MAzR6B,EA0R7B,MA1R6B,EA2R7B,MA3R6B,EA4R7B,MA5R6B,EA6R7B,MA7R6B,EA8R7B,MA9R6B,EA+R7B,MA/R6B,EAgS7B,MAhS6B,EAiS7B,MAjS6B,EAkS7B,MAlS6B,EAmS7B,MAnS6B,EAoS7B,MApS6B,EAqS7B,MArS6B,EAsS7B,MAtS6B,EAuS7B,MAvS6B,EAwS7B,MAxS6B,EAyS7B,MAzS6B,EA0S7B,MA1S6B,EA2S7B,MA3S6B,EA4S7B,MA5S6B,EA6S7B,MA7S6B,EA8S7B,MA9S6B,EA+S7B,MA/S6B,EAgT7B,MAhT6B,EAiT7B,MAjT6B,EAkT7B,MAlT6B,EAmT7B,MAnT6B,EAoT7B,MApT6B,EAqT7B,MArT6B,EAsT7B,MAtT6B,EAuT7B,MAvT6B,EAwT7B,MAxT6B,EAyT7B,MAzT6B,EA0T7B,MA1T6B,EA2T7B,MA3T6B,EA4T7B,MA5T6B,EA6T7B,MA7T6B,EA8T7B,MA9T6B,EA+T7B,MA/T6B,EAgU7B,MAhU6B,EAiU7B,MAjU6B,EAkU7B,MAlU6B,EAmU7B,MAnU6B,EAoU7B,MApU6B,EAqU7B,MArU6B,EAsU7B,MAtU6B,EAuU7B,MAvU6B,EAwU7B,MAxU6B,EAyU7B,MAzU6B,EA0U7B,MA1U6B,EA2U7B,MA3U6B,EA4U7B,MA5U6B,EA6U7B,MA7U6B,EA8U7B,MA9U6B,EA+U7B,MA/U6B,EAgV7B,MAhV6B,EAiV7B,MAjV6B,EAkV7B,MAlV6B,EAmV7B,MAnV6B,EAoV7B,MApV6B,EAqV7B,MArV6B,EAsV7B,MAtV6B,EAuV7B,MAvV6B,EAwV7B,MAxV6B,EAyV7B,MAzV6B,EA0V7B,MA1V6B,EA2V7B,MA3V6B,EA4V7B,MA5V6B,EA6V7B,MA7V6B,EA8V7B,MA9V6B,EA+V7B,MA/V6B,EAgW7B,MAhW6B,EAiW7B,MAjW6B,EAkW7B,MAlW6B,EAmW7B,MAnW6B,EAoW7B,MApW6B,EAqW7B,MArW6B,EAsW7B,MAtW6B,EAuW7B,MAvW6B,EAwW7B,MAxW6B,EAyW7B,MAzW6B,EA0W7B,MA1W6B,EA2W7B,MA3W6B,EA4W7B,MA5W6B,EA6W7B,MA7W6B,EA8W7B,MA9W6B,EA+W7B,MA/W6B,EAgX7B,MAhX6B,EAiX7B,MAjX6B,EAkX7B,MAlX6B,EAmX7B,MAnX6B,EAoX7B,MApX6B,EAqX7B,MArX6B,EAsX7B,MAtX6B,EAuX7B,MAvX6B,EAwX7B,MAxX6B,EAyX7B,MAzX6B,EA0X7B,MA1X6B,EA2X7B,MA3X6B,EA4X7B,MA5X6B,EA6X7B,MA7X6B,EA8X7B,MA9X6B,EA+X7B,MA/X6B,EAgY7B,MAhY6B,EAiY7B,MAjY6B,EAkY7B,MAlY6B,EAmY7B,MAnY6B,EAoY7B,MApY6B,EAqY7B,MArY6B,EAsY7B,MAtY6B,EAuY7B,MAvY6B,EAwY7B,MAxY6B,EAyY7B,MAzY6B,EA0Y7B,MA1Y6B,EA2Y7B,MA3Y6B,EA4Y7B,MA5Y6B,EA6Y7B,MA7Y6B,EA8Y7B,MA9Y6B,EA+Y7B,MA/Y6B,EAgZ7B,MAhZ6B,EAiZ7B,MAjZ6B,EAkZ7B,MAlZ6B,EAmZ7B,MAnZ6B,EAoZ7B,MApZ6B,EAqZ7B,MArZ6B,EAsZ7B,MAtZ6B,EAuZ7B,MAvZ6B,EAwZ7B,MAxZ6B,EAyZ7B,MAzZ6B,EA0Z7B,MA1Z6B,EA2Z7B,MA3Z6B,EA4Z7B,MA5Z6B,EA6Z7B,MA7Z6B,EA8Z7B,MA9Z6B,EA+Z7B,MA/Z6B,EAga7B,MAha6B,EAia7B,MAja6B,EAka7B,MAla6B,EAma7B,MAna6B,EAoa7B,MApa6B,EAqa7B,MAra6B,EAsa7B,MAta6B,EAua7B,MAva6B,EAwa7B,MAxa6B,EAya7B,MAza6B,EA0a7B,MA1a6B,EA2a7B,MA3a6B,EA4a7B,MA5a6B,EA6a7B,MA7a6B,EA8a7B,MA9a6B,EA+a7B,MA/a6B,EAgb7B,MAhb6B,EAib7B,MAjb6B,EAkb7B,MAlb6B,EAmb7B,MAnb6B,EAob7B,MApb6B,EAqb7B,MArb6B,EAsb7B,MAtb6B,EAub7B,MAvb6B,EAwb7B,MAxb6B,EAyb7B,MAzb6B,EA0b7B,MA1b6B,EA2b7B,MA3b6B,EA4b7B,MA5b6B,EA6b7B,MA7b6B,EA8b7B,MA9b6B,EA+b7B,MA/b6B,EAgc7B,MAhc6B,EAic7B,MAjc6B,EAkc7B,MAlc6B,EAmc7B,MAnc6B,EAoc7B,MApc6B,EAqc7B,MArc6B,EAsc7B,MAtc6B,EAuc7B,MAvc6B,EAwc7B,MAxc6B,EAyc7B,MAzc6B,EA0c7B,MA1c6B,EA2c7B,MA3c6B,EA4c7B,MA5c6B,EA6c7B,MA7c6B,EA8c7B,MA9c6B,EA+c7B,MA/c6B,EAgd7B,MAhd6B,EAid7B,MAjd6B,EAkd7B,MAld6B,EAmd7B,MAnd6B,EAod7B,MApd6B,EAqd7B,MArd6B,EAsd7B,MAtd6B,EAud7B,MAvd6B,EAwd7B,MAxd6B,EAyd7B,MAzd6B,EA0d7B,MA1d6B,EA2d7B,MA3d6B,EA4d7B,MA5d6B,EA6d7B,MA7d6B,EA8d7B,MA9d6B,EA+d7B,MA/d6B,EAge7B,MAhe6B,EAie7B,MAje6B,EAke7B,MAle6B,EAme7B,MAne6B,EAoe7B,MApe6B,EAqe7B,MAre6B,EAse7B,MAte6B,EAue7B,MAve6B,EAwe7B,MAxe6B,EAye7B,MAze6B,EA0e7B,MA1e6B,EA2e7B,MA3e6B,EA4e7B,MA5e6B,EA6e7B,MA7e6B,EA8e7B,MA9e6B,EA+e7B,MA/e6B,EAgf7B,MAhf6B,EAif7B,MAjf6B,EAkf7B,MAlf6B,EAmf7B,MAnf6B,EAof7B,MApf6B,EAqf7B,MArf6B,EAsf7B,MAtf6B,EAuf7B,MAvf6B,EAwf7B,MAxf6B,EAyf7B,MAzf6B,EA0f7B,MA1f6B,EA2f7B,MA3f6B,EA4f7B,MA5f6B,EA6f7B,MA7f6B,EA8f7B,MA9f6B,EA+f7B,MA/f6B,EAggB7B,MAhgB6B,EAigB7B,MAjgB6B,EAkgB7B,MAlgB6B,EAmgB7B,MAngB6B,EAogB7B,MApgB6B,EAqgB7B,MArgB6B,EAsgB7B,MAtgB6B,EAugB7B,MAvgB6B,EAwgB7B,MAxgB6B,EAygB7B,MAzgB6B,EA0gB7B,MA1gB6B,EA2gB7B,MA3gB6B,EA4gB7B,MA5gB6B,EA6gB7B,MA7gB6B,EA8gB7B,MA9gB6B,EA+gB7B,MA/gB6B,EAghB7B,MAhhB6B,EAihB7B,MAjhB6B,EAkhB7B,MAlhB6B,EAmhB7B,MAnhB6B,EAohB7B,MAphB6B,EAqhB7B,MArhB6B,EAshB7B,MAthB6B,EAuhB7B,MAvhB6B,EAwhB7B,MAxhB6B,EAyhB7B,MAzhB6B,EA0hB7B,MA1hB6B,EA2hB7B,MA3hB6B,EA4hB7B,MA5hB6B,EA6hB7B,MA7hB6B,EA8hB7B,MA9hB6B,EA+hB7B,MA/hB6B,EAgiB7B,MAhiB6B,EAiiB7B,MAjiB6B,EAkiB7B,MAliB6B,EAmiB7B,MAniB6B,EAoiB7B,MApiB6B,EAqiB7B,MAriB6B,EAsiB7B,MAtiB6B,EAuiB7B,MAviB6B,EAwiB7B,MAxiB6B,EAyiB7B,MAziB6B,EA0iB7B,MA1iB6B,EA2iB7B,MA3iB6B,EA4iB7B,MA5iB6B,EA6iB7B,MA7iB6B,EA8iB7B,MA9iB6B,EA+iB7B,MA/iB6B,EAgjB7B,MAhjB6B,EAijB7B,MAjjB6B,EAkjB7B,MAljB6B,EAmjB7B,MAnjB6B,EAojB7B,MApjB6B,EAqjB7B,MArjB6B,EAsjB7B,MAtjB6B,EAujB7B,MAvjB6B,EAwjB7B,MAxjB6B,EAyjB7B,MAzjB6B,EA0jB7B,MA1jB6B,EA2jB7B,MA3jB6B,EA4jB7B,MA5jB6B,EA6jB7B,MA7jB6B,EA8jB7B,MA9jB6B,EA+jB7B,MA/jB6B,EAgkB7B,MAhkB6B,EAikB7B,MAjkB6B,EAkkB7B,MAlkB6B,EAmkB7B,MAnkB6B,EAokB7B,MApkB6B,EAqkB7B,MArkB6B,EAskB7B,MAtkB6B,EAukB7B,MAvkB6B,EAwkB7B,MAxkB6B,EAykB7B,MAzkB6B,EA0kB7B,MA1kB6B,EA2kB7B,MA3kB6B,EA4kB7B,MA5kB6B,EA6kB7B,MA7kB6B,EA8kB7B,MA9kB6B,EA+kB7B,MA/kB6B,EAglB7B,MAhlB6B,EAilB7B,MAjlB6B,EAklB7B,MAllB6B,EAmlB7B,MAnlB6B,EAolB7B,MAplB6B,EAqlB7B,MArlB6B,EAslB7B,MAtlB6B,EAulB7B,MAvlB6B,EAwlB7B,MAxlB6B,EAylB7B,MAzlB6B,EA0lB7B,MA1lB6B,EA2lB7B,MA3lB6B,EA4lB7B,MA5lB6B,EA6lB7B,MA7lB6B,EA8lB7B,MA9lB6B,EA+lB7B,MA/lB6B,EAgmB7B,MAhmB6B,EAimB7B,MAjmB6B,EAkmB7B,MAlmB6B,EAmmB7B,MAnmB6B,EAomB7B,MApmB6B,EAqmB7B,MArmB6B,EAsmB7B,MAtmB6B,EAumB7B,MAvmB6B,EAwmB7B,MAxmB6B,EAymB7B,MAzmB6B,EA0mB7B,MA1mB6B,EA2mB7B,MA3mB6B,EA4mB7B,MA5mB6B,EA6mB7B,MA7mB6B,EA8mB7B,MA9mB6B,EA+mB7B,MA/mB6B,EAgnB7B,MAhnB6B,EAinB7B,MAjnB6B,EAknB7B,MAlnB6B,EAmnB7B,MAnnB6B,EAonB7B,MApnB6B,EAqnB7B,MArnB6B,EAsnB7B,MAtnB6B,EAunB7B,MAvnB6B,EAwnB7B,MAxnB6B,EAynB7B,MAznB6B,EA0nB7B,MA1nB6B,EA2nB7B,MA3nB6B,EA4nB7B,MA5nB6B,EA6nB7B,MA7nB6B,EA8nB7B,MA9nB6B,EA+nB7B,MA/nB6B,EAgoB7B,MAhoB6B,EAioB7B,MAjoB6B,EAkoB7B,MAloB6B,EAmoB7B,MAnoB6B,EAooB7B,MApoB6B,EAqoB7B,MAroB6B,EAsoB7B,MAtoB6B,EAuoB7B,MAvoB6B,EAwoB7B,MAxoB6B,EAyoB7B,MAzoB6B,EA0oB7B,MA1oB6B,EA2oB7B,MA3oB6B,EA4oB7B,MA5oB6B,EA6oB7B,MA7oB6B,EA8oB7B,MA9oB6B,EA+oB7B,MA/oB6B,EAgpB7B,MAhpB6B,EAipB7B,MAjpB6B,EAkpB7B,MAlpB6B,EAmpB7B,MAnpB6B,EAopB7B,MAppB6B,EAqpB7B,MArpB6B,EAspB7B,MAtpB6B,EAupB7B,MAvpB6B,EAwpB7B,MAxpB6B,EAypB7B,MAzpB6B,EA0pB7B,MA1pB6B,EA2pB7B,MA3pB6B,EA4pB7B,MA5pB6B,EA6pB7B,MA7pB6B,EA8pB7B,MA9pB6B,EA+pB7B,MA/pB6B,EAgqB7B,MAhqB6B,EAiqB7B,MAjqB6B,EAkqB7B,MAlqB6B,EAmqB7B,MAnqB6B,EAoqB7B,MApqB6B,EAqqB7B,MArqB6B,EAsqB7B,MAtqB6B,EAuqB7B,MAvqB6B,EAwqB7B,MAxqB6B,EAyqB7B,MAzqB6B,EA0qB7B,MA1qB6B,EA2qB7B,MA3qB6B,EA4qB7B,MA5qB6B,EA6qB7B,MA7qB6B,EA8qB7B,MA9qB6B,EA+qB7B,MA/qB6B,EAgrB7B,MAhrB6B,EAirB7B,MAjrB6B,EAkrB7B,MAlrB6B,EAmrB7B,MAnrB6B,EAorB7B,MAprB6B,EAqrB7B,MArrB6B,EAsrB7B,MAtrB6B,EAurB7B,MAvrB6B,EAwrB7B,MAxrB6B,EAyrB7B,MAzrB6B,EA0rB7B,MA1rB6B,EA2rB7B,OA3rB6B,EA4rB7B,OA5rB6B,EA6rB7B,OA7rB6B,EA8rB7B,OA9rB6B,EA+rB7B,OA/rB6B,EAgsB7B,OAhsB6B,EAisB7B,OAjsB6B,EAksB7B,OAlsB6B,EAmsB7B,OAnsB6B,EAosB7B,OApsB6B,EAqsB7B,OArsB6B,EAssB7B,OAtsB6B,EAusB7B,OAvsB6B,EAwsB7B,OAxsB6B,EAysB7B,OAzsB6B,EA0sB7B,OA1sB6B,EA2sB7B,OA3sB6B,EA4sB7B,OA5sB6B,EA6sB7B,OA7sB6B,EA8sB7B,OA9sB6B,EA+sB7B,OA/sB6B,EAgtB7B,OAhtB6B,EAitB7B,OAjtB6B,EAktB7B,OAltB6B,EAmtB7B,OAntB6B,EAotB7B,OAptB6B,EAqtB7B,OArtB6B,EAstB7B,OAttB6B,EAutB7B,OAvtB6B,EAwtB7B,OAxtB6B,EAytB7B,OAztB6B,EA0tB7B,OA1tB6B,EA2tB7B,OA3tB6B,EA4tB7B,OA5tB6B,EA6tB7B,OA7tB6B,EA8tB7B,OA9tB6B,EA+tB7B,OA/tB6B,EAguB7B,OAhuB6B,EAiuB7B,OAjuB6B,EAkuB7B,OAluB6B,EAmuB7B,OAnuB6B,EAouB7B,OApuB6B,EAquB7B,OAruB6B,EAsuB7B,OAtuB6B,EAuuB7B,OAvuB6B,EAwuB7B,OAxuB6B,EAyuB7B,OAzuB6B,EA0uB7B,OA1uB6B,EA2uB7B,OA3uB6B,EA4uB7B,OA5uB6B,EA6uB7B,OA7uB6B,EA8uB7B,OA9uB6B,EA+uB7B,OA/uB6B,EAgvB7B,OAhvB6B,EAivB7B,OAjvB6B,EAkvB7B,OAlvB6B,EAmvB7B,OAnvB6B,EAovB7B,OApvB6B,EAqvB7B,OArvB6B,EAsvB7B,OAtvB6B,EAuvB7B,OAvvB6B,EAwvB7B,OAxvB6B,EAyvB7B,OAzvB6B,EA0vB7B,OA1vB6B,EA2vB7B,OA3vB6B,EA4vB7B,OA5vB6B,EA6vB7B,OA7vB6B,EA8vB7B,OA9vB6B,EA+vB7B,OA/vB6B,EAgwB7B,OAhwB6B,EAiwB7B,OAjwB6B,EAkwB7B,OAlwB6B,EAmwB7B,OAnwB6B,EAowB7B,OApwB6B,EAqwB7B,OArwB6B,EAswB7B,OAtwB6B,EAuwB7B,OAvwB6B,EAwwB7B,OAxwB6B,EAywB7B,OAzwB6B,EA0wB7B,OA1wB6B,EA2wB7B,OA3wB6B,EA4wB7B,OA5wB6B,EA6wB7B,OA7wB6B,EA8wB7B,OA9wB6B,EA+wB7B,OA/wB6B,EAgxB7B,OAhxB6B,EAixB7B,OAjxB6B,EAkxB7B,OAlxB6B,EAmxB7B,OAnxB6B,EAoxB7B,OApxB6B,EAqxB7B,OArxB6B,EAsxB7B,OAtxB6B,EAuxB7B,OAvxB6B,EAwxB7B,OAxxB6B,CAA/B;;AA4xBA,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAwB,CAAAC,SAAS;SACrCV,OAAO,CAACU,SAAD,EAAYF,sBAAZ,CAD8B;CAAvC;;;;;;;;AAQA,IAAMG,0BAA0B,GAAG,CACjC,MADiC,EAEjC,MAFiC,EAGjC,MAHiC,EAIjC,MAJiC,EAKjC,MALiC,EAMjC,MANiC,EAOjC,MAPiC,EAQjC,MARiC,EASjC,MATiC,EAUjC,MAViC,EAWjC,MAXiC,EAYjC,MAZiC,EAajC,MAbiC,EAcjC,MAdiC,EAejC,MAfiC,EAgBjC,MAhBiC,EAiBjC,MAjBiC,EAkBjC,MAlBiC,EAmBjC,MAnBiC,EAoBjC,MApBiC,EAqBjC,MArBiC,EAsBjC,MAtBiC,EAuBjC,MAvBiC,EAwBjC,MAxBiC,EAyBjC,MAzBiC,EA0BjC,MA1BiC,EA2BjC,MA3BiC,EA4BjC,MA5BiC,EA6BjC,MA7BiC,EA8BjC,MA9BiC,EA+BjC,MA/BiC,EAgCjC,MAhCiC,EAiCjC,MAjCiC,EAkCjC,MAlCiC,EAmCjC,MAnCiC,EAoCjC,MApCiC,EAqCjC,MArCiC,EAsCjC,MAtCiC,EAuCjC,MAvCiC,EAwCjC,MAxCiC,EAyCjC,MAzCiC,EA0CjC,MA1CiC,EA2CjC,MA3CiC,EA4CjC,MA5CiC,EA6CjC,MA7CiC,EA8CjC,MA9CiC,EA+CjC,MA/CiC,EAgDjC,MAhDiC,EAiDjC,MAjDiC,EAkDjC,MAlDiC,EAmDjC,MAnDiC,EAoDjC,MApDiC,EAqDjC,MArDiC,EAsDjC,MAtDiC,CAAnC;;AA0DA,IAAMC,yBAAyB,GAAG,SAA5BA,yBAA4B,CAAAF,SAAS;SACzCV,OAAO,CAACU,SAAD,EAAYC,0BAAZ,CADkC;CAA3C;;;;;;;;AAQA,IAAME,0BAA0B,GAAG,CACjC,MADiC,EAEjC;;EACA,MAHiC,EAIjC;;EACA,MALiC,EAMjC;;EACA,MAPiC,EAQjC;;EACA,MATiC,EAUjC;;EACA,MAXiC,EAYjC;;EACA,MAbiC,EAcjC;;EACA,MAfiC,EAgBjC;;EACA,MAjBiC,EAkBjC;;EACA,MAnBiC,EAoBjC;;EACA,MArBiC,EAsBjC;;EACA,MAvBiC,EAwBjC;;EACA,MAzBiC,EA0BjC;;EACA,MA3BiC,EA4BjC;;EACA,MA7BiC,EA8BjC;;EACA,MA/BiC,EAgCjC;;EACA,MAjCiC,EAkCjC;;CAlCF;;AAsCA,IAAMC,wBAAwB,GAAG,SAA3BA,wBAA2B,CAAAJ,SAAS;SACxCV,OAAO,CAACU,SAAD,EAAYG,0BAAZ,CADiC;CAA1C;;;AAIA,IAAME,6BAA6B,GAAG;;;;;AAKpC,MALoC,EAMpC;;EACA,MAPoC,EAQpC;;EACA,MAToC,EAUpC;;EACA,MAXoC,EAYpC;;EACA,MAboC,EAcpC;;EACA,MAfoC,EAgBpC;;EACA,MAjBoC,EAkBpC;;EACA,MAnBoC,EAoBpC;;EACA,MArBoC,EAsBpC;;EACA,MAvBoC,EAwBpC;;EACA,MAzBoC,EA0BpC;;EACA,MA3BoC,EA4BpC;;EACA,MA7BoC,EA8BpC;;EACA,MA/BoC,EAgCpC;;EACA,MAjCoC,EAkCpC;;EACA,OAnCoC,EAoCpC;;CApCF;AAuCA,IAAMC,wBAAwB,GAAG;;;;;AAK/B,MAL+B,EAM/B;;EACA,MAP+B,EAQ/B;;EACA,OAT+B,EAU/B;;EACA,OAX+B,EAY/B;;EACA,OAb+B,EAc/B;;EACA,OAf+B,EAgB/B;;EACA,OAjB+B,EAkB/B;;EACA,OAnB+B,EAoB/B;;EACA,OArB+B,EAsB/B;;EACA,OAvB+B,EAwB/B;;EACA,OAzB+B,EA0B/B;;EACA,OA3B+B,EA4B/B;;EACA,OA7B+B,EA8B/B;;EACA,OA/B+B,EAgC/B;;EACA,OAjC+B,EAkC/B;;EACA,OAnC+B,EAoC/B;;EACA,QArC+B,EAsC/B;;CAtCF;;;;;AA4CA,IAAMC,qBAAqB,GAAG;;;;;AAK5B,CAL4B,EAM5B;;EACA,MAP4B,EAQ5B;;;;;;;AAMA,MAd4B,EAe5B;;EACA,MAhB4B,EAiB5B;;EACA,MAlB4B,EAmB5B;;EACA,MApB4B,EAqB5B;;EACA,MAtB4B,EAuB5B;;EACA,MAxB4B,EAyB5B;;EACA,MA1B4B,EA2B5B;;EACA,MA5B4B,EA6B5B;;EACA,MA9B4B,EA+B5B;;EACA,MAhC4B,EAiC5B;;EACA,MAlC4B,EAmC5B;;EACA,MApC4B,EAqC5B;;EACA,MAtC4B,EAuC5B;;EACA,MAxC4B,EAyC5B;;EACA,MA1C4B,EA2C5B;;;;;;;AAMA,MAjD4B,EAkD5B;;;;;;;AAMA,MAxD4B,EAyD5B,MAzD4B;;;;;AA+D5B,MA/D4B,EAgE5B;;;;;;;AAMA,MAtE4B,EAuE5B;;EACA,MAxE4B,EAyE5B;;EACA,MA1E4B,EA2E5B;;EACA,MA5E4B,EA6E5B;;EACA,MA9E4B,EA+E5B;;;;;;;AAMA,OArF4B,EAsF5B;;EACA,OAvF4B,EAwF5B;;;;;;;AAOA,OA/F4B,EAgG5B;;EACA,QAjG4B,EAkG5B;;CAlGF;;AAsGA,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAwB,CAAAR,SAAS;SACrCV,OAAO,CAACU,SAAD,EAAYG,0BAAZ,CAAP,IACAb,OAAO,CAACU,SAAD,EAAYO,qBAAZ,CADP,IAEAjB,OAAO,CAACU,SAAD,EAAYK,6BAAZ,CAFP,IAGAf,OAAO,CAACU,SAAD,EAAYM,wBAAZ,CAJ8B;CAAvC;;;;;;;;AAWA,IAAMG,kBAAkB,GAAG,CACzB,MADyB,EAEzB,MAFyB,EAGzB,MAHyB,EAIzB,MAJyB,EAKzB,MALyB,EAMzB,MANyB,EAOzB,MAPyB,EAQzB,MARyB,EASzB,MATyB,EAUzB,MAVyB,EAWzB,MAXyB,EAYzB,MAZyB,EAazB,MAbyB,EAczB,MAdyB,EAezB,MAfyB,EAgBzB,MAhByB,EAiBzB,MAjByB,EAkBzB,MAlByB,EAmBzB,MAnByB,EAoBzB,MApByB,EAqBzB,MArByB,EAsBzB,MAtByB,EAuBzB,MAvByB,EAwBzB,MAxByB,EAyBzB,MAzByB,EA0BzB,MA1ByB,EA2BzB,MA3ByB,EA4BzB,MA5ByB,EA6BzB,MA7ByB,EA8BzB,MA9ByB,EA+BzB,MA/ByB,EAgCzB,MAhCyB,EAiCzB,MAjCyB,EAkCzB,MAlCyB,EAmCzB,MAnCyB,EAoCzB,MApCyB,EAqCzB,MArCyB,EAsCzB,MAtCyB,EAuCzB,MAvCyB,EAwCzB,MAxCyB,EAyCzB,MAzCyB,EA0CzB,MA1CyB,EA2CzB,MA3CyB,EA4CzB,MA5CyB,EA6CzB,MA7CyB,EA8CzB,MA9CyB,EA+CzB,MA/CyB,EAgDzB,MAhDyB,EAiDzB,MAjDyB,EAkDzB,MAlDyB,EAmDzB,MAnDyB,EAoDzB,MApDyB,EAqDzB,MArDyB,EAsDzB,MAtDyB,EAuDzB,MAvDyB,EAwDzB,MAxDyB,EAyDzB,MAzDyB,EA0DzB,MA1DyB,EA2DzB,MA3DyB,EA4DzB,MA5DyB,EA6DzB,MA7DyB,EA8DzB,MA9DyB,EA+DzB,MA/DyB,EAgEzB,MAhEyB,EAiEzB,MAjEyB,EAkEzB,MAlEyB,EAmEzB,MAnEyB,EAoEzB,MApEyB,CAA3B;;AAwEA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,CAAAV,SAAS;SAAIV,OAAO,CAACU,SAAD,EAAYS,kBAAZ,CAAX;CAApC;;;;;;;;AAOA,IAAME,eAAe,GAAG,CACtB,MADsB,EAEtB,MAFsB,EAGtB,MAHsB,EAItB,MAJsB,EAKtB,MALsB,EAMtB,MANsB,EAOtB,MAPsB,EAQtB,MARsB,EAStB,MATsB,EAUtB,MAVsB,EAWtB,MAXsB,EAYtB,MAZsB,EAatB,MAbsB,EActB,MAdsB,EAetB,MAfsB,EAgBtB,MAhBsB,EAiBtB,MAjBsB,EAkBtB,MAlBsB,EAmBtB,MAnBsB,EAoBtB,MApBsB,EAqBtB,MArBsB,EAsBtB,MAtBsB,EAuBtB,MAvBsB,EAwBtB,MAxBsB,EAyBtB,MAzBsB,EA0BtB,MA1BsB,EA2BtB,MA3BsB,EA4BtB,MA5BsB,EA6BtB,MA7BsB,EA8BtB,MA9BsB,EA+BtB,MA/BsB,EAgCtB,MAhCsB,EAiCtB,MAjCsB,EAkCtB,MAlCsB,EAmCtB,MAnCsB,EAoCtB,MApCsB,EAqCtB,MArCsB,EAsCtB,MAtCsB,EAuCtB,MAvCsB,EAwCtB,MAxCsB,EAyCtB,MAzCsB,EA0CtB,MA1CsB,EA2CtB,MA3CsB,EA4CtB,MA5CsB,EA6CtB,MA7CsB,EA8CtB,MA9CsB,EA+CtB,MA/CsB,EAgDtB,MAhDsB,EAiDtB,MAjDsB,EAkDtB,MAlDsB,EAmDtB,MAnDsB,EAoDtB,MApDsB,EAqDtB,MArDsB,EAsDtB,MAtDsB,EAuDtB,MAvDsB,EAwDtB,MAxDsB,EAyDtB,MAzDsB,EA0DtB,MA1DsB,EA2DtB,MA3DsB,EA4DtB,MA5DsB,EA6DtB,MA7DsB,EA8DtB,MA9DsB,EA+DtB,MA/DsB,EAgEtB,MAhEsB,EAiEtB,MAjEsB,EAkEtB,MAlEsB,EAmEtB,MAnEsB,EAoEtB,MApEsB,EAqEtB,MArEsB,EAsEtB,MAtEsB,EAuEtB,MAvEsB,EAwEtB,MAxEsB,EAyEtB,MAzEsB,EA0EtB,MA1EsB,EA2EtB,MA3EsB,EA4EtB,MA5EsB,EA6EtB,MA7EsB,EA8EtB,MA9EsB,EA+EtB,MA/EsB,EAgFtB,MAhFsB,EAiFtB,MAjFsB,EAkFtB,MAlFsB,EAmFtB,MAnFsB,EAoFtB,MApFsB,EAqFtB,MArFsB,EAsFtB,MAtFsB,EAuFtB,MAvFsB,EAwFtB,MAxFsB,EAyFtB,MAzFsB,EA0FtB,MA1FsB,EA2FtB,MA3FsB,EA4FtB,MA5FsB,EA6FtB,MA7FsB,EA8FtB,MA9FsB,EA+FtB,MA/FsB,EAgGtB,MAhGsB,EAiGtB,MAjGsB,EAkGtB,MAlGsB,EAmGtB,MAnGsB,EAoGtB,MApGsB,EAqGtB,MArGsB,EAsGtB,MAtGsB,EAuGtB,MAvGsB,EAwGtB,MAxGsB,EAyGtB,MAzGsB,EA0GtB,MA1GsB,EA2GtB,MA3GsB,EA4GtB,MA5GsB,EA6GtB,MA7GsB,EA8GtB,MA9GsB,EA+GtB,MA/GsB,EAgHtB,MAhHsB,EAiHtB,MAjHsB,EAkHtB,MAlHsB,EAmHtB,MAnHsB,EAoHtB,MApHsB,EAqHtB,MArHsB,EAsHtB,MAtHsB,EAuHtB,MAvHsB,EAwHtB,MAxHsB,EAyHtB,MAzHsB,EA0HtB,MA1HsB,EA2HtB,MA3HsB,EA4HtB,MA5HsB,EA6HtB,MA7HsB,EA8HtB,MA9HsB,EA+HtB,MA/HsB,EAgItB,MAhIsB,EAiItB,MAjIsB,EAkItB,MAlIsB,EAmItB,MAnIsB,EAoItB,MApIsB,EAqItB,MArIsB,EAsItB,MAtIsB,EAuItB,MAvIsB,EAwItB,MAxIsB,EAyItB,MAzIsB,EA0ItB,MA1IsB,EA2ItB,MA3IsB,EA4ItB,MA5IsB,EA6ItB,MA7IsB,EA8ItB,MA9IsB,EA+ItB,MA/IsB,EAgJtB,MAhJsB,EAiJtB,MAjJsB,EAkJtB,MAlJsB,EAmJtB,MAnJsB,EAoJtB,MApJsB,EAqJtB,MArJsB,EAsJtB,MAtJsB,EAuJtB,MAvJsB,EAwJtB,MAxJsB,EAyJtB,MAzJsB,EA0JtB,MA1JsB,EA2JtB,MA3JsB,EA4JtB,MA5JsB,EA6JtB,MA7JsB,EA8JtB,MA9JsB,EA+JtB,MA/JsB,EAgKtB,MAhKsB,EAiKtB,MAjKsB,EAkKtB,MAlKsB,EAmKtB,MAnKsB,EAoKtB,MApKsB,EAqKtB,MArKsB,EAsKtB,MAtKsB,EAuKtB,MAvKsB,EAwKtB,MAxKsB,EAyKtB,MAzKsB,EA0KtB,MA1KsB,EA2KtB,MA3KsB,EA4KtB,MA5KsB,EA6KtB,MA7KsB,EA8KtB,MA9KsB,EA+KtB,MA/KsB,EAgLtB,MAhLsB,EAiLtB,MAjLsB,EAkLtB,MAlLsB,EAmLtB,MAnLsB,EAoLtB,MApLsB,EAqLtB,MArLsB,EAsLtB,MAtLsB,EAuLtB,MAvLsB,EAwLtB,MAxLsB,EAyLtB,MAzLsB,EA0LtB,MA1LsB,EA2LtB,MA3LsB,EA4LtB,MA5LsB,EA6LtB,MA7LsB,EA8LtB,MA9LsB,EA+LtB,MA/LsB,EAgMtB,MAhMsB,EAiMtB,MAjMsB,EAkMtB,MAlMsB,EAmMtB,MAnMsB,EAoMtB,MApMsB,EAqMtB,MArMsB,EAsMtB,MAtMsB,EAuMtB,MAvMsB,EAwMtB,MAxMsB,EAyMtB,MAzMsB,EA0MtB,MA1MsB,EA2MtB,MA3MsB,EA4MtB,MA5MsB,EA6MtB,MA7MsB,EA8MtB,MA9MsB,EA+MtB,MA/MsB,EAgNtB,MAhNsB,EAiNtB,MAjNsB,EAkNtB,MAlNsB,EAmNtB,MAnNsB,EAoNtB,MApNsB,EAqNtB,MArNsB,EAsNtB,MAtNsB,EAuNtB,MAvNsB,EAwNtB,MAxNsB,EAyNtB,MAzNsB,EA0NtB,MA1NsB,EA2NtB,MA3NsB,EA4NtB,MA5NsB,EA6NtB,MA7NsB,EA8NtB,MA9NsB,EA+NtB,MA/NsB,EAgOtB,MAhOsB,EAiOtB,MAjOsB,EAkOtB,MAlOsB,EAmOtB,MAnOsB,EAoOtB,MApOsB,EAqOtB,MArOsB,EAsOtB,MAtOsB,EAuOtB,MAvOsB,EAwOtB,MAxOsB,EAyOtB,MAzOsB,EA0OtB,MA1OsB,EA2OtB,MA3OsB,EA4OtB,MA5OsB,EA6OtB,MA7OsB,EA8OtB,MA9OsB,EA+OtB,MA/OsB,EAgPtB,MAhPsB,EAiPtB,MAjPsB,EAkPtB,MAlPsB,EAmPtB,MAnPsB,EAoPtB,MApPsB,EAqPtB,MArPsB,EAsPtB,MAtPsB,EAuPtB,MAvPsB,EAwPtB,MAxPsB,EAyPtB,MAzPsB,EA0PtB,MA1PsB,EA2PtB,MA3PsB,EA4PtB,MA5PsB,EA6PtB,MA7PsB,EA8PtB,MA9PsB,EA+PtB,MA/PsB,EAgQtB,MAhQsB,EAiQtB,MAjQsB,EAkQtB,MAlQsB,EAmQtB,MAnQsB,EAoQtB,MApQsB,EAqQtB,MArQsB,EAsQtB,MAtQsB,EAuQtB,MAvQsB,EAwQtB,MAxQsB,EAyQtB,MAzQsB,EA0QtB,MA1QsB,EA2QtB,MA3QsB,EA4QtB,MA5QsB,EA6QtB,MA7QsB,EA8QtB,MA9QsB,EA+QtB,MA/QsB,EAgRtB,MAhRsB,EAiRtB,MAjRsB,EAkRtB,MAlRsB,EAmRtB,MAnRsB,EAoRtB,MApRsB,EAqRtB,MArRsB,EAsRtB,MAtRsB,EAuRtB,MAvRsB,EAwRtB,MAxRsB,EAyRtB,MAzRsB,EA0RtB,MA1RsB,EA2RtB,MA3RsB,EA4RtB,MA5RsB,EA6RtB,MA7RsB,EA8RtB,MA9RsB,EA+RtB,MA/RsB,EAgStB,MAhSsB,EAiStB,MAjSsB,EAkStB,MAlSsB,EAmStB,MAnSsB,EAoStB,MApSsB,EAqStB,MArSsB,EAsStB,MAtSsB,EAuStB,MAvSsB,EAwStB,MAxSsB,EAyStB,MAzSsB,EA0StB,MA1SsB,EA2StB,MA3SsB,EA4StB,MA5SsB,EA6StB,MA7SsB,EA8StB,MA9SsB,EA+StB,MA/SsB,EAgTtB,MAhTsB,EAiTtB,MAjTsB,EAkTtB,MAlTsB,EAmTtB,MAnTsB,EAoTtB,MApTsB,EAqTtB,MArTsB,EAsTtB,MAtTsB,EAuTtB,MAvTsB,EAwTtB,MAxTsB,EAyTtB,MAzTsB,EA0TtB,MA1TsB,EA2TtB,MA3TsB,EA4TtB,MA5TsB,EA6TtB,MA7TsB,EA8TtB,MA9TsB,EA+TtB,MA/TsB,EAgUtB,MAhUsB,EAiUtB,MAjUsB,EAkUtB,MAlUsB,EAmUtB,MAnUsB,EAoUtB,MApUsB,EAqUtB,MArUsB,EAsUtB,MAtUsB,EAuUtB,MAvUsB,EAwUtB,MAxUsB,EAyUtB,MAzUsB,EA0UtB,MA1UsB,EA2UtB,MA3UsB,EA4UtB,MA5UsB,EA6UtB,MA7UsB,EA8UtB,MA9UsB,EA+UtB,MA/UsB,EAgVtB,MAhVsB,EAiVtB,MAjVsB,EAkVtB,MAlVsB,EAmVtB,MAnVsB,EAoVtB,MApVsB,EAqVtB,MArVsB,EAsVtB,MAtVsB,EAuVtB,MAvVsB,EAwVtB,MAxVsB,EAyVtB,MAzVsB,EA0VtB,MA1VsB,EA2VtB,MA3VsB,EA4VtB,MA5VsB,EA6VtB,MA7VsB,EA8VtB,MA9VsB,EA+VtB,MA/VsB,EAgWtB,MAhWsB,EAiWtB,MAjWsB,EAkWtB,MAlWsB,EAmWtB,MAnWsB,EAoWtB,MApWsB,EAqWtB,MArWsB,EAsWtB,MAtWsB,EAuWtB,MAvWsB,EAwWtB,MAxWsB,EAyWtB,MAzWsB,EA0WtB,MA1WsB,EA2WtB,MA3WsB,EA4WtB,MA5WsB,EA6WtB,MA7WsB,EA8WtB,MA9WsB,EA+WtB,MA/WsB,EAgXtB,MAhXsB,EAiXtB,MAjXsB,EAkXtB,MAlXsB,EAmXtB,MAnXsB,EAoXtB,MApXsB,EAqXtB,MArXsB,EAsXtB,MAtXsB,EAuXtB,MAvXsB,EAwXtB,MAxXsB,EAyXtB,MAzXsB,EA0XtB,MA1XsB,EA2XtB,MA3XsB,EA4XtB,MA5XsB,EA6XtB,MA7XsB,EA8XtB,MA9XsB,EA+XtB,MA/XsB,EAgYtB,MAhYsB,EAiYtB,MAjYsB,EAkYtB,MAlYsB,EAmYtB,MAnYsB,EAoYtB,MApYsB,EAqYtB,MArYsB,EAsYtB,MAtYsB,EAuYtB,MAvYsB,EAwYtB,MAxYsB,EAyYtB,MAzYsB,EA0YtB,MA1YsB,EA2YtB,MA3YsB,EA4YtB,MA5YsB,EA6YtB,MA7YsB,EA8YtB,MA9YsB,EA+YtB,MA/YsB,EAgZtB,MAhZsB,EAiZtB,MAjZsB,EAkZtB,MAlZsB,EAmZtB,MAnZsB,EAoZtB,MApZsB,EAqZtB,MArZsB,EAsZtB,MAtZsB,EAuZtB,MAvZsB,EAwZtB,MAxZsB,EAyZtB,MAzZsB,EA0ZtB,MA1ZsB,EA2ZtB,MA3ZsB,EA4ZtB,MA5ZsB,EA6ZtB,MA7ZsB,EA8ZtB,MA9ZsB,EA+ZtB,MA/ZsB,EAgatB,MAhasB,EAiatB,MAjasB,EAkatB,MAlasB,EAmatB,MAnasB,EAoatB,MApasB,EAqatB,MArasB,EAsatB,MAtasB,EAuatB,MAvasB,EAwatB,MAxasB,EAyatB,MAzasB,EA0atB,MA1asB,EA2atB,MA3asB,EA4atB,MA5asB,EA6atB,MA7asB,EA8atB,MA9asB,EA+atB,MA/asB,EAgbtB,MAhbsB,EAibtB,MAjbsB,EAkbtB,MAlbsB,EAmbtB,MAnbsB,EAobtB,MApbsB,EAqbtB,MArbsB,EAsbtB,MAtbsB,EAubtB,MAvbsB,EAwbtB,MAxbsB,EAybtB,MAzbsB,EA0btB,MA1bsB,EA2btB,MA3bsB,EA4btB,MA5bsB,EA6btB,MA7bsB,EA8btB,MA9bsB,EA+btB,MA/bsB,EAgctB,MAhcsB,EAictB,MAjcsB,EAkctB,MAlcsB,EAmctB,MAncsB,EAoctB,MApcsB,EAqctB,MArcsB,EAsctB,MAtcsB,EAuctB,MAvcsB,EAwctB,MAxcsB,EAyctB,MAzcsB,EA0ctB,MA1csB,EA2ctB,MA3csB,EA4ctB,MA5csB,EA6ctB,MA7csB,EA8ctB,MA9csB,EA+ctB,MA/csB,EAgdtB,MAhdsB,EAidtB,MAjdsB,EAkdtB,MAldsB,EAmdtB,MAndsB,EAodtB,MApdsB,EAqdtB,MArdsB,EAsdtB,MAtdsB,EAudtB,MAvdsB,EAwdtB,MAxdsB,EAydtB,MAzdsB,EA0dtB,MA1dsB,EA2dtB,MA3dsB,EA4dtB,MA5dsB,EA6dtB,MA7dsB,EA8dtB,MA9dsB,EA+dtB,MA/dsB,EAgetB,MAhesB,EAietB,MAjesB,EAketB,MAlesB,EAmetB,MAnesB,EAoetB,MApesB,EAqetB,MAresB,EAsetB,MAtesB,EAuetB,MAvesB,EAwetB,MAxesB,EAyetB,MAzesB,EA0etB,MA1esB,EA2etB,MA3esB,EA4etB,MA5esB,EA6etB,MA7esB,EA8etB,MA9esB,EA+etB,MA/esB,EAgftB,MAhfsB,EAiftB,MAjfsB,EAkftB,MAlfsB,EAmftB,MAnfsB,EAoftB,MApfsB,EAqftB,MArfsB,EAsftB,MAtfsB,EAuftB,MAvfsB,EAwftB,MAxfsB,EAyftB,MAzfsB,EA0ftB,MA1fsB,EA2ftB,MA3fsB,EA4ftB,MA5fsB,EA6ftB,MA7fsB,EA8ftB,MA9fsB,EA+ftB,MA/fsB,EAggBtB,MAhgBsB,EAigBtB,MAjgBsB,EAkgBtB,MAlgBsB,EAmgBtB,MAngBsB,EAogBtB,MApgBsB,EAqgBtB,MArgBsB,EAsgBtB,MAtgBsB,EAugBtB,MAvgBsB,EAwgBtB,MAxgBsB,EAygBtB,MAzgBsB,EA0gBtB,MA1gBsB,EA2gBtB,MA3gBsB,EA4gBtB,MA5gBsB,EA6gBtB,MA7gBsB,EA8gBtB,MA9gBsB,EA+gBtB,MA/gBsB,EAghBtB,MAhhBsB,EAihBtB,MAjhBsB,EAkhBtB,MAlhBsB,EAmhBtB,MAnhBsB,EAohBtB,MAphBsB,EAqhBtB,MArhBsB,EAshBtB,MAthBsB,EAuhBtB,MAvhBsB,EAwhBtB,MAxhBsB,EAyhBtB,MAzhBsB,EA0hBtB,MA1hBsB,EA2hBtB,MA3hBsB,EA4hBtB,MA5hBsB,EA6hBtB,MA7hBsB,EA8hBtB,MA9hBsB,EA+hBtB,MA/hBsB,EAgiBtB,MAhiBsB,EAiiBtB,MAjiBsB,EAkiBtB,MAliBsB,EAmiBtB,MAniBsB,EAoiBtB,MApiBsB,EAqiBtB,MAriBsB,EAsiBtB,MAtiBsB,EAuiBtB,MAviBsB,EAwiBtB,MAxiBsB,EAyiBtB,MAziBsB,EA0iBtB,MA1iBsB,EA2iBtB,MA3iBsB,EA4iBtB,MA5iBsB,EA6iBtB,MA7iBsB,EA8iBtB,MA9iBsB,EA+iBtB,MA/iBsB,EAgjBtB,MAhjBsB,EAijBtB,MAjjBsB,EAkjBtB,MAljBsB,EAmjBtB,MAnjBsB,EAojBtB,MApjBsB,EAqjBtB,MArjBsB,EAsjBtB,MAtjBsB,EAujBtB,MAvjBsB,EAwjBtB,MAxjBsB,EAyjBtB,MAzjBsB,EA0jBtB,MA1jBsB,EA2jBtB,MA3jBsB,EA4jBtB,MA5jBsB,EA6jBtB,MA7jBsB,EA8jBtB,MA9jBsB,EA+jBtB,MA/jBsB,EAgkBtB,MAhkBsB,EAikBtB,MAjkBsB,EAkkBtB,MAlkBsB,EAmkBtB,MAnkBsB,EAokBtB,MApkBsB,EAqkBtB,MArkBsB,EAskBtB,MAtkBsB,EAukBtB,MAvkBsB,EAwkBtB,MAxkBsB,EAykBtB,MAzkBsB,EA0kBtB,MA1kBsB,EA2kBtB,MA3kBsB,EA4kBtB,MA5kBsB,EA6kBtB,MA7kBsB,EA8kBtB,MA9kBsB,EA+kBtB,MA/kBsB,EAglBtB,MAhlBsB,EAilBtB,MAjlBsB,EAklBtB,MAllBsB,EAmlBtB,MAnlBsB,EAolBtB,MAplBsB,EAqlBtB,MArlBsB,EAslBtB,MAtlBsB,EAulBtB,MAvlBsB,EAwlBtB,MAxlBsB,EAylBtB,MAzlBsB,EA0lBtB,MA1lBsB,EA2lBtB,MA3lBsB,EA4lBtB,MA5lBsB,EA6lBtB,MA7lBsB,EA8lBtB,MA9lBsB,EA+lBtB,MA/lBsB,EAgmBtB,MAhmBsB,EAimBtB,MAjmBsB,EAkmBtB,MAlmBsB,EAmmBtB,MAnmBsB,EAomBtB,MApmBsB,EAqmBtB,MArmBsB,EAsmBtB,MAtmBsB,EAumBtB,MAvmBsB,EAwmBtB,MAxmBsB,EAymBtB,MAzmBsB,EA0mBtB,MA1mBsB,EA2mBtB,MA3mBsB,EA4mBtB,MA5mBsB,EA6mBtB,MA7mBsB,EA8mBtB,MA9mBsB,EA+mBtB,MA/mBsB,EAgnBtB,MAhnBsB,EAinBtB,MAjnBsB,EAknBtB,MAlnBsB,EAmnBtB,MAnnBsB,EAonBtB,MApnBsB,EAqnBtB,MArnBsB,EAsnBtB,MAtnBsB,EAunBtB,MAvnBsB,EAwnBtB,MAxnBsB,EAynBtB,MAznBsB,EA0nBtB,MA1nBsB,EA2nBtB,MA3nBsB,EA4nBtB,MA5nBsB,EA6nBtB,MA7nBsB,EA8nBtB,MA9nBsB,EA+nBtB,MA/nBsB,EAgoBtB,MAhoBsB,EAioBtB,MAjoBsB,EAkoBtB,MAloBsB,EAmoBtB,MAnoBsB,EAooBtB,MApoBsB,EAqoBtB,MAroBsB,EAsoBtB,MAtoBsB,EAuoBtB,OAvoBsB,EAwoBtB,OAxoBsB,EAyoBtB,OAzoBsB,EA0oBtB,OA1oBsB,EA2oBtB,OA3oBsB,EA4oBtB,OA5oBsB,EA6oBtB,OA7oBsB,EA8oBtB,OA9oBsB,EA+oBtB,OA/oBsB,EAgpBtB,OAhpBsB,EAipBtB,OAjpBsB,EAkpBtB,OAlpBsB,EAmpBtB,OAnpBsB,EAopBtB,OAppBsB,EAqpBtB,OArpBsB,EAspBtB,OAtpBsB,EAupBtB,OAvpBsB,EAwpBtB,OAxpBsB,EAypBtB,OAzpBsB,EA0pBtB,OA1pBsB,EA2pBtB,OA3pBsB,EA4pBtB,OA5pBsB,EA6pBtB,OA7pBsB,EA8pBtB,OA9pBsB,EA+pBtB,OA/pBsB,EAgqBtB,OAhqBsB,EAiqBtB,OAjqBsB,EAkqBtB,OAlqBsB,EAmqBtB,OAnqBsB,EAoqBtB,OApqBsB,EAqqBtB,OArqBsB,EAsqBtB,OAtqBsB,EAuqBtB,OAvqBsB,EAwqBtB,OAxqBsB,EAyqBtB,OAzqBsB,EA0qBtB,OA1qBsB,EA2qBtB,OA3qBsB,EA4qBtB,OA5qBsB,EA6qBtB,OA7qBsB,EA8qBtB,OA9qBsB,EA+qBtB,OA/qBsB,EAgrBtB,OAhrBsB,EAirBtB,OAjrBsB,EAkrBtB,OAlrBsB,EAmrBtB,OAnrBsB,EAorBtB,OAprBsB,EAqrBtB,OArrBsB,EAsrBtB,OAtrBsB,EAurBtB,OAvrBsB,EAwrBtB,OAxrBsB,EAyrBtB,OAzrBsB,EA0rBtB,OA1rBsB,EA2rBtB,OA3rBsB,EA4rBtB,OA5rBsB,EA6rBtB,OA7rBsB,EA8rBtB,OA9rBsB,EA+rBtB,OA/rBsB,EAgsBtB,OAhsBsB,EAisBtB,OAjsBsB,EAksBtB,OAlsBsB,EAmsBtB,OAnsBsB,EAosBtB,OApsBsB,EAqsBtB,OArsBsB,EAssBtB,OAtsBsB,EAusBtB,OAvsBsB,EAwsBtB,OAxsBsB,EAysBtB,OAzsBsB,EA0sBtB,OA1sBsB,EA2sBtB,OA3sBsB,EA4sBtB,OA5sBsB,EA6sBtB,OA7sBsB,EA8sBtB,OA9sBsB,EA+sBtB,QA/sBsB,EAgtBtB,QAhtBsB,CAAxB;;AAotBA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAmB,CAAAZ,SAAS;SAAIV,OAAO,CAACU,SAAD,EAAYW,eAAZ,CAAX;CAAlC;;ACn3DA;;;;;AAIA,IAAME,aAAa,GAAGT,wBAAtB;;;;;;AAMA,IAAMU,eAAe,GAAGZ,yBAAxB;;AAGA,IAAMa,YAAY,GAAG,SAAfA,YAAe,CAAAf,SAAS;SAAIA,SAAS,CAACgB,WAAV,CAAsB,CAAtB,CAAJ;CAA9B;;AACA,IAAMxL,KAAK,GAAG,SAARA,KAAQ,CAAAyL,CAAC;SAAIA,CAAC,CAAC,CAAD,CAAL;CAAf;;AACA,IAAMxL,IAAI,GAAG,SAAPA,IAAO,CAAAwL,CAAC;SAAIA,CAAC,CAACA,CAAC,CAAC1L,MAAF,GAAW,CAAZ,CAAL;CAAd;;;;;;;;;;AASA,SAAS2L,YAAT,CAAsBC,KAAtB,EAA6B;MACrBC,UAAU,GAAG,EAAnB;MACM9D,IAAI,GAAG6D,KAAK,CAAC5L,MAAnB;;OAEK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6G,IAApB,EAA0B7G,CAAC,IAAI,CAA/B,EAAkC;QAC1B4K,MAAM,GAAGF,KAAK,CAACnK,UAAN,CAAiBP,CAAjB,CAAf;;QAEI4K,MAAM,IAAI,MAAV,IAAoBA,MAAM,IAAI,MAA9B,IAAwC/D,IAAI,GAAG7G,CAAC,GAAG,CAAvD,EAA0D;UAClD6K,IAAI,GAAGH,KAAK,CAACnK,UAAN,CAAiBP,CAAC,GAAG,CAArB,CAAb;;UAEI6K,IAAI,IAAI,MAAR,IAAkBA,IAAI,IAAI,MAA9B,EAAsC;QACpCF,UAAU,CAAC1L,IAAX,CAAgB,CAAC2L,MAAM,GAAG,MAAV,IAAoB,KAApB,GAA4BC,IAA5B,GAAmC,MAAnC,GAA4C,OAA5D;QACA7K,CAAC,IAAI,CAAL;;;;;IAKJ2K,UAAU,CAAC1L,IAAX,CAAgB2L,MAAhB;;;SAGKD,UAAP;;;;;;;;;;;AAUF,SAASG,QAAT,CAAkBJ,KAAlB,EAAoC;MAAXK,IAAW,uEAAJ,EAAI;;MAC9B,OAAOL,KAAP,KAAiB,QAArB,EAA+B;UACvB,IAAIM,SAAJ,CAAc,kBAAd,CAAN;;;MAGEN,KAAK,CAAC5L,MAAN,KAAiB,CAArB,EAAwB;WACf,EAAP;GANgC;;;MAU5BmM,YAAY,GAAGR,YAAY,CAACC,KAAD,CAAZ;GAElBjJ,GAFkB,CAEd,UAAA8H,SAAS;WAAKa,aAAa,CAACb,SAAD,CAAb,GAA2B,IAA3B,GAAkCA,SAAvC;GAFK;GAIlB2B,MAJkB,CAIX,UAAA3B,SAAS;WAAI,CAACc,eAAe,CAACd,SAAD,CAApB;GAJE,CAArB,CAVkC;;MAiB5B4B,gBAAgB,GAAG/K,MAAM,CAACgL,aAAP,CACtBC,KADsB,CAChB,IADgB,EACVJ,YADU,EAEtBK,SAFsB,CAEZ,MAFY,CAAzB;MAIMC,cAAc,GAAGd,YAAY,CAACU,gBAAD,CAAnC,CArBkC;;MAwB5BK,aAAa,GAAGD,cAAc,CAACE,IAAf,CAAoB1B,qBAApB,CAAtB;;MAEIyB,aAAJ,EAAmB;UACX,IAAIzN,KAAJ,CACJ,2EADI,CAAN;GA3BgC;;;MAiC9BgN,IAAI,CAACW,eAAL,KAAyB,IAA7B,EAAmC;QAC3BC,aAAa,GAAGJ,cAAc,CAACE,IAAf,CAAoBnC,qBAApB,CAAtB;;QAEIqC,aAAJ,EAAmB;YACX,IAAI5N,KAAJ,CACJ,4EADI,CAAN;;GArC8B;;;MA6C5B6N,UAAU,GAAGL,cAAc,CAACE,IAAf,CAAoBxB,kBAApB,CAAnB;MAEM4B,QAAQ,GAAGN,cAAc,CAACE,IAAf,CAAoBtB,gBAApB,CAAjB,CA/CkC;;;MAmD9ByB,UAAU,IAAIC,QAAlB,EAA4B;UACpB,IAAI9N,KAAJ,CACJ,iEACE,oDAFE,CAAN;;;;;;;;;MAYI+N,cAAc,GAAG7B,kBAAkB,CACvCK,YAAY,CAACvL,KAAK,CAACoM,gBAAD,CAAN,CAD2B,CAAzC;MAGMY,aAAa,GAAG9B,kBAAkB,CACtCK,YAAY,CAACtL,IAAI,CAACmM,gBAAD,CAAL,CAD0B,CAAxC;;MAIIS,UAAU,IAAI,EAAEE,cAAc,IAAIC,aAApB,CAAlB,EAAsD;UAC9C,IAAIhO,KAAJ,CACJ,qEACE,6EAFE,CAAN;;;SAMKoN,gBAAP;;;ICvIIa;;;qCAC6B;UAAXC,IAAW,uEAAJ,EAAI;UAC3BC,OAAO,aAAMD,IAAI,CAACE,YAAL,CAAkBC,OAAlB,EAAN,OAAX;;WAEK,IAAIhO,GAAT,IAAgB6N,IAAhB,EAAsB;;YAEhB,CAACA,IAAI,CAACI,cAAL,CAAoBjO,GAApB,CAAL,EAA+B;;;;QAG/B8N,OAAO,cAAO9N,GAAP,eAAe6N,IAAI,CAAC7N,GAAD,CAAJ,CAAUuC,OAAV,EAAf,OAAP;;;aAGK2L,iBAAiB,CAACC,QAAQ,CAACC,GAAT,CAAaN,OAAb,CAAD,CAAxB;;;;4CAG6BO,OAAO;aAC7BF,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuBC,MAAvB,CAA8BH,KAA9B,CAAP;;;;2BAGYxK,UAAwB;UAAdhE,OAAc,uEAAJ,EAAI;;UAChC,CAACA,OAAO,CAAC4O,aAAT,IAA0B,CAAC5O,OAAO,CAAC6O,YAAvC,EAAqD;eAC5C,IAAP;;;aAEK,IAAId,WAAJ,CAAgB/J,QAAhB,EAA0BhE,OAA1B,CAAP;;;;uBAGUgE,QAAZ,EAAoC;QAAdhE,OAAc,uEAAJ,EAAI;;;;QAC9B,CAACA,OAAO,CAAC4O,aAAT,IAA0B,CAAC5O,OAAO,CAAC6O,YAAvC,EAAqD;YAC7C,IAAI/O,KAAJ,CAAU,sDAAV,CAAN;;;SAGGkE,QAAL,GAAgBA,QAAhB;;SACK8K,gBAAL,CAAsB9O,OAAtB;;;;;qCAGeA,SAAS;cAChBA,OAAO,CAAC+O,UAAhB;aACO,KAAL;aACK,KAAL;eACOC,OAAL,GAAe,CAAf;;;aAEG,KAAL;aACK,KAAL;eACOA,OAAL,GAAe,CAAf;;;aAEG,SAAL;eACOA,OAAL,GAAe,CAAf;;;;eAGKA,OAAL,GAAe,CAAf;;;;UAIEC,OAAO,GAAG;QACd5K,MAAM,EAAE;OADV;;cAIQ,KAAK2K,OAAb;aACO,CAAL;aACK,CAAL;aACK,CAAL;eACOE,sBAAL,CAA4B,KAAKF,OAAjC,EAA0CC,OAA1C,EAAmDjP,OAAnD;;;;aAEG,CAAL;eACOmP,kBAAL,CAAwBF,OAAxB,EAAiCjP,OAAjC;;;;;WAICwJ,UAAL,GAAkB,KAAKxF,QAAL,CAAcqF,GAAd,CAAkB4F,OAAlB,CAAlB;;;;2CAGqBG,GAAGH,SAASjP,SAAS;UACtCqP,CAAJ,EAAOC,WAAP;;cACQF,CAAR;aACO,CAAL;UACEC,CAAC,GAAG,CAAJ;eACKE,OAAL,GAAe,EAAf;UACAD,WAAW,GAAGE,gBAAgB,CAACxP,OAAO,CAACsP,WAAT,CAA9B;;;aAEG,CAAL;UACED,CAAC,GAAG,CAAJ;eACKE,OAAL,GAAe,GAAf;UACAD,WAAW,GAAGG,gBAAgB,CAACzP,OAAO,CAACsP,WAAT,CAA9B;;;aAEG,CAAL;UACED,CAAC,GAAG,CAAJ;eACKE,OAAL,GAAe,GAAf;UACAD,WAAW,GAAGG,gBAAgB,CAACzP,OAAO,CAACsP,WAAT,CAA9B;;;;UAIEI,kBAAkB,GAAGC,qBAAqB,CAAC3P,OAAO,CAAC6O,YAAT,CAAhD;UACMe,mBAAmB,GAAG5P,OAAO,CAAC4O,aAAR,GACxBe,qBAAqB,CAAC3P,OAAO,CAAC4O,aAAT,CADG,GAExBc,kBAFJ;UAIMG,kBAAkB,GAAGC,sBAAsB,CAC/CT,CAD+C,EAE/C,KAAKE,OAF0C,EAG/CG,kBAH+C,EAI/CE,mBAJ+C,CAAjD;WAMKG,aAAL,GAAqBC,sBAAsB,CACzCX,CADyC,EAEzC,KAAKE,OAFoC,EAGzC,KAAKvL,QAAL,CAAciM,GAH2B,EAIzCP,kBAJyC,EAKzCG,kBALyC,EAMzCP,WANyC,CAA3C;UAQIY,iBAAJ;;UACIb,CAAC,KAAK,CAAV,EAAa;QACXa,iBAAiB,GAAGC,iBAAiB,CAAC,KAAKJ,aAAN,CAArC;OADF,MAEO;QACLG,iBAAiB,GAAGE,mBAAmB,CACrC,KAAKpM,QAAL,CAAciM,GADuB,EAErC,KAAKF,aAFgC,CAAvC;;;MAMFd,OAAO,CAACoB,CAAR,GAAYjB,CAAZ;;UACIA,CAAC,IAAI,CAAT,EAAY;QACVH,OAAO,CAACxK,MAAR,GAAiB,KAAK8K,OAAtB;;;UAEEH,CAAC,KAAK,CAAV,EAAa;QACXH,OAAO,CAACqB,EAAR,GAAa;UACXC,KAAK,EAAE;YACLC,SAAS,EAAE,SADN;YAELC,GAAG,EAAE,OAFA;YAGLhM,MAAM,EAAE,KAAK8K,OAAL,GAAe;;SAJ3B;QAOAN,OAAO,CAACyB,IAAR,GAAe,OAAf;QACAzB,OAAO,CAAC0B,IAAR,GAAe,OAAf;;;MAEF1B,OAAO,CAAC2B,CAAR,GAAYvB,CAAZ;MACAJ,OAAO,CAAC4B,CAAR,GAAYxC,iBAAiB,CAACwB,kBAAD,CAA7B;MACAZ,OAAO,CAAC6B,CAAR,GAAYzC,iBAAiB,CAAC6B,iBAAD,CAA7B;MACAjB,OAAO,CAAC8B,CAAR,GAAYzB,WAAZ;;;;uCAGiBL,SAASjP,SAAS;WAC9BuP,OAAL,GAAe,GAAf;UACMD,WAAW,GAAGG,gBAAgB,CAACzP,OAAO,CAACsP,WAAT,CAApC;UAEM0B,qBAAqB,GAAGC,iBAAiB,CAACjR,OAAO,CAAC6O,YAAT,CAA/C;UACMqC,sBAAsB,GAAGlR,OAAO,CAAC4O,aAAR,GAC3BqC,iBAAiB,CAACjR,OAAO,CAAC4O,aAAT,CADU,GAE3BoC,qBAFJ;WAIKjB,aAAL,GAAqBoB,kBAAkB,CACrCpD,WAAW,CAACqD,uBADyB,CAAvC;UAGMlB,iBAAiB,GAAGmB,iBAAiB,CACzCL,qBADyC,EAEzCjD,WAAW,CAACqD,uBAF6B,CAA3C;UAIME,WAAW,GAAGhD,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CAClBrB,iBAAiB,CAACsB,KAAlB,CAAwB/P,KAAxB,CAA8B,EAA9B,EAAkC,EAAlC,CADkB,EAElB,CAFkB,CAApB;UAIMgQ,sBAAsB,GAAGC,sBAAsB,CACnDV,qBADmD,EAEnDM,WAFmD,EAGnD,KAAKvB,aAH8C,CAArD;UAKMF,kBAAkB,GAAG8B,kBAAkB,CAC3CT,sBAD2C,EAE3ChB,iBAF2C,EAG3CnC,WAAW,CAACqD,uBAH+B,CAA7C;UAKMQ,YAAY,GAAGtD,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CACnB1B,kBAAkB,CAAC2B,KAAnB,CAAyB/P,KAAzB,CAA+B,EAA/B,EAAmC,EAAnC,CADmB,EAEnB,CAFmB,CAArB;UAIMoQ,uBAAuB,GAAGC,uBAAuB,CACrDZ,sBADqD,EAErDU,YAFqD,EAGrD1B,iBAHqD,EAIrD,KAAKH,aAJgD,CAAvD;UAMMgC,UAAU,GAAGC,yBAAyB,CAC1C1C,WAD0C,EAE1C,KAAKS,aAFqC,EAG1ChC,WAAW,CAACqD,uBAH8B,CAA5C;MAMAnC,OAAO,CAACoB,CAAR,GAAY,CAAZ;MACApB,OAAO,CAACxK,MAAR,GAAiB,KAAK8K,OAAtB;MACAN,OAAO,CAACqB,EAAR,GAAa;QACXC,KAAK,EAAE;UACLC,SAAS,EAAE,SADN;UAELC,GAAG,EAAE,OAFA;UAGLhM,MAAM,EAAE,KAAK8K,OAAL,GAAe;;OAJ3B;MAOAN,OAAO,CAACyB,IAAR,GAAe,OAAf;MACAzB,OAAO,CAAC0B,IAAR,GAAe,OAAf;MACA1B,OAAO,CAAC2B,CAAR,GAAY,CAAZ;MACA3B,OAAO,CAAC4B,CAAR,GAAYxC,iBAAiB,CAACwB,kBAAD,CAA7B;MACAZ,OAAO,CAACgD,EAAR,GAAa5D,iBAAiB,CAACwD,uBAAD,CAA9B;MACA5C,OAAO,CAAC6B,CAAR,GAAYzC,iBAAiB,CAAC6B,iBAAD,CAA7B;MACAjB,OAAO,CAACiD,EAAR,GAAa7D,iBAAiB,CAACoD,sBAAD,CAA9B;MACAxC,OAAO,CAAC8B,CAAR,GAAYzB,WAAZ;MACAL,OAAO,CAACkD,KAAR,GAAgB9D,iBAAiB,CAAC0D,UAAD,CAAjC;;;;iCAGWK,KAAKjO,KAAK;UACjBkO,MAAJ;;UACI,KAAKrD,OAAL,GAAe,CAAnB,EAAsB;QACpBqD,MAAM,GAAG,KAAKtC,aAAL,CACNuC,KADM,GAENtN,MAFM,CAGLsJ,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CACE,CACG,CAACa,GAAG,GAAG,IAAP,KAAgB,EAAjB,GACG,CAACA,GAAG,GAAG,MAAP,KAAkB,CADrB,GAEIA,GAAG,IAAI,CAAR,GAAa,MAFhB,GAGGjO,GAAG,GAAG,IAJX,EAKE,CAACA,GAAG,GAAG,MAAP,KAAkB,EALpB,CADF,EAQE,CARF,CAHK,CAAT;;;UAgBE,KAAK6K,OAAL,KAAiB,CAAjB,IAAsB,KAAKA,OAAL,KAAiB,CAA3C,EAA8C;YACxC7O,IAAG,GAAGmO,QAAQ,CAACC,GAAT,CAAa8D,MAAb,CAAV;;QACAlS,IAAG,CAACoS,QAAJ,GAAe1O,IAAI,CAAC2O,GAAL,CAAS,EAAT,EAAa,KAAKjD,OAAL,GAAe,CAAf,GAAmB,CAAhC,CAAf;eACO,UAAAhL,MAAM;iBACX8J,iBAAiB,CACfC,QAAQ,CAACmE,GAAT,CAAaC,OAAb,CAAqBpE,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CAA8BhN,MAA9B,CAArB,EAA4DpE,IAA5D,EACGwS,UAFY,CADN;SAAb;;;UAOExS,GAAJ;;UACI,KAAK6O,OAAL,KAAiB,CAArB,EAAwB;QACtB7O,GAAG,GAAGmO,QAAQ,CAACC,GAAT,CACJ8D,MAAM,CAACrN,MAAP,CAAcsJ,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CAA8B,CAAC,UAAD,CAA9B,EAA4C,CAA5C,CAAd,CADI,CAAN;OADF,MAIO;QACLpR,GAAG,GAAG,KAAK4P,aAAX;;;UAGI6C,EAAE,GAAG7E,WAAW,CAACqD,uBAAZ,CAAoC,EAApC,CAAX;UACMpR,OAAO,GAAG;QACd6S,IAAI,EAAEvE,QAAQ,CAACuE,IAAT,CAAcC,GADN;QAEdC,OAAO,EAAEzE,QAAQ,CAAChN,GAAT,CAAa0R,KAFR;QAGdJ,EAAE,EAAFA;OAHF;aAMO,UAAArO,MAAM;eACX8J,iBAAiB,CACfuE,EAAE,CACCN,KADH,GAEGtN,MAFH,CAGIsJ,QAAQ,CAAC2E,GAAT,CAAaP,OAAb,CACEpE,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CAA8BhN,MAA9B,CADF,EAEEpE,GAFF,EAGEH,OAHF,EAIE2S,UAPN,CADe,CADN;OAAb;;;;0BAcI;WACCnJ,UAAL,CAAgBxH,GAAhB;;;;;;;AAIJ,SAASwN,gBAAT,GAAiD;MAAvB0D,gBAAuB,uEAAJ,EAAI;MAC3C5D,WAAW,GAAG,cAAc,CAAhC;;MACI4D,gBAAgB,CAACC,QAArB,EAA+B;IAC7B7D,WAAW,IAAI,CAAf;;;MAEE4D,gBAAgB,CAACE,SAArB,EAAgC;IAC9B9D,WAAW,IAAI,CAAf;;;MAEE4D,gBAAgB,CAACG,OAArB,EAA8B;IAC5B/D,WAAW,IAAI,EAAf;;;MAEE4D,gBAAgB,CAACI,UAArB,EAAiC;IAC/BhE,WAAW,IAAI,EAAf;;;SAEKA,WAAP;;;AAGF,SAASG,gBAAT,GAAiD;MAAvByD,gBAAuB,uEAAJ,EAAI;MAC3C5D,WAAW,GAAG,cAAc,CAAhC;;MACI4D,gBAAgB,CAACC,QAAjB,KAA8B,eAAlC,EAAmD;IACjD7D,WAAW,IAAI,CAAf;;;MAEE4D,gBAAgB,CAACC,QAAjB,KAA8B,gBAAlC,EAAoD;IAClD7D,WAAW,IAAI,IAAf;;;MAEE4D,gBAAgB,CAACE,SAArB,EAAgC;IAC9B9D,WAAW,IAAI,CAAf;;;MAEE4D,gBAAgB,CAACG,OAArB,EAA8B;IAC5B/D,WAAW,IAAI,EAAf;;;MAEE4D,gBAAgB,CAACI,UAArB,EAAiC;IAC/BhE,WAAW,IAAI,EAAf;;;MAEE4D,gBAAgB,CAACK,YAArB,EAAmC;IACjCjE,WAAW,IAAI,GAAf;;;MAEE4D,gBAAgB,CAACM,oBAArB,EAA2C;IACzClE,WAAW,IAAI,GAAf;;;MAEE4D,gBAAgB,CAACO,gBAArB,EAAuC;IACrCnE,WAAW,IAAI,IAAf;;;SAEKA,WAAP;;;AAGF,SAASa,iBAAT,CAA2BJ,aAA3B,EAA0C;SACjCzB,QAAQ,CAACmE,GAAT,CAAaC,OAAb,CAAqB/C,qBAAqB,EAA1C,EAA8CI,aAA9C,EACJ4C,UADH;;;AAIF,SAASvC,mBAAT,CAA6BsD,UAA7B,EAAyC3D,aAAzC,EAAwD;MAChD5P,GAAG,GAAG4P,aAAa,CAACuC,KAAd,EAAZ;MACIqB,MAAM,GAAGrF,QAAQ,CAACC,GAAT,CACXoB,qBAAqB,GAAG3K,MAAxB,CAA+BsJ,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CAA8BmC,UAA9B,CAA/B,CADW,CAAb;;OAGK,IAAI3R,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwBA,CAAC,EAAzB,EAA6B;QACrB6R,QAAQ,GAAG/P,IAAI,CAACgQ,IAAL,CAAU1T,GAAG,CAACoS,QAAJ,GAAe,CAAzB,CAAjB;;SACK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,QAApB,EAA8BE,CAAC,EAA/B,EAAmC;MACjC3T,GAAG,CAACqR,KAAJ,CAAUsC,CAAV,IACE/D,aAAa,CAACyB,KAAd,CAAoBsC,CAApB,KAA0B/R,CAAC,GAAIA,CAAC,IAAI,CAAV,GAAgBA,CAAC,IAAI,EAArB,GAA4BA,CAAC,IAAI,EAA3D,CADF;;;IAGF4R,MAAM,GAAGrF,QAAQ,CAACmE,GAAT,CAAaC,OAAb,CAAqBiB,MAArB,EAA6BxT,GAA7B,EAAkCwS,UAA3C;;;SAEKgB,MAAM,CAAC3O,MAAP,CAAcsJ,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CAA8B,IAA9B,EAAoC,EAApC,CAAd,CAAP;;;AAGF,SAASzB,sBAAT,CACET,CADF,EAEEE,OAFF,EAGEG,kBAHF,EAIEE,mBAJF,EAKE;MACIyC,MAAM,GAAGzC,mBAAb;MACI9L,KAAK,GAAGuL,CAAC,IAAI,CAAL,GAAS,EAAT,GAAc,CAA1B;;OACK,IAAItN,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG+B,KAApB,EAA2B/B,CAAC,EAA5B,EAAgC;IAC9BsQ,MAAM,GAAG/D,QAAQ,CAACC,GAAT,CAAa8D,MAAb,CAAT;;;MAGIlS,GAAG,GAAGkS,MAAM,CAACC,KAAP,EAAZ;EACAnS,GAAG,CAACoS,QAAJ,GAAehD,OAAO,GAAG,CAAzB;MACIoE,MAAM,GAAGjE,kBAAb;EACA5L,KAAK,GAAGuL,CAAC,IAAI,CAAL,GAAS,EAAT,GAAc,CAAtB;;OACK,IAAItN,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG+B,KAApB,EAA2B/B,EAAC,EAA5B,EAAgC;QACxB6R,QAAQ,GAAG/P,IAAI,CAACgQ,IAAL,CAAU1T,GAAG,CAACoS,QAAJ,GAAe,CAAzB,CAAjB;;SACK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,QAApB,EAA8BE,CAAC,EAA/B,EAAmC;MACjC3T,GAAG,CAACqR,KAAJ,CAAUsC,CAAV,IAAezB,MAAM,CAACb,KAAP,CAAasC,CAAb,KAAmB/R,EAAC,GAAIA,EAAC,IAAI,CAAV,GAAgBA,EAAC,IAAI,EAArB,GAA4BA,EAAC,IAAI,EAApD,CAAf;;;IAEF4R,MAAM,GAAGrF,QAAQ,CAACmE,GAAT,CAAaC,OAAb,CAAqBiB,MAArB,EAA6BxT,GAA7B,EAAkCwS,UAA3C;;;SAEKgB,MAAP;;;AAGF,SAAS3D,sBAAT,CACEX,CADF,EAEEE,OAFF,EAGEmE,UAHF,EAIEhE,kBAJF,EAKEG,kBALF,EAMEP,WANF,EAOE;MACInP,GAAG,GAAGuP,kBAAkB,CACzB4C,KADO,GAEPtN,MAFO,CAEA6K,kBAFA,EAGP7K,MAHO,CAGAsJ,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CAA8B,CAACwC,YAAY,CAACzE,WAAD,CAAb,CAA9B,EAA2D,CAA3D,CAHA,EAIPtK,MAJO,CAIAsJ,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CAA8BmC,UAA9B,CAJA,CAAV;MAKM5P,KAAK,GAAGuL,CAAC,IAAI,CAAL,GAAS,EAAT,GAAc,CAA5B;;OACK,IAAItN,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG+B,KAApB,EAA2B/B,CAAC,EAA5B,EAAgC;IAC9B5B,GAAG,GAAGmO,QAAQ,CAACC,GAAT,CAAapO,GAAb,CAAN;IACAA,GAAG,CAACoS,QAAJ,GAAehD,OAAO,GAAG,CAAzB;;;SAEKpP,GAAP;;;AAGF,SAASkR,iBAAT,CAA2BL,qBAA3B,EAAkDI,uBAAlD,EAA2E;MACnE4C,cAAc,GAAG5C,uBAAuB,CAAC,CAAD,CAA9C;MACM6C,OAAO,GAAG7C,uBAAuB,CAAC,CAAD,CAAvC;SACO9C,QAAQ,CAAC4F,MAAT,CAAgBlD,qBAAqB,CAACsB,KAAtB,GAA8BtN,MAA9B,CAAqCgP,cAArC,CAAhB,EACJhP,MADI,CACGgP,cADH,EAEJhP,MAFI,CAEGiP,OAFH,CAAP;;;AAKF,SAASvC,sBAAT,CACEV,qBADF,EAEEM,WAFF,EAGEvB,aAHF,EAIE;MACM5P,GAAG,GAAGmO,QAAQ,CAAC4F,MAAT,CACVlD,qBAAqB,CAACsB,KAAtB,GAA8BtN,MAA9B,CAAqCsM,WAArC,CADU,CAAZ;MAGMtR,OAAO,GAAG;IACd6S,IAAI,EAAEvE,QAAQ,CAACuE,IAAT,CAAcC,GADN;IAEdC,OAAO,EAAEzE,QAAQ,CAAChN,GAAT,CAAa6S,SAFR;IAGdvB,EAAE,EAAEtE,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CAA8B,IAA9B,EAAoC,EAApC;GAHN;SAKOjD,QAAQ,CAAC2E,GAAT,CAAaP,OAAb,CAAqB3C,aAArB,EAAoC5P,GAApC,EAAyCH,OAAzC,EAAkD2S,UAAzD;;;AAGF,SAAShB,kBAAT,CACET,sBADF,EAEEhB,iBAFF,EAGEkB,uBAHF,EAIE;MACM4C,cAAc,GAAG5C,uBAAuB,CAAC,CAAD,CAA9C;MACM6C,OAAO,GAAG7C,uBAAuB,CAAC,CAAD,CAAvC;SACO9C,QAAQ,CAAC4F,MAAT,CACLhD,sBAAsB,CACnBoB,KADH,GAEGtN,MAFH,CAEUgP,cAFV,EAGGhP,MAHH,CAGUkL,iBAHV,CADK,EAMJlL,MANI,CAMGgP,cANH,EAOJhP,MAPI,CAOGiP,OAPH,CAAP;;;AAUF,SAASnC,uBAAT,CACEZ,sBADF,EAEEU,YAFF,EAGE1B,iBAHF,EAIEH,aAJF,EAKE;MACM5P,GAAG,GAAGmO,QAAQ,CAAC4F,MAAT,CACVhD,sBAAsB,CACnBoB,KADH,GAEGtN,MAFH,CAEU4M,YAFV,EAGG5M,MAHH,CAGUkL,iBAHV,CADU,CAAZ;MAMMlQ,OAAO,GAAG;IACd6S,IAAI,EAAEvE,QAAQ,CAACuE,IAAT,CAAcC,GADN;IAEdC,OAAO,EAAEzE,QAAQ,CAAChN,GAAT,CAAa6S,SAFR;IAGdvB,EAAE,EAAEtE,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CAA8B,IAA9B,EAAoC,EAApC;GAHN;SAKOjD,QAAQ,CAAC2E,GAAT,CAAaP,OAAb,CAAqB3C,aAArB,EAAoC5P,GAApC,EAAyCH,OAAzC,EAAkD2S,UAAzD;;;AAGF,SAASxB,kBAAT,CAA4BC,uBAA5B,EAAqD;SAC5CA,uBAAuB,CAAC,EAAD,CAA9B;;;AAGF,SAASY,yBAAT,CACE1C,WADF,EAEES,aAFF,EAGEqB,uBAHF,EAIE;MACMuC,MAAM,GAAGrF,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CACb,CAACwC,YAAY,CAACzE,WAAD,CAAb,EAA4B,UAA5B,EAAwC,UAAxC,CADa,EAEb,EAFa,EAGbtK,MAHa,CAGNoM,uBAAuB,CAAC,CAAD,CAHjB,CAAf;MAIMpR,OAAO,GAAG;IACd6S,IAAI,EAAEvE,QAAQ,CAACuE,IAAT,CAAcuB,GADN;IAEdrB,OAAO,EAAEzE,QAAQ,CAAChN,GAAT,CAAa6S;GAFxB;SAIO7F,QAAQ,CAAC2E,GAAT,CAAaP,OAAb,CAAqBiB,MAArB,EAA6B5D,aAA7B,EAA4C/P,OAA5C,EAAqD2S,UAA5D;;;AAGF,SAAShD,qBAAT,GAA8C;MAAf0E,QAAe,uEAAJ,EAAI;MACtCzT,GAAG,GAAG4B,MAAM,CAAC8R,KAAP,CAAa,EAAb,CAAZ;MACMzT,MAAM,GAAGwT,QAAQ,CAACxT,MAAxB;MACI0T,KAAK,GAAG,CAAZ;;SACOA,KAAK,GAAG1T,MAAR,IAAkB0T,KAAK,GAAG,EAAjC,EAAqC;QAC7BC,IAAI,GAAGH,QAAQ,CAAC/R,UAAT,CAAoBiS,KAApB,CAAb;;QACIC,IAAI,GAAG,IAAX,EAAiB;YACT,IAAI1U,KAAJ,CAAU,mDAAV,CAAN;;;IAEFc,GAAG,CAAC2T,KAAD,CAAH,GAAaC,IAAb;IACAD,KAAK;;;SAEAA,KAAK,GAAG,EAAf,EAAmB;IACjB3T,GAAG,CAAC2T,KAAD,CAAH,GAAaE,gBAAgB,CAACF,KAAK,GAAG1T,MAAT,CAA7B;IACA0T,KAAK;;;SAEAjG,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CAA8B3Q,GAA9B,CAAP;;;AAGF,SAASqQ,iBAAT,GAA0C;MAAfoD,QAAe,uEAAJ,EAAI;EACxCA,QAAQ,GAAGK,QAAQ,CAACC,kBAAkB,CAAC9H,QAAQ,CAACwH,QAAD,CAAT,CAAnB,CAAnB;MACMxT,MAAM,GAAGgD,IAAI,CAAC2O,GAAL,CAAS,GAAT,EAAc6B,QAAQ,CAACxT,MAAvB,CAAf;MACMD,GAAG,GAAG4B,MAAM,CAAC8R,KAAP,CAAazT,MAAb,CAAZ;;OAEK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlB,MAApB,EAA4BkB,CAAC,EAA7B,EAAiC;IAC/BnB,GAAG,CAACmB,CAAD,CAAH,GAASsS,QAAQ,CAAC/R,UAAT,CAAoBP,CAApB,CAAT;;;SAGKuM,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CAA8B3Q,GAA9B,CAAP;;;AAGF,SAASmT,YAAT,CAAsB7P,IAAtB,EAA4B;SAEvB,CAACA,IAAI,GAAG,IAAR,KAAiB,EAAlB,GACC,CAACA,IAAI,GAAG,MAAR,KAAmB,CADpB,GAEEA,IAAI,IAAI,CAAT,GAAc,MAFf,GAGEA,IAAI,IAAI,EAAT,GAAe,IAJlB;;;AAQF,SAASmK,iBAAT,CAA2BuG,SAA3B,EAAsC;MAC9BC,SAAS,GAAG,EAAlB;;OACK,IAAI9S,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6S,SAAS,CAACrC,QAA9B,EAAwCxQ,CAAC,EAAzC,EAA6C;IAC3C8S,SAAS,CAAC7T,IAAV,CACG4T,SAAS,CAACpD,KAAV,CAAgB3N,IAAI,CAACqH,KAAL,CAAWnJ,CAAC,GAAG,CAAf,CAAhB,KAAuC,KAAK,IAAKA,CAAC,GAAG,CAAd,CAAxC,GAA8D,IADhE;;;SAIKS,MAAM,CAACC,IAAP,CAAYoS,SAAZ,CAAP;;;AAGF,IAAMJ,gBAAgB,GAAG,CACvB,IADuB,EAEvB,IAFuB,EAGvB,IAHuB,EAIvB,IAJuB,EAKvB,IALuB,EAMvB,IANuB,EAOvB,IAPuB,EAQvB,IARuB,EASvB,IATuB,EAUvB,IAVuB,EAWvB,IAXuB,EAYvB,IAZuB,EAavB,IAbuB,EAcvB,IAduB,EAevB,IAfuB,EAgBvB,IAhBuB,EAiBvB,IAjBuB,EAkBvB,IAlBuB,EAmBvB,IAnBuB,EAoBvB,IApBuB,EAqBvB,IArBuB,EAsBvB,IAtBuB,EAuBvB,IAvBuB,EAwBvB,IAxBuB,EAyBvB,IAzBuB,EA0BvB,IA1BuB,EA2BvB,IA3BuB,EA4BvB,IA5BuB,EA6BvB,IA7BuB,EA8BvB,IA9BuB,EA+BvB,IA/BuB,EAgCvB,IAhCuB,CAAzB;;IC1gBQ9Q,SAAW1C,UAAX0C;;IAEFmR;uBACQC,GAAZ,EAAiB;;;SACVA,GAAL,GAAWA,GAAX;SACKC,KAAL,GAAa,EAAb;SACKC,QAAL,GAAgB,KAAhB;SACKC,SAAL,GAAiB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAAjB;;;;;yBAGGC,KAAKC,OAAOC,SAAS;UACpBA,OAAO,IAAI,IAAf,EAAqB;QACnBA,OAAO,GAAG,CAAV;;;MAEFD,KAAK,GAAG,KAAKL,GAAL,CAASO,eAAT,CAAyBF,KAAzB,CAAR;;UAEI,KAAKJ,KAAL,CAAWnU,MAAX,KAAsB,CAA1B,EAA6B;YACvBuU,KAAK,CAACvU,MAAN,KAAiB,CAArB,EAAwB;eACjB0U,WAAL,GAAmB,WAAnB;SADF,MAEO,IAAIH,KAAK,CAACvU,MAAN,KAAiB,CAArB,EAAwB;eACxB0U,WAAL,GAAmB,YAAnB;SADK,MAEA,IAAIH,KAAK,CAACvU,MAAN,KAAiB,CAArB,EAAwB;eACxB0U,WAAL,GAAmB,YAAnB;SADK,MAEA;gBACC,IAAIzV,KAAJ,CAAU,qBAAV,CAAN;;OARJ,MAUO,IACJ,KAAKyV,WAAL,KAAqB,WAArB,IAAoCH,KAAK,CAACvU,MAAN,KAAiB,CAAtD,IACC,KAAK0U,WAAL,KAAqB,YAArB,IAAqCH,KAAK,CAACvU,MAAN,KAAiB,CADvD,IAEC,KAAK0U,WAAL,KAAqB,YAArB,IAAqCH,KAAK,CAACvU,MAAN,KAAiB,CAHlD,EAIL;cACM,IAAIf,KAAJ,CAAU,kDAAV,CAAN;;;MAGFuV,OAAO,GAAGxR,IAAI,CAAC2R,GAAL,CAAS,CAAT,EAAY3R,IAAI,CAAC2O,GAAL,CAAS,CAAT,EAAY6C,OAAZ,CAAZ,CAAV;WACKL,KAAL,CAAWhU,IAAX,CAAgB,CAACmU,GAAD,EAAMC,KAAN,EAAaC,OAAb,CAAhB;aACO,IAAP;;;;iCAGWI,KAAKC,KAAKC,KAAKC,KAAKC,IAAIC,IAAI;WAClCZ,SAAL,GAAiB,CAACO,GAAD,EAAMC,GAAN,EAAWC,GAAX,EAAgBC,GAAhB,EAAqBC,EAArB,EAAyBC,EAAzB,CAAjB;aACO,IAAP;;;;0BAGIC,GAAG;UACHC,EAAJ;UACMC,WAAW,GAAG,KAAKjB,KAAL,CAAWnU,MAA/B;;UACIoV,WAAW,KAAK,CAApB,EAAuB;;;;WAGlBhB,QAAL,GAAgB,IAAhB;WACKiB,MAAL,GAAcH,CAAd,CAPO;;UAUDhV,IAAI,GAAG,KAAKiU,KAAL,CAAWiB,WAAW,GAAG,CAAzB,CAAb;;UACIlV,IAAI,CAAC,CAAD,CAAJ,GAAU,CAAd,EAAiB;aACViU,KAAL,CAAWhU,IAAX,CAAgB,CAAC,CAAD,EAAID,IAAI,CAAC,CAAD,CAAR,EAAaA,IAAI,CAAC,CAAD,CAAjB,CAAhB;;;UAGIoV,MAAM,GAAG,EAAf;UACMC,MAAM,GAAG,EAAf;UACMpB,KAAK,GAAG,EAAd;;WAEK,IAAIjT,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkU,WAAW,GAAG,CAAlC,EAAqClU,CAAC,EAAtC,EAA0C;QACxCqU,MAAM,CAACpV,IAAP,CAAY,CAAZ,EAAe,CAAf;;YACIe,CAAC,GAAG,CAAJ,KAAUkU,WAAd,EAA2B;UACzBE,MAAM,CAACnV,IAAP,CAAY,KAAKgU,KAAL,CAAWjT,CAAC,GAAG,CAAf,EAAkB,CAAlB,CAAZ;;;QAGFiU,EAAE,GAAG,KAAKjB,GAAL,CAAS1L,GAAT,CAAa;UAChBgN,YAAY,EAAE,CADE;UAEhBC,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAFQ;UAGhBrP,EAAE,EAAE,KAAK+N,KAAL,CAAWjT,CAAC,GAAG,CAAf,EAAkB,CAAlB,CAHY;UAIhBmF,EAAE,EAAE,KAAK8N,KAAL,CAAWjT,CAAC,GAAG,CAAf,EAAkB,CAAlB,CAJY;UAKhBwU,CAAC,EAAE;SALA,CAAL;QAQAvB,KAAK,CAAChU,IAAN,CAAWgV,EAAX;QACAA,EAAE,CAAChU,GAAH;OAlCK;;;UAsCHiU,WAAW,KAAK,CAApB,EAAuB;QACrBD,EAAE,GAAGhB,KAAK,CAAC,CAAD,CAAV;OADF,MAEO;QACLgB,EAAE,GAAG,KAAKjB,GAAL,CAAS1L,GAAT,CAAa;UAChBgN,YAAY,EAAE,CADE;;UAEhBC,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAFQ;UAGhBE,SAAS,EAAExB,KAHK;UAIhByB,MAAM,EAAEN,MAJQ;UAKhBO,MAAM,EAAEN;SALL,CAAL;QAQAJ,EAAE,CAAChU,GAAH;;;WAGGiC,EAAL,eAAe,EAAE,KAAK8Q,GAAL,CAAS4B,UAA1B;UAEMC,MAAM,GAAG,KAAKA,MAAL,CAAYZ,EAAZ,CAAf;MACAY,MAAM,CAAC5U,GAAP;UAEM6U,OAAO,GAAG,KAAK9B,GAAL,CAAS1L,GAAT,CAAa;QAC3BI,IAAI,EAAE,SADqB;QAE3BqN,WAAW,EAAE,CAFc;QAG3BC,OAAO,EAAEH,MAHkB;QAI3BI,MAAM,EAAE,KAAKd,MAAL,CAAY1S,GAAZ,CAAgBG,MAAhB;OAJM,CAAhB;MAOAkT,OAAO,CAAC7U,GAAR;;UAEI,KAAKgT,KAAL,CAAWxH,IAAX,CAAgB,UAAAyJ,IAAI;eAAIA,IAAI,CAAC,CAAD,CAAJ,GAAU,CAAd;OAApB,CAAJ,EAA0C;YACpCC,IAAI,GAAG,KAAKC,eAAL,EAAX;QACAD,IAAI,CAAC3B,WAAL,GAAmB,YAAnB;;mDAEiB,KAAKP,KAJkB;;;;8DAIX;gBAApBiC,IAAoB;YAC3BC,IAAI,CAACD,IAAL,CAAUA,IAAI,CAAC,CAAD,CAAd,EAAmB,CAACA,IAAI,CAAC,CAAD,CAAL,CAAnB;;;;;;;;QAGFC,IAAI,GAAGA,IAAI,CAACE,KAAL,CAAW,KAAKlB,MAAhB,CAAP;YAEMmB,QAAQ,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,KAAKtC,GAAL,CAASuC,IAAT,CAAcpO,KAArB,EAA4B,KAAK6L,GAAL,CAASuC,IAAT,CAAcnO,MAA1C,CAAjB;YAEMoO,IAAI,GAAG,KAAKxC,GAAL,CAAS1L,GAAT,CAAa;UACxBI,IAAI,EAAE,SADkB;UAExB+N,OAAO,EAAE,MAFe;UAGxBC,QAAQ,EAAE,CAHc;UAIxBC,IAAI,EAAEL,QAJkB;UAKxBM,KAAK,EAAE;YACLlO,IAAI,EAAE,OADD;YAELmO,CAAC,EAAE,cAFE;YAGLC,EAAE,EAAE;WARkB;UAUxB9N,SAAS,EAAE;YACTR,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR,EAAgB,QAAhB,EAA0B,QAA1B,EAAoC,QAApC,CADA;YAETa,OAAO,EAAE;cACP0N,GAAG,EAAEZ;;;SAbE,CAAb;QAkBAK,IAAI,CAAC7S,KAAL,CAAW,sBAAX;QACA6S,IAAI,CAACvV,GAAL,WAAYqV,QAAQ,CAAChW,IAAT,CAAc,GAAd,CAAZ;YAEM0W,MAAM,GAAG,KAAKhD,GAAL,CAAS1L,GAAT,CAAa;UAC1BI,IAAI,EAAE,WADoB;UAE1BuO,KAAK,EAAE;YACLvO,IAAI,EAAE,MADD;YAELmO,CAAC,EAAE,YAFE;YAGLK,CAAC,EAAEV;;SALQ,CAAf;QASAQ,MAAM,CAAC/V,GAAP;YAEMkW,cAAc,GAAG,KAAKnD,GAAL,CAAS1L,GAAT,CAAa;UAClCI,IAAI,EAAE,SAD4B;UAElCqN,WAAW,EAAE,CAFqB;UAGlCqB,SAAS,EAAE,CAHuB;UAIlCC,UAAU,EAAE,CAJsB;UAKlCV,IAAI,EAAEL,QAL4B;UAMlCgB,KAAK,EAAEhB,QAAQ,CAAC,CAAD,CANmB;UAOlCiB,KAAK,EAAEjB,QAAQ,CAAC,CAAD,CAPmB;UAQlCtN,SAAS,EAAE;YACTR,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR,EAAgB,QAAhB,EAA0B,QAA1B,EAAoC,QAApC,CADA;YAETa,OAAO,EAAE;cACP0N,GAAG,EAAEjB;aAHE;YAKT1M,SAAS,EAAE;cACToO,GAAG,EAAER;;;SAdY,CAAvB;QAmBAG,cAAc,CAACxT,KAAf,CAAqB,8BAArB;QACAwT,cAAc,CAAClW,GAAf,WAAsBqV,QAAQ,CAAChW,IAAT,CAAc,GAAd,CAAtB;aAEK0T,GAAL,CAASuC,IAAT,CAAckB,QAAd,CAAuB,KAAKvU,EAA5B,IAAkCiU,cAAlC;OAlEF,MAmEO;aACAnD,GAAL,CAASuC,IAAT,CAAckB,QAAd,CAAuB,KAAKvU,EAA5B,IAAkC4S,OAAlC;;;aAGKA,OAAP;;;;0BAGI4B,QAAQ;;0CAEqB,KAAK1D,GAAL,CAAS2D,IAF9B;UAELC,EAFK;UAEDC,EAFC;UAEGC,EAFH;UAEOC,EAFP;UAEWC,EAFX;UAEeC,EAFf;;2CAGyB,KAAK9D,SAH9B;UAGLO,GAHK;UAGAC,GAHA;UAGKC,GAHL;UAGUC,GAHV;UAGeC,EAHf;UAGmBC,EAHnB;;UAINC,CAAC,GAAG,CACR4C,EAAE,GAAGlD,GAAL,GAAWoD,EAAE,GAAGnD,GADR,EAERkD,EAAE,GAAGnD,GAAL,GAAWqD,EAAE,GAAGpD,GAFR,EAGRiD,EAAE,GAAGhD,GAAL,GAAWkD,EAAE,GAAGjD,GAHR,EAIRgD,EAAE,GAAGjD,GAAL,GAAWmD,EAAE,GAAGlD,GAJR,EAKR+C,EAAE,GAAG9C,EAAL,GAAUgD,EAAE,GAAG/C,EAAf,GAAoBiD,EALZ,EAMRH,EAAE,GAAG/C,EAAL,GAAUiD,EAAE,GAAGhD,EAAf,GAAoBkD,EANZ,CAAV;;UASI,CAAC,KAAK/D,QAAN,IAAkBc,CAAC,CAAC1U,IAAF,CAAO,GAAP,MAAgB,KAAK6U,MAAL,CAAY7U,IAAZ,CAAiB,GAAjB,CAAtC,EAA6D;aACtD+V,KAAL,CAAWrB,CAAX;;;WAEGhB,GAAL,CAASkE,cAAT,CAAwB,SAAxB,EAAmCR,MAAnC;;UACMS,EAAE,GAAGT,MAAM,GAAG,KAAH,GAAW,KAA5B;aACO,KAAK1D,GAAL,CAASoE,UAAT,YAAwB,KAAKlV,EAA7B,cAAmCiV,EAAnC,EAAP;;;;;;;IAIEE;;;;;6BACQrE,GAAZ,EAAiBsE,EAAjB,EAAqBC,EAArB,EAAyBC,EAAzB,EAA6BC,EAA7B,EAAiC;;;;;8BACzBzE,GAAN;UACKsE,EAAL,GAAUA,EAAV;UACKC,EAAL,GAAUA,EAAV;UACKC,EAAL,GAAUA,EAAV;UACKC,EAAL,GAAUA,EAAV;;;;;;2BAGKxD,IAAI;aACF,KAAKjB,GAAL,CAAS1L,GAAT,CAAa;QAClBoQ,WAAW,EAAE,CADK;QAElBpP,UAAU,EAAE,KAAKkL,WAFC;QAGlBmE,MAAM,EAAE,CAAC,KAAKL,EAAN,EAAU,KAAKC,EAAf,EAAmB,KAAKC,EAAxB,EAA4B,KAAKC,EAAjC,CAHU;QAIlBG,QAAQ,EAAE3D,EAJQ;QAKlB4D,MAAM,EAAE,CAAC,IAAD,EAAO,IAAP;OALH,CAAP;;;;sCASgB;aACT,IAAIR,iBAAJ,CAAsB,KAAKrE,GAA3B,EAAgC,KAAKsE,EAArC,EAAyC,KAAKC,EAA9C,EAAkD,KAAKC,EAAvD,EAA2D,KAAKC,EAAhE,CAAP;;;;;EApB4B1E;;IAwB1B+E;;;;;6BACQ9E,GAAZ,EAAiBsE,EAAjB,EAAqBC,EAArB,EAAyBQ,EAAzB,EAA6BP,EAA7B,EAAiCC,EAAjC,EAAqCO,EAArC,EAAyC;;;;;gCACjChF,GAAN;WACKA,GAAL,GAAWA,GAAX;WACKsE,EAAL,GAAUA,EAAV;WACKC,EAAL,GAAUA,EAAV;WACKQ,EAAL,GAAUA,EAAV;WACKP,EAAL,GAAUA,EAAV;WACKC,EAAL,GAAUA,EAAV;WACKO,EAAL,GAAUA,EAAV;;;;;;2BAGK/D,IAAI;aACF,KAAKjB,GAAL,CAAS1L,GAAT,CAAa;QAClBoQ,WAAW,EAAE,CADK;QAElBpP,UAAU,EAAE,KAAKkL,WAFC;QAGlBmE,MAAM,EAAE,CAAC,KAAKL,EAAN,EAAU,KAAKC,EAAf,EAAmB,KAAKQ,EAAxB,EAA4B,KAAKP,EAAjC,EAAqC,KAAKC,EAA1C,EAA8C,KAAKO,EAAnD,CAHU;QAIlBJ,QAAQ,EAAE3D,EAJQ;QAKlB4D,MAAM,EAAE,CAAC,IAAD,EAAO,IAAP;OALH,CAAP;;;;sCASgB;aACT,IAAIC,iBAAJ,CACL,KAAK9E,GADA,EAEL,KAAKsE,EAFA,EAGL,KAAKC,EAHA,EAIL,KAAKQ,EAJA,EAKL,KAAKP,EALA,EAML,KAAKC,EANA,EAOL,KAAKO,EAPA,CAAP;;;;;EAvB4BjF;;AAmChC,eAAe;EAAEA,WAAW,EAAXA,WAAF;EAAesE,iBAAiB,EAAjBA,iBAAf;EAAkCS,iBAAiB,EAAjBA;CAAjD;;AC3QA;;;AAIA,IAAMG,qBAAqB,GAAG,CAAC,YAAD,EAAe,WAAf,CAA9B;;IAEMC;4BACQlF,GAAZ,EAAiBmF,IAAjB,EAAuBC,KAAvB,EAA8BC,KAA9B,EAAqCC,MAArC,EAA6C;;;SACtCtF,GAAL,GAAWA,GAAX;SACKmF,IAAL,GAAYA,IAAZ;SACKC,KAAL,GAAaA,KAAb;SACKC,KAAL,GAAaA,KAAb;SACKC,MAAL,GAAcA,MAAd;;;;;oCAGc;;;UAGR/Q,SAAS,GAAG,KAAKyL,GAAL,CAAS1L,GAAT,EAAlB;MACAC,SAAS,CAACtH,GAAV,GAJc;;;0CAOmB,KAAK+S,GAAL,CAAS2D,IAP5B;UAOPC,EAPO;UAOHC,EAPG;UAOCC,EAPD;UAOKC,EAPL;UAOSC,EAPT;UAOaC,EAPb;;UAQPvD,GARO,GAQwB,CARxB;UAQFC,GARE,GAQ2B,CAR3B;UAQGC,GARH,GAQ8B,CAR9B;UAQQC,GARR,GAQiC,CARjC;UAQaC,EARb,GAQoC,CARpC;UAQiBC,EARjB,GAQuC,CARvC;UASRC,CAAC,GAAG,CACR4C,EAAE,GAAGlD,GAAL,GAAWoD,EAAE,GAAGnD,GADR,EAERkD,EAAE,GAAGnD,GAAL,GAAWqD,EAAE,GAAGpD,GAFR,EAGRiD,EAAE,GAAGhD,GAAL,GAAWkD,EAAE,GAAGjD,GAHR,EAIRgD,EAAE,GAAGjD,GAAL,GAAWmD,EAAE,GAAGlD,GAJR,EAKR+C,EAAE,GAAG9C,EAAL,GAAUgD,EAAE,GAAG/C,EAAf,GAAoBiD,EALZ,EAMRH,EAAE,GAAG/C,EAAL,GAAUiD,EAAE,GAAGhD,EAAf,GAAoBkD,EANZ,CAAV;UAQMnC,OAAO,GAAG,KAAK9B,GAAL,CAAS1L,GAAT,CAAa;QAC3BI,IAAI,EAAE,SADqB;QAE3BqN,WAAW,EAAE,CAFc;;QAG3BqB,SAAS,EAAE,CAHgB;;QAI3BC,UAAU,EAAE,CAJe;;QAK3BV,IAAI,EAAE,KAAKwC,IALgB;QAM3B7B,KAAK,EAAE,KAAK8B,KANe;QAO3B7B,KAAK,EAAE,KAAK8B,KAPe;QAQ3BpD,MAAM,EAAEjB,CAAC,CAACvS,GAAF,CAAM,UAAA4L,CAAC;iBAAI,CAACA,CAAC,CAACkL,OAAF,CAAU,CAAV,CAAL;SAAP,CARmB;QAS3BvQ,SAAS,EAAET;OATG,CAAhB;MAWAuN,OAAO,CAAC7U,GAAR,CAAY,KAAKqY,MAAjB;aACOxD,OAAP;;;;8CAGwB;;;;;MAGxBmD,qBAAqB,CAACO,OAAtB,CAA8B,UAAAC,MAAM,EAAI;YAChCC,IAAI,GAAG,KAAI,CAACC,sBAAL,CAA4BF,MAA5B,CAAb;;YAEI,KAAI,CAACzF,GAAL,CAASuC,IAAT,CAAcqD,WAAd,CAA0BF,IAA1B,CAAJ,EAAqC;;YAC/BG,EAAE,GAAG,KAAI,CAAC7F,GAAL,CAAS1L,GAAT,CAAa,CAAC,SAAD,EAAYmR,MAAZ,CAAb,CAAX;;QACAI,EAAE,CAAC5Y,GAAH;QACA,KAAI,CAAC+S,GAAL,CAASuC,IAAT,CAAcqD,WAAd,CAA0BF,IAA1B,IAAkCG,EAAlC;OANF;;;;2CAUqBC,sBAAsB;0BAC9BA,oBAAb;;;;4BAGM;UACF,CAAC,KAAK5W,EAAV,EAAc;aACP8Q,GAAL,CAAS+F,aAAT,GAAyB,KAAK/F,GAAL,CAAS+F,aAAT,GAAyB,CAAlD;aACK7W,EAAL,GAAU,MAAM,KAAK8Q,GAAL,CAAS+F,aAAzB;aACKjE,OAAL,GAAe,KAAKkE,aAAL,EAAf;OAJI;;;UAQF,CAAC,KAAKhG,GAAL,CAASuC,IAAT,CAAckB,QAAd,CAAuB,KAAKvU,EAA5B,CAAL,EAAsC;aAC/B8Q,GAAL,CAASuC,IAAT,CAAckB,QAAd,CAAuB,KAAKvU,EAA5B,IAAkC,KAAK4S,OAAvC;;;;;0BAIE4B,QAAQuC,cAAc;;WAErBC,uBAAL;WACK7D,KAAL;;UAEM8D,eAAe,GAAG,KAAKnG,GAAL,CAASO,eAAT,CAAyB0F,YAAzB,CAAxB;;UACI,CAACE,eAAL,EACE,MAAMpb,KAAK,0CAAmCkb,YAAnC,OAAX,CAPwB;;UAUpBP,IAAI,GAAG,KAAKC,sBAAL,CACX,KAAK3F,GAAL,CAASoG,cAAT,CAAwBD,eAAxB,CADW,CAAb;;WAGKnG,GAAL,CAASkE,cAAT,CAAwBwB,IAAxB,EAA8BhC,MAA9B,EAb0B;;;UAgBpBS,EAAE,GAAGT,MAAM,GAAG,KAAH,GAAW,KAA5B;aACO,KAAK1D,GAAL,CAASoE,UAAT,WACF+B,eAAe,CAAC7Z,IAAhB,CAAqB,GAArB,CADE,eAC4B,KAAK4C,EADjC,cACuCiV,EADvC,EAAP;;;;;;;AAMJ,cAAe;EAAEe,gBAAgB,EAAhBA;CAAjB;;ICjGQnF,gBAAsDsG,SAAtDtG;IAAasE,sBAAyCgC,SAAzChC;IAAmBS,sBAAsBuB,SAAtBvB;IAChCI,qBAAqBpD,QAArBoD;AAER,iBAAe;EACboB,SADa,uBACD;;SAELC,gBAAL,GAAwB,EAAxB;SACKC,aAAL,GAAqB,CAArB;SACKT,aAAL,GAAqB,CAArB;WACQ,KAAKnE,UAAL,GAAkB,CAA1B;GANW;EASbrB,eATa,2BASGF,KATH,EASU;QACjB,OAAOA,KAAP,KAAiB,QAArB,EAA+B;UACzBA,KAAK,CAACoG,MAAN,CAAa,CAAb,MAAoB,GAAxB,EAA6B;YACvBpG,KAAK,CAACvU,MAAN,KAAiB,CAArB,EAAwB;UACtBuU,KAAK,GAAGA,KAAK,CAACxS,OAAN,CACN,kCADM,EAEN,eAFM,CAAR;;;YAKI6Y,GAAG,GAAGC,QAAQ,CAACtG,KAAK,CAAC3T,KAAN,CAAY,CAAZ,CAAD,EAAiB,EAAjB,CAApB;QACA2T,KAAK,GAAG,CAACqG,GAAG,IAAI,EAAR,EAAaA,GAAG,IAAI,CAAR,GAAa,IAAzB,EAA+BA,GAAG,GAAG,IAArC,CAAR;OARF,MASO,IAAIE,WAAW,CAACvG,KAAD,CAAf,EAAwB;QAC7BA,KAAK,GAAGuG,WAAW,CAACvG,KAAD,CAAnB;;;;QAIA5T,KAAK,CAAC8B,OAAN,CAAc8R,KAAd,CAAJ,EAA0B;;UAEpBA,KAAK,CAACvU,MAAN,KAAiB,CAArB,EAAwB;QACtBuU,KAAK,GAAGA,KAAK,CAAC5R,GAAN,CAAU,UAAAoY,IAAI;iBAAIA,IAAI,GAAG,GAAX;SAAd,CAAR,CADsB;OAAxB,MAGO,IAAIxG,KAAK,CAACvU,MAAN,KAAiB,CAArB,EAAwB;QAC7BuU,KAAK,GAAGA,KAAK,CAAC5R,GAAN,CAAU,UAAAoY,IAAI;iBAAIA,IAAI,GAAG,GAAX;SAAd,CAAR;;;aAEKxG,KAAP;;;WAGK,IAAP;GApCW;EAuCbyG,SAvCa,qBAuCHzG,KAvCG,EAuCIqD,MAvCJ,EAuCY;QACnBrD,KAAK,YAAYN,aAArB,EAAkC;MAChCM,KAAK,CAAChI,KAAN,CAAYqL,MAAZ;aACO,IAAP,CAFgC;KAAlC,MAIO,IAAIjX,KAAK,CAAC8B,OAAN,CAAc8R,KAAd,KAAwBA,KAAK,CAAC,CAAD,CAAL,YAAoB6E,kBAAhD,EAAkE;MACvE7E,KAAK,CAAC,CAAD,CAAL,CAAShI,KAAT,CAAeqL,MAAf,EAAuBrD,KAAK,CAAC,CAAD,CAA5B;aACO,IAAP;KAPqB;;;WAUhB,KAAK0G,aAAL,CAAmB1G,KAAnB,EAA0BqD,MAA1B,CAAP;GAjDW;EAoDbqD,aApDa,yBAoDC1G,KApDD,EAoDQqD,MApDR,EAoDgB;IAC3BrD,KAAK,GAAG,KAAKE,eAAL,CAAqBF,KAArB,CAAR;;QACI,CAACA,KAAL,EAAY;aACH,KAAP;;;QAGI8D,EAAE,GAAGT,MAAM,GAAG,KAAH,GAAW,KAA5B;;QACMsD,KAAK,GAAG,KAAKZ,cAAL,CAAoB/F,KAApB,CAAd;;SACK6D,cAAL,CAAoB8C,KAApB,EAA2BtD,MAA3B;;IAEArD,KAAK,GAAGA,KAAK,CAAC/T,IAAN,CAAW,GAAX,CAAR;SACK8X,UAAL,WAAmB/D,KAAnB,cAA4B8D,EAA5B;WAEO,IAAP;GAjEW;EAoEbD,cApEa,0BAoEE8C,KApEF,EAoEStD,MApET,EAoEiB;QACtBS,EAAE,GAAGT,MAAM,GAAG,IAAH,GAAU,IAA3B;WACO,KAAKU,UAAL,YAAoB4C,KAApB,cAA6B7C,EAA7B,EAAP;GAtEW;EAyEbiC,cAzEa,0BAyEE/F,KAzEF,EAyES;WACbA,KAAK,CAACvU,MAAN,KAAiB,CAAjB,GAAqB,YAArB,GAAoC,WAA3C;GA1EW;EA6Ebmb,SA7Ea,qBA6EH5G,KA7EG,EA6EIC,OA7EJ,EA6Ea;QAClB4G,GAAG,GAAG,KAAKJ,SAAL,CAAezG,KAAf,EAAsB,KAAtB,CAAZ;;QACI6G,GAAJ,EAAS;WACFC,WAAL,CAAiB7G,OAAjB;KAHsB;;;;SAQnB8G,UAAL,GAAkB,CAAC/G,KAAD,EAAQC,OAAR,CAAlB;WACO,IAAP;GAtFW;EAyFb+G,WAzFa,uBAyFDhH,KAzFC,EAyFMC,OAzFN,EAyFe;QACpB4G,GAAG,GAAG,KAAKJ,SAAL,CAAezG,KAAf,EAAsB,IAAtB,CAAZ;;QACI6G,GAAJ,EAAS;WACFI,aAAL,CAAmBhH,OAAnB;;;WAEK,IAAP;GA9FW;EAiGbA,OAjGa,mBAiGLA,QAjGK,EAiGI;SACViH,UAAL,CAAgBjH,QAAhB,EAAyBA,QAAzB;;WACO,IAAP;GAnGW;EAsGb6G,WAtGa,uBAsGD7G,OAtGC,EAsGQ;SACdiH,UAAL,CAAgBjH,OAAhB,EAAyB,IAAzB;;WACO,IAAP;GAxGW;EA2GbgH,aA3Ga,yBA2GChH,OA3GD,EA2GU;SAChBiH,UAAL,CAAgB,IAAhB,EAAsBjH,OAAtB;;WACO,IAAP;GA7GW;EAgHbiH,UAhHa,sBAgHFJ,WAhHE,EAgHWG,aAhHX,EAgH0B;QACjC7S,UAAJ,EAAgB+S,IAAhB;;QACIL,WAAW,IAAI,IAAf,IAAuBG,aAAa,IAAI,IAA5C,EAAkD;;;;QAI9CH,WAAW,IAAI,IAAnB,EAAyB;MACvBA,WAAW,GAAGrY,IAAI,CAAC2R,GAAL,CAAS,CAAT,EAAY3R,IAAI,CAAC2O,GAAL,CAAS,CAAT,EAAY0J,WAAZ,CAAZ,CAAd;;;QAEEG,aAAa,IAAI,IAArB,EAA2B;MACzBA,aAAa,GAAGxY,IAAI,CAAC2R,GAAL,CAAS,CAAT,EAAY3R,IAAI,CAAC2O,GAAL,CAAS,CAAT,EAAY6J,aAAZ,CAAZ,CAAhB;;;QAEIlc,GAAG,aAAM+b,WAAN,cAAqBG,aAArB,CAAT;;QAEI,KAAKf,gBAAL,CAAsBnb,GAAtB,CAAJ,EAAgC;iDACT,KAAKmb,gBAAL,CAAsBnb,GAAtB,CADS;;MAC7BqJ,UAD6B;MACjB+S,IADiB;KAAhC,MAEO;MACL/S,UAAU,GAAG;QAAEC,IAAI,EAAE;OAArB;;UAEIyS,WAAW,IAAI,IAAnB,EAAyB;QACvB1S,UAAU,CAACgT,EAAX,GAAgBN,WAAhB;;;UAEEG,aAAa,IAAI,IAArB,EAA2B;QACzB7S,UAAU,CAACiT,EAAX,GAAgBJ,aAAhB;;;MAGF7S,UAAU,GAAG,KAAKH,GAAL,CAASG,UAAT,CAAb;MACAA,UAAU,CAACxH,GAAX;UACMiC,EAAE,GAAG,EAAE,KAAKsX,aAAlB;MACAgB,IAAI,eAAQtY,EAAR,CAAJ;WACKqX,gBAAL,CAAsBnb,GAAtB,IAA6B,CAACqJ,UAAD,EAAa+S,IAAb,CAA7B;;;SAGGjF,IAAL,CAAUoF,WAAV,CAAsBH,IAAtB,IAA8B/S,UAA9B;WACO,KAAK2P,UAAL,YAAoBoD,IAApB,SAAP;GAlJW;EAqJbI,cArJa,0BAqJEtD,EArJF,EAqJMC,EArJN,EAqJUC,EArJV,EAqJcC,EArJd,EAqJkB;WACtB,IAAIJ,mBAAJ,CAAsB,IAAtB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,CAAP;GAtJW;EAyJboD,cAzJa,0BAyJEvD,EAzJF,EAyJMC,EAzJN,EAyJUQ,EAzJV,EAyJcP,EAzJd,EAyJkBC,EAzJlB,EAyJsBO,EAzJtB,EAyJ0B;WAC9B,IAAIF,mBAAJ,CAAsB,IAAtB,EAA4BR,EAA5B,EAAgCC,EAAhC,EAAoCQ,EAApC,EAAwCP,EAAxC,EAA4CC,EAA5C,EAAgDO,EAAhD,CAAP;GA1JW;EA6JblD,OA7Ja,mBA6JLgG,IA7JK,EA6JC1C,KA7JD,EA6JQC,KA7JR,EA6JeC,MA7Jf,EA6JuB;WAC3B,IAAIJ,kBAAJ,CAAqB,IAArB,EAA2B4C,IAA3B,EAAiC1C,KAAjC,EAAwCC,KAAxC,EAA+CC,MAA/C,CAAP;;CA9JJ;AAkKA,IAAIsB,WAAW,GAAG;EAChBmB,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CADK;EAEhBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAFE;EAGhBC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAHU;EAIhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAJI;EAKhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CALS;EAMhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CANS;EAOhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAPQ;EAQhBC,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CARS;EAShBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CATA;EAUhBC,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAVU;EAWhBC,UAAU,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAXI;EAYhBC,KAAK,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CAZS;EAahBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAbK;EAchBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CAdK;EAehBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAfI;EAgBhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAhBK;EAiBhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAjBS;EAkBhBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlBA;EAmBhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnBM;EAoBhBC,OAAO,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CApBO;EAqBhBC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CArBU;EAsBhBC,QAAQ,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAtBM;EAuBhBC,QAAQ,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAvBM;EAwBhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAxBC;EAyBhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzBM;EA0BhBC,SAAS,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,CAAT,CA1BK;EA2BhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3BM;EA4BhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5BK;EA6BhBC,WAAW,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CA7BG;EA8BhBC,cAAc,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CA9BA;EA+BhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CA/BI;EAgChBC,UAAU,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAhCI;EAiChBC,OAAO,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,CAAT,CAjCO;EAkChBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlCI;EAmChBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnCE;EAoChBC,aAAa,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,GAAT,CApCC;EAqChBC,aAAa,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,CArCC;EAsChBC,aAAa,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,CAtCC;EAuChBC,aAAa,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAvCC;EAwChBC,UAAU,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAxCI;EAyChBC,QAAQ,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAzCM;EA0ChBC,WAAW,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CA1CG;EA2ChBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3CO;EA4ChBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5CO;EA6ChBC,UAAU,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA7CI;EA8ChBC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA9CK;EA+ChBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/CG;EAgDhBC,WAAW,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CAhDG;EAiDhBC,OAAO,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAjDO;EAkDhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlDK;EAmDhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnDI;EAoDhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CApDU;EAqDhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CArDK;EAsDhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtDU;EAuDhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAvDU;EAwDhBC,KAAK,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,CAAT,CAxDS;EAyDhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAzDG;EA0DhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA1DM;EA2DhBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3DO;EA4DhBC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA5DK;EA6DhBC,MAAM,EAAE,CAAC,EAAD,EAAK,CAAL,EAAQ,GAAR,CA7DQ;EA8DhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9DS;EA+DhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/DS;EAgEhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhEM;EAiEhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjEC;EAkEhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAlEK;EAmEhBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnEE;EAoEhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApEK;EAqEhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArEI;EAsEhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtEK;EAuEhBC,oBAAoB,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAvEN;EAwEhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAxEK;EAyEhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzEI;EA0EhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA1EK;EA2EhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3EK;EA4EhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5EG;EA6EhBC,aAAa,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA7EC;EA8EhBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9EE;EA+EhBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/EA;EAgFhBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhFA;EAiFhBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjFA;EAkFhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlFG;EAmFhBC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,CAAT,CAnFU;EAoFhBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CApFK;EAqFhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArFS;EAsFhBC,OAAO,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAtFO;EAuFhBC,MAAM,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,CAAT,CAvFQ;EAwFhBC,gBAAgB,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAxFF;EAyFhBC,UAAU,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAzFI;EA0FhBC,YAAY,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CA1FE;EA2FhBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3FE;EA4FhBC,cAAc,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA5FA;EA6FhBC,eAAe,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA7FD;EA8FhBC,iBAAiB,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CA9FH;EA+FhBC,eAAe,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA/FD;EAgGhBC,eAAe,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAhGD;EAiGhBC,YAAY,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,GAAT,CAjGE;EAkGhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlGK;EAmGhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnGK;EAoGhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApGM;EAqGhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArGG;EAsGhBC,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAtGU;EAuGhBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAvGO;EAwGhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAxGS;EAyGhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAzGK;EA0GhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CA1GQ;EA2GhBC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,CAAV,CA3GK;EA4GhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5GQ;EA6GhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA7GC;EA8GhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9GK;EA+GhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/GC;EAgHhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhHC;EAiHhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjHI;EAkHhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlHK;EAmHhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAnHU;EAoHhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApHU;EAqHhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArHU;EAsHhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtHI;EAuHhBC,MAAM,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAvHQ;EAwHhBC,GAAG,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,CAAT,CAxHW;EAyHhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzHK;EA0HhBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA1HK;EA2HhBC,WAAW,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA3HG;EA4HhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5HQ;EA6HhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CA7HI;EA8HhBC,QAAQ,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CA9HM;EA+HhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/HM;EAgIhBC,MAAM,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CAhIQ;EAiIhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjIQ;EAkIhBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlIO;EAmIhBC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAnIK;EAoIhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApIK;EAqIhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArIK;EAsIhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtIU;EAuIhBC,WAAW,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAvIG;EAwIhBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CAxIK;EAyIhBC,GAAG,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzIW;EA0IhBC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CA1IU;EA2IhBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3IO;EA4IhBC,MAAM,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA5IQ;EA6IhBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA7IK;EA8IhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9IQ;EA+IhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/IS;EAgJhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhJS;EAiJhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjJI;EAkJhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAlJQ;EAmJhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX;CAnJf;;ACxKA,IAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoBC,EAApB,EAAwBC,EAAxB;AAEAL,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,CAA9B;AAEA,IAAMC,UAAU,GAAG;EACjBC,CAAC,EAAE,CADc;EAEjB/lB,CAAC,EAAE,CAFc;EAGjBgmB,CAAC,EAAE,CAHc;EAIjB5jB,CAAC,EAAE,CAJc;EAKjB6jB,CAAC,EAAE,CALc;EAMjBC,CAAC,EAAE,CANc;EAOjBC,CAAC,EAAE,CAPc;EAQjB9kB,CAAC,EAAE,CARc;EASjB+kB,CAAC,EAAE,CATc;EAUjB9Q,CAAC,EAAE,CAVc;EAWjB+Q,CAAC,EAAE,CAXc;EAYjBC,CAAC,EAAE,CAZc;EAajBnP,CAAC,EAAE,CAbc;EAcjBoP,CAAC,EAAE,CAdc;EAejBC,CAAC,EAAE,CAfc;EAgBjBC,CAAC,EAAE,CAhBc;EAiBjB7W,CAAC,EAAE,CAjBc;EAkBjBjB,CAAC,EAAE,CAlBc;EAmBjB+X,CAAC,EAAE,CAnBc;EAoBjBC,CAAC,EAAE;CApBL;;AAuBA,IAAMC,KAAK,GAAG,SAARA,KAAQ,CAASC,IAAT,EAAe;MACvBC,GAAJ;MACMC,GAAG,GAAG,EAAZ;MACIC,IAAI,GAAG,EAAX;MACIC,MAAM,GAAG,EAAb;MACIC,YAAY,GAAG,KAAnB;MACIC,MAAM,GAAG,CAAb;;6CAEcN,IARa;;;;wDAQP;UAAXzkB,CAAW;;UACd0jB,UAAU,CAAC1jB,CAAD,CAAV,IAAiB,IAArB,EAA2B;QACzB+kB,MAAM,GAAGrB,UAAU,CAAC1jB,CAAD,CAAnB;;YACI0kB,GAAJ,EAAS;;cAEHG,MAAM,CAAC7mB,MAAP,GAAgB,CAApB,EAAuB;YACrB4mB,IAAI,CAACA,IAAI,CAAC5mB,MAAN,CAAJ,GAAoB,CAAC6mB,MAArB;;;UAEFF,GAAG,CAACA,GAAG,CAAC3mB,MAAL,CAAH,GAAkB;YAAE0mB,GAAG,EAAHA,GAAF;YAAOE,IAAI,EAAJA;WAAzB;UAEAA,IAAI,GAAG,EAAP;UACAC,MAAM,GAAG,EAAT;UACAC,YAAY,GAAG,KAAf;;;QAGFJ,GAAG,GAAG1kB,CAAN;OAdF,MAeO,IACL,CAAC,GAAD,EAAM,GAAN,EAAWglB,QAAX,CAAoBhlB,CAApB,KACCA,CAAC,KAAK,GAAN,IAAa6kB,MAAM,CAAC7mB,MAAP,GAAgB,CAA7B,IAAkC6mB,MAAM,CAACA,MAAM,CAAC7mB,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GADjE,IAECgC,CAAC,KAAK,GAAN,IAAa8kB,YAHT,EAIL;YACID,MAAM,CAAC7mB,MAAP,KAAkB,CAAtB,EAAyB;;;;YAIrB4mB,IAAI,CAAC5mB,MAAL,KAAgB+mB,MAApB,EAA4B;;UAE1BJ,GAAG,CAACA,GAAG,CAAC3mB,MAAL,CAAH,GAAkB;YAAE0mB,GAAG,EAAHA,GAAF;YAAOE,IAAI,EAAJA;WAAzB;UACAA,IAAI,GAAG,CAAC,CAACC,MAAF,CAAP,CAH0B;;cAMtBH,GAAG,KAAK,GAAZ,EAAiB;YACfA,GAAG,GAAG,GAAN;;;cAEEA,GAAG,KAAK,GAAZ,EAAiB;YACfA,GAAG,GAAG,GAAN;;SAVJ,MAYO;UACLE,IAAI,CAACA,IAAI,CAAC5mB,MAAN,CAAJ,GAAoB,CAAC6mB,MAArB;;;QAGFC,YAAY,GAAG9kB,CAAC,KAAK,GAArB,CArBA;;QAwBA6kB,MAAM,GAAG,CAAC,GAAD,EAAM,GAAN,EAAWG,QAAX,CAAoBhlB,CAApB,IAAyBA,CAAzB,GAA6B,EAAtC;OA5BK,MA6BA;QACL6kB,MAAM,IAAI7kB,CAAV;;YACIA,CAAC,KAAK,GAAV,EAAe;UACb8kB,YAAY,GAAG,IAAf;;;KAxDqB;;;;;;;;MA8DvBD,MAAM,CAAC7mB,MAAP,GAAgB,CAApB,EAAuB;QACjB4mB,IAAI,CAAC5mB,MAAL,KAAgB+mB,MAApB,EAA4B;;MAE1BJ,GAAG,CAACA,GAAG,CAAC3mB,MAAL,CAAH,GAAkB;QAAE0mB,GAAG,EAAHA,GAAF;QAAOE,IAAI,EAAJA;OAAzB;MACAA,IAAI,GAAG,CAAC,CAACC,MAAF,CAAP,CAH0B;;UAMtBH,GAAG,KAAK,GAAZ,EAAiB;QACfA,GAAG,GAAG,GAAN;;;UAEEA,GAAG,KAAK,GAAZ,EAAiB;QACfA,GAAG,GAAG,GAAN;;KAVJ,MAYO;MACLE,IAAI,CAACA,IAAI,CAAC5mB,MAAN,CAAJ,GAAoB,CAAC6mB,MAArB;;;;EAIJF,GAAG,CAACA,GAAG,CAAC3mB,MAAL,CAAH,GAAkB;IAAE0mB,GAAG,EAAHA,GAAF;IAAOE,IAAI,EAAJA;GAAzB;SAEOD,GAAP;CAlFF;;AAqFA,IAAMpa,MAAK,GAAG,SAARA,KAAQ,CAAS0a,QAAT,EAAmB/S,GAAnB,EAAwB;;EAEpCkR,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,CAA9B,CAFoC;;OAK/B,IAAIvkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG+lB,QAAQ,CAACjnB,MAA7B,EAAqCkB,CAAC,EAAtC,EAA0C;QAClCc,CAAC,GAAGilB,QAAQ,CAAC/lB,CAAD,CAAlB;;QACI,OAAOgmB,OAAO,CAACllB,CAAC,CAAC0kB,GAAH,CAAd,KAA0B,UAA9B,EAA0C;MACxCQ,OAAO,CAACllB,CAAC,CAAC0kB,GAAH,CAAP,CAAexS,GAAf,EAAoBlS,CAAC,CAAC4kB,IAAtB;;;CARN;;AAaA,IAAMM,OAAO,GAAG;EACdlB,CADc,aACZ9R,GADY,EACPtU,CADO,EACJ;IACRwlB,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CAAN;IACAylB,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CAAN;IACA0lB,EAAE,GAAGC,EAAE,GAAG,IAAV;IACAC,EAAE,GAAGJ,EAAL;IACAK,EAAE,GAAGJ,EAAL;WACOnR,GAAG,CAACiT,MAAJ,CAAW/B,EAAX,EAAeC,EAAf,CAAP;GAPY;EAUdnQ,CAVc,aAUZhB,GAVY,EAUPtU,CAVO,EAUJ;IACRwlB,EAAE,IAAIxlB,CAAC,CAAC,CAAD,CAAP;IACAylB,EAAE,IAAIzlB,CAAC,CAAC,CAAD,CAAP;IACA0lB,EAAE,GAAGC,EAAE,GAAG,IAAV;IACAC,EAAE,GAAGJ,EAAL;IACAK,EAAE,GAAGJ,EAAL;WACOnR,GAAG,CAACiT,MAAJ,CAAW/B,EAAX,EAAeC,EAAf,CAAP;GAhBY;EAmBdO,CAnBc,aAmBZ1R,GAnBY,EAmBPtU,CAnBO,EAmBJ;IACRwlB,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CAAN;IACAylB,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CAAN;IACA0lB,EAAE,GAAG1lB,CAAC,CAAC,CAAD,CAAN;IACA2lB,EAAE,GAAG3lB,CAAC,CAAC,CAAD,CAAN;WACOsU,GAAG,CAACkT,aAAJ,OAAAlT,GAAG,qBAAkBtU,CAAlB,EAAV;GAxBY;EA2BdoC,CA3Bc,aA2BZkS,GA3BY,EA2BPtU,CA3BO,EA2BJ;IACRsU,GAAG,CAACkT,aAAJ,CACExnB,CAAC,CAAC,CAAD,CAAD,GAAOwlB,EADT,EAEExlB,CAAC,CAAC,CAAD,CAAD,GAAOylB,EAFT,EAGEzlB,CAAC,CAAC,CAAD,CAAD,GAAOwlB,EAHT,EAIExlB,CAAC,CAAC,CAAD,CAAD,GAAOylB,EAJT,EAKEzlB,CAAC,CAAC,CAAD,CAAD,GAAOwlB,EALT,EAMExlB,CAAC,CAAC,CAAD,CAAD,GAAOylB,EANT;IAQAC,EAAE,GAAGF,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CAAX;IACA2lB,EAAE,GAAGF,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CAAX;IACAwlB,EAAE,IAAIxlB,CAAC,CAAC,CAAD,CAAP;WACQylB,EAAE,IAAIzlB,CAAC,CAAC,CAAD,CAAf;GAvCY;EA0CdmX,CA1Cc,aA0CZ7C,GA1CY,EA0CPtU,CA1CO,EA0CJ;QACJ0lB,EAAE,KAAK,IAAX,EAAiB;MACfA,EAAE,GAAGF,EAAL;MACAG,EAAE,GAAGF,EAAL;;;IAGFnR,GAAG,CAACkT,aAAJ,CAAkBhC,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAApB,EAAkCC,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAApC,EAAkDzlB,CAAC,CAAC,CAAD,CAAnD,EAAwDA,CAAC,CAAC,CAAD,CAAzD,EAA8DA,CAAC,CAAC,CAAD,CAA/D,EAAoEA,CAAC,CAAC,CAAD,CAArE;IACA0lB,EAAE,GAAG1lB,CAAC,CAAC,CAAD,CAAN;IACA2lB,EAAE,GAAG3lB,CAAC,CAAC,CAAD,CAAN;IACAwlB,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CAAN;WACQylB,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CAAd;GApDY;EAuDdumB,CAvDc,aAuDZjS,GAvDY,EAuDPtU,CAvDO,EAuDJ;QACJ0lB,EAAE,KAAK,IAAX,EAAiB;MACfA,EAAE,GAAGF,EAAL;MACAG,EAAE,GAAGF,EAAL;;;IAGFnR,GAAG,CAACkT,aAAJ,CACEhC,EAAE,IAAIE,EAAE,GAAGF,EAAT,CADJ,EAEEC,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAFJ,EAGED,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CAHR,EAIEylB,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CAJR,EAKEwlB,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CALR,EAMEylB,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CANR;IAQA0lB,EAAE,GAAGF,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CAAX;IACA2lB,EAAE,GAAGF,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CAAX;IACAwlB,EAAE,IAAIxlB,CAAC,CAAC,CAAD,CAAP;WACQylB,EAAE,IAAIzlB,CAAC,CAAC,CAAD,CAAf;GAxEY;EA2EdqmB,CA3Ec,aA2EZ/R,GA3EY,EA2EPtU,CA3EO,EA2EJ;IACR0lB,EAAE,GAAG1lB,CAAC,CAAC,CAAD,CAAN;IACA2lB,EAAE,GAAG3lB,CAAC,CAAC,CAAD,CAAN;IACAwlB,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CAAN;IACAylB,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CAAN;WACOsU,GAAG,CAACmT,gBAAJ,CAAqBznB,CAAC,CAAC,CAAD,CAAtB,EAA2BA,CAAC,CAAC,CAAD,CAA5B,EAAiCwlB,EAAjC,EAAqCC,EAArC,CAAP;GAhFY;EAmFda,CAnFc,aAmFZhS,GAnFY,EAmFPtU,CAnFO,EAmFJ;IACRsU,GAAG,CAACmT,gBAAJ,CAAqBznB,CAAC,CAAC,CAAD,CAAD,GAAOwlB,EAA5B,EAAgCxlB,CAAC,CAAC,CAAD,CAAD,GAAOylB,EAAvC,EAA2CzlB,CAAC,CAAC,CAAD,CAAD,GAAOwlB,EAAlD,EAAsDxlB,CAAC,CAAC,CAAD,CAAD,GAAOylB,EAA7D;IACAC,EAAE,GAAGF,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CAAX;IACA2lB,EAAE,GAAGF,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CAAX;IACAwlB,EAAE,IAAIxlB,CAAC,CAAC,CAAD,CAAP;WACQylB,EAAE,IAAIzlB,CAAC,CAAC,CAAD,CAAf;GAxFY;EA2FdwmB,CA3Fc,aA2FZlS,GA3FY,EA2FPtU,CA3FO,EA2FJ;QACJ0lB,EAAE,KAAK,IAAX,EAAiB;MACfA,EAAE,GAAGF,EAAL;MACAG,EAAE,GAAGF,EAAL;KAFF,MAGO;MACLC,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;MACAG,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;;;IAGFnR,GAAG,CAACmT,gBAAJ,CAAqB/B,EAArB,EAAyBC,EAAzB,EAA6B3lB,CAAC,CAAC,CAAD,CAA9B,EAAmCA,CAAC,CAAC,CAAD,CAApC;IACA0lB,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;IACAG,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;IACAD,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CAAN;WACQylB,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CAAd;GAxGY;EA2GdymB,CA3Gc,aA2GZnS,GA3GY,EA2GPtU,CA3GO,EA2GJ;QACJ0lB,EAAE,KAAK,IAAX,EAAiB;MACfA,EAAE,GAAGF,EAAL;MACAG,EAAE,GAAGF,EAAL;KAFF,MAGO;MACLC,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;MACAG,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;;;IAGFnR,GAAG,CAACmT,gBAAJ,CAAqB/B,EAArB,EAAyBC,EAAzB,EAA6BH,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CAAnC,EAAwCylB,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CAA9C;IACAwlB,EAAE,IAAIxlB,CAAC,CAAC,CAAD,CAAP;WACQylB,EAAE,IAAIzlB,CAAC,CAAC,CAAD,CAAf;GAtHY;EAyHd+lB,CAzHc,aAyHZzR,GAzHY,EAyHPtU,CAzHO,EAyHJ;IACR0nB,QAAQ,CAACpT,GAAD,EAAMkR,EAAN,EAAUC,EAAV,EAAczlB,CAAd,CAAR;IACAwlB,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CAAN;WACQylB,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CAAd;GA5HY;EA+HdA,CA/Hc,aA+HZsU,GA/HY,EA+HPtU,EA/HO,EA+HJ;IACRA,EAAC,CAAC,CAAD,CAAD,IAAQwlB,EAAR;IACAxlB,EAAC,CAAC,CAAD,CAAD,IAAQylB,EAAR;IACAiC,QAAQ,CAACpT,GAAD,EAAMkR,EAAN,EAAUC,EAAV,EAAczlB,EAAd,CAAR;IACAwlB,EAAE,GAAGxlB,EAAC,CAAC,CAAD,CAAN;WACQylB,EAAE,GAAGzlB,EAAC,CAAC,CAAD,CAAd;GApIY;EAuIdmmB,CAvIc,aAuIZ7R,GAvIY,EAuIPtU,CAvIO,EAuIJ;IACRwlB,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CAAN;IACAylB,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CAAN;IACA0lB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACOrR,GAAG,CAACqT,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GA3IY;EA8IdpkB,CA9Ic,aA8IZiT,GA9IY,EA8IPtU,CA9IO,EA8IJ;IACRwlB,EAAE,IAAIxlB,CAAC,CAAC,CAAD,CAAP;IACAylB,EAAE,IAAIzlB,CAAC,CAAC,CAAD,CAAP;IACA0lB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACOrR,GAAG,CAACqT,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GAlJY;EAqJdQ,CArJc,aAqJZ3R,GArJY,EAqJPtU,CArJO,EAqJJ;IACRwlB,EAAE,GAAGxlB,CAAC,CAAC,CAAD,CAAN;IACA0lB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACOrR,GAAG,CAACqT,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GAxJY;EA2JdS,CA3Jc,aA2JZ5R,GA3JY,EA2JPtU,CA3JO,EA2JJ;IACRwlB,EAAE,IAAIxlB,CAAC,CAAC,CAAD,CAAP;IACA0lB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACOrR,GAAG,CAACqT,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GA9JY;EAiKd7V,CAjKc,aAiKZ0E,GAjKY,EAiKPtU,CAjKO,EAiKJ;IACRylB,EAAE,GAAGzlB,CAAC,CAAC,CAAD,CAAN;IACA0lB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACOrR,GAAG,CAACqT,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GApKY;EAuKd9W,CAvKc,aAuKZ2F,GAvKY,EAuKPtU,CAvKO,EAuKJ;IACRylB,EAAE,IAAIzlB,CAAC,CAAC,CAAD,CAAP;IACA0lB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACOrR,GAAG,CAACqT,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GA1KY;EA6KdiB,CA7Kc,aA6KZpS,GA7KY,EA6KP;IACLA,GAAG,CAACsT,SAAJ;IACApC,EAAE,GAAGI,EAAL;WACQH,EAAE,GAAGI,EAAb;GAhLY;EAmLdc,CAnLc,aAmLZrS,GAnLY,EAmLP;IACLA,GAAG,CAACsT,SAAJ;IACApC,EAAE,GAAGI,EAAL;WACQH,EAAE,GAAGI,EAAb;;CAtLJ;;AA0LA,IAAM6B,QAAQ,GAAG,SAAXA,QAAW,CAASpT,GAAT,EAAcxI,CAAd,EAAiB+b,CAAjB,EAAoBC,MAApB,EAA4B;+BACCA,MADD;MACpCC,EADoC;MAChCC,EADgC;MAC5BC,GAD4B;MACvBC,KADuB;MAChBC,KADgB;MACTC,EADS;MACLC,EADK;;MAErCC,IAAI,GAAGC,aAAa,CAACH,EAAD,EAAKC,EAAL,EAASN,EAAT,EAAaC,EAAb,EAAiBE,KAAjB,EAAwBC,KAAxB,EAA+BF,GAA/B,EAAoCnc,CAApC,EAAuC+b,CAAvC,CAA1B;;8CAEgBS,IAJ2B;;;;2DAIrB;UAAbE,GAAa;UACdC,GAAG,GAAGC,eAAe,MAAf,4BAAmBF,GAAnB,EAAZ;MACAlU,GAAG,CAACkT,aAAJ,OAAAlT,GAAG,qBAAkBmU,GAAlB,EAAH;;;;;;;CANJ;;;AAWA,IAAMF,aAAa,GAAG,SAAhBA,aAAgB,CAASzc,CAAT,EAAY+b,CAAZ,EAAeE,EAAf,EAAmBC,EAAnB,EAAuBE,KAAvB,EAA8BC,KAA9B,EAAqCQ,OAArC,EAA8CC,EAA9C,EAAkDC,EAAlD,EAAsD;MACpEC,EAAE,GAAGH,OAAO,IAAIvlB,IAAI,CAAC2lB,EAAL,GAAU,GAAd,CAAlB;MACMC,MAAM,GAAG5lB,IAAI,CAAC6lB,GAAL,CAASH,EAAT,CAAf;MACMI,MAAM,GAAG9lB,IAAI,CAAC+lB,GAAL,CAASL,EAAT,CAAf;EACAf,EAAE,GAAG3kB,IAAI,CAACgmB,GAAL,CAASrB,EAAT,CAAL;EACAC,EAAE,GAAG5kB,IAAI,CAACgmB,GAAL,CAASpB,EAAT,CAAL;EACAtC,EAAE,GAAGwD,MAAM,IAAIN,EAAE,GAAG9c,CAAT,CAAN,GAAoB,GAApB,GAA0Bkd,MAAM,IAAIH,EAAE,GAAGhB,CAAT,CAAN,GAAoB,GAAnD;EACAlC,EAAE,GAAGuD,MAAM,IAAIL,EAAE,GAAGhB,CAAT,CAAN,GAAoB,GAApB,GAA0BmB,MAAM,IAAIJ,EAAE,GAAG9c,CAAT,CAAN,GAAoB,GAAnD;MACIud,EAAE,GAAI3D,EAAE,GAAGA,EAAN,IAAaqC,EAAE,GAAGA,EAAlB,IAAyBpC,EAAE,GAAGA,EAAN,IAAaqC,EAAE,GAAGA,EAAlB,CAAjC;;MACIqB,EAAE,GAAG,CAAT,EAAY;IACVA,EAAE,GAAGjmB,IAAI,CAACkmB,IAAL,CAAUD,EAAV,CAAL;IACAtB,EAAE,IAAIsB,EAAN;IACArB,EAAE,IAAIqB,EAAN;;;MAGIE,GAAG,GAAGL,MAAM,GAAGnB,EAArB;MACMyB,GAAG,GAAGR,MAAM,GAAGjB,EAArB;MACM0B,GAAG,GAAG,CAACT,MAAD,GAAUhB,EAAtB;MACM0B,GAAG,GAAGR,MAAM,GAAGlB,EAArB;MACM2B,EAAE,GAAGJ,GAAG,GAAGX,EAAN,GAAWY,GAAG,GAAGX,EAA5B;MACMe,EAAE,GAAGH,GAAG,GAAGb,EAAN,GAAWc,GAAG,GAAGb,EAA5B;MACMjQ,EAAE,GAAG2Q,GAAG,GAAGzd,CAAN,GAAU0d,GAAG,GAAG3B,CAA3B;MACMhP,EAAE,GAAG4Q,GAAG,GAAG3d,CAAN,GAAU4d,GAAG,GAAG7B,CAA3B;MAEMgC,CAAC,GAAG,CAACjR,EAAE,GAAG+Q,EAAN,KAAa/Q,EAAE,GAAG+Q,EAAlB,IAAwB,CAAC9Q,EAAE,GAAG+Q,EAAN,KAAa/Q,EAAE,GAAG+Q,EAAlB,CAAlC;MACIE,UAAU,GAAG,IAAID,CAAJ,GAAQ,IAAzB;;MACIC,UAAU,GAAG,CAAjB,EAAoB;IAClBA,UAAU,GAAG,CAAb;;;MAEEC,OAAO,GAAG3mB,IAAI,CAACkmB,IAAL,CAAUQ,UAAV,CAAd;;MACI3B,KAAK,KAAKD,KAAd,EAAqB;IACnB6B,OAAO,GAAG,CAACA,OAAX;;;MAGIC,EAAE,GAAG,OAAOL,EAAE,GAAG/Q,EAAZ,IAAkBmR,OAAO,IAAIlR,EAAE,GAAG+Q,EAAT,CAApC;MACMK,EAAE,GAAG,OAAOL,EAAE,GAAG/Q,EAAZ,IAAkBkR,OAAO,IAAInR,EAAE,GAAG+Q,EAAT,CAApC;MAEMO,GAAG,GAAG9mB,IAAI,CAAC+mB,KAAL,CAAWP,EAAE,GAAGK,EAAhB,EAAoBN,EAAE,GAAGK,EAAzB,CAAZ;MACMI,GAAG,GAAGhnB,IAAI,CAAC+mB,KAAL,CAAWtR,EAAE,GAAGoR,EAAhB,EAAoBrR,EAAE,GAAGoR,EAAzB,CAAZ;MAEIK,MAAM,GAAGD,GAAG,GAAGF,GAAnB;;MACIG,MAAM,GAAG,CAAT,IAAclC,KAAK,KAAK,CAA5B,EAA+B;IAC7BkC,MAAM,IAAI,IAAIjnB,IAAI,CAAC2lB,EAAnB;GADF,MAEO,IAAIsB,MAAM,GAAG,CAAT,IAAclC,KAAK,KAAK,CAA5B,EAA+B;IACpCkC,MAAM,IAAI,IAAIjnB,IAAI,CAAC2lB,EAAnB;;;MAGIuB,QAAQ,GAAGlnB,IAAI,CAACgQ,IAAL,CAAUhQ,IAAI,CAACgmB,GAAL,CAASiB,MAAM,IAAIjnB,IAAI,CAAC2lB,EAAL,GAAU,GAAV,GAAgB,KAApB,CAAf,CAAV,CAAjB;MACMwB,MAAM,GAAG,EAAf;;OAEK,IAAIjpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgpB,QAApB,EAA8BhpB,CAAC,EAA/B,EAAmC;QAC3BkpB,GAAG,GAAGN,GAAG,GAAI5oB,CAAC,GAAG+oB,MAAL,GAAeC,QAAjC;QACMG,GAAG,GAAGP,GAAG,GAAI,CAAC5oB,CAAC,GAAG,CAAL,IAAU+oB,MAAX,GAAqBC,QAAvC;IACAC,MAAM,CAACjpB,CAAD,CAAN,GAAY,CAAC0oB,EAAD,EAAKC,EAAL,EAASO,GAAT,EAAcC,GAAd,EAAmB1C,EAAnB,EAAuBC,EAAvB,EAA2BgB,MAA3B,EAAmCE,MAAnC,CAAZ;;;SAGKqB,MAAP;CAxDF;;AA2DA,IAAM7B,eAAe,GAAG,SAAlBA,eAAkB,CAASlD,EAAT,EAAaC,EAAb,EAAiByE,GAAjB,EAAsBE,GAAtB,EAA2BrC,EAA3B,EAA+BC,EAA/B,EAAmCgB,MAAnC,EAA2CE,MAA3C,EAAmD;MACnEK,GAAG,GAAGL,MAAM,GAAGnB,EAArB;MACMyB,GAAG,GAAG,CAACR,MAAD,GAAUhB,EAAtB;MACMyB,GAAG,GAAGT,MAAM,GAAGjB,EAArB;MACM2B,GAAG,GAAGR,MAAM,GAAGlB,EAArB;MAEM0C,OAAO,GAAG,OAAON,GAAG,GAAGF,GAAb,CAAhB;MACMzD,CAAC,GACH,IAAI,CAAL,GAAUrjB,IAAI,CAAC6lB,GAAL,CAASyB,OAAO,GAAG,GAAnB,CAAV,GAAoCtnB,IAAI,CAAC6lB,GAAL,CAASyB,OAAO,GAAG,GAAnB,CAArC,GACAtnB,IAAI,CAAC6lB,GAAL,CAASyB,OAAT,CAFF;MAGM9R,EAAE,GAAG4M,EAAE,GAAGpiB,IAAI,CAAC+lB,GAAL,CAASe,GAAT,CAAL,GAAqBzD,CAAC,GAAGrjB,IAAI,CAAC6lB,GAAL,CAASiB,GAAT,CAApC;MACMrR,EAAE,GAAG4M,EAAE,GAAGriB,IAAI,CAAC6lB,GAAL,CAASiB,GAAT,CAAL,GAAqBzD,CAAC,GAAGrjB,IAAI,CAAC+lB,GAAL,CAASe,GAAT,CAApC;MACMS,EAAE,GAAGnF,EAAE,GAAGpiB,IAAI,CAAC+lB,GAAL,CAASiB,GAAT,CAAhB;MACMQ,EAAE,GAAGnF,EAAE,GAAGriB,IAAI,CAAC6lB,GAAL,CAASmB,GAAT,CAAhB;MACMtR,EAAE,GAAG6R,EAAE,GAAGlE,CAAC,GAAGrjB,IAAI,CAAC6lB,GAAL,CAASmB,GAAT,CAApB;MACMrR,EAAE,GAAG6R,EAAE,GAAGnE,CAAC,GAAGrjB,IAAI,CAAC+lB,GAAL,CAASiB,GAAT,CAApB;SAEO,CACLb,GAAG,GAAG3Q,EAAN,GAAW4Q,GAAG,GAAG3Q,EADZ,EAEL4Q,GAAG,GAAG7Q,EAAN,GAAW8Q,GAAG,GAAG7Q,EAFZ,EAGL0Q,GAAG,GAAGzQ,EAAN,GAAW0Q,GAAG,GAAGzQ,EAHZ,EAIL0Q,GAAG,GAAG3Q,EAAN,GAAW4Q,GAAG,GAAG3Q,EAJZ,EAKLwQ,GAAG,GAAGoB,EAAN,GAAWnB,GAAG,GAAGoB,EALZ,EAMLnB,GAAG,GAAGkB,EAAN,GAAWjB,GAAG,GAAGkB,EANZ,CAAP;CAjBF;;IA2BMC;;;;;;;0BACSvW,KAAKuS,MAAM;UAChBQ,QAAQ,GAAGT,KAAK,CAACC,IAAD,CAAtB;;MACAla,MAAK,CAAC0a,QAAD,EAAW/S,GAAX,CAAL;;;;;;;ICxZIpR,WAAW1C,UAAX0C;;;AAIR,IAAM4nB,KAAK,GAAG,OAAO,CAAC1nB,IAAI,CAACkmB,IAAL,CAAU,CAAV,IAAe,GAAhB,IAAuB,GAA9B,CAAd;AACA,kBAAe;EACbyB,UADa,wBACA;SACN9S,IAAL,GAAY,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAAZ,CADW;;WAEH,KAAK+S,SAAL,GAAiB,EAAzB;GAHW;EAMbC,IANa,kBAMN;SACAD,SAAL,CAAezqB,IAAf,CAAoB,KAAK0X,IAAL,CAAUjX,KAAV,EAApB,EADK;;;WAGE,KAAK0X,UAAL,CAAgB,GAAhB,CAAP;GATW;EAYbwS,OAZa,qBAYH;SACHjT,IAAL,GAAY,KAAK+S,SAAL,CAAeG,GAAf,MAAwB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAApC;WACO,KAAKzS,UAAL,CAAgB,GAAhB,CAAP;GAdW;EAiBbkP,SAjBa,uBAiBD;WACH,KAAKlP,UAAL,CAAgB,GAAhB,CAAP;GAlBW;EAqBb0S,SArBa,qBAqBHC,CArBG,EAqBA;WACJ,KAAK3S,UAAL,WAAmBxV,QAAM,CAACmoB,CAAD,CAAzB,QAAP;GAtBW;EAyBbC,WAAW,EAAE;IACXC,IAAI,EAAE,CADK;IAEXC,KAAK,EAAE,CAFI;IAGXC,MAAM,EAAE;GA5BG;EA+BbC,OA/Ba,mBA+BLtpB,CA/BK,EA+BF;QACL,OAAOA,CAAP,KAAa,QAAjB,EAA2B;MACzBA,CAAC,GAAG,KAAKkpB,WAAL,CAAiBlpB,CAAC,CAACoG,WAAF,EAAjB,CAAJ;;;WAEK,KAAKkQ,UAAL,WAAmBtW,CAAnB,QAAP;GAnCW;EAsCbupB,YAAY,EAAE;IACZC,KAAK,EAAE,CADK;IAEZJ,KAAK,EAAE,CAFK;IAGZK,KAAK,EAAE;GAzCI;EA4CbC,QA5Ca,oBA4CJzY,CA5CI,EA4CD;QACN,OAAOA,CAAP,KAAa,QAAjB,EAA2B;MACzBA,CAAC,GAAG,KAAKsY,YAAL,CAAkBtY,CAAC,CAAC7K,WAAF,EAAlB,CAAJ;;;WAEK,KAAKkQ,UAAL,WAAmBrF,CAAnB,QAAP;GAhDW;EAmDb0Y,UAnDa,sBAmDFzW,CAnDE,EAmDC;WACL,KAAKoD,UAAL,WAAmBxV,QAAM,CAACoS,CAAD,CAAzB,QAAP;GApDW;EAuDb0W,IAvDa,gBAuDR5rB,MAvDQ,EAuDc;QAAdb,OAAc,uEAAJ,EAAI;QACnB0sB,cAAc,GAAG7rB,MAAvB;;QACI,CAACW,KAAK,CAAC8B,OAAN,CAAczC,MAAd,CAAL,EAA4B;MAC1BA,MAAM,GAAG,CAACA,MAAD,EAASb,OAAO,CAAC+b,KAAR,IAAiBlb,MAA1B,CAAT;;;QAGI8rB,KAAK,GAAG9rB,MAAM,CAAC+rB,KAAP,CAAa,UAAArgB,CAAC;aAAIsgB,MAAM,CAACC,QAAP,CAAgBvgB,CAAhB,KAAsBA,CAAC,GAAG,CAA9B;KAAd,CAAd;;QACI,CAACogB,KAAL,EAAY;YACJ,IAAI7sB,KAAJ,gBACIitB,IAAI,CAACC,SAAL,CAAeN,cAAf,CADJ,eACuCK,IAAI,CAACC,SAAL,CACzChtB,OADyC,CADvC,8DAAN;;;IAOFa,MAAM,GAAGA,MAAM,CAAC2C,GAAP,CAAWG,QAAX,EAAmBtC,IAAnB,CAAwB,GAAxB,CAAT;WACO,KAAK8X,UAAL,YAAoBtY,MAApB,eAA+B8C,QAAM,CAAC3D,OAAO,CAACitB,KAAR,IAAiB,CAAlB,CAArC,QAAP;GAvEW;EA0EbC,MA1Ea,oBA0EJ;WACA,KAAK/T,UAAL,CAAgB,QAAhB,CAAP;GA3EW;EA8Eb6O,MA9Ea,kBA8ENzb,CA9EM,EA8EH+b,CA9EG,EA8EA;WACJ,KAAKnP,UAAL,WAAmBxV,QAAM,CAAC4I,CAAD,CAAzB,cAAgC5I,QAAM,CAAC2kB,CAAD,CAAtC,QAAP;GA/EW;EAkFbF,MAlFa,kBAkFN7b,CAlFM,EAkFH+b,CAlFG,EAkFA;WACJ,KAAKnP,UAAL,WAAmBxV,QAAM,CAAC4I,CAAD,CAAzB,cAAgC5I,QAAM,CAAC2kB,CAAD,CAAtC,QAAP;GAnFW;EAsFbL,aAtFa,yBAsFCkF,IAtFD,EAsFOC,IAtFP,EAsFaC,IAtFb,EAsFmBC,IAtFnB,EAsFyB/gB,CAtFzB,EAsF4B+b,CAtF5B,EAsF+B;WACnC,KAAKnP,UAAL,WACFxV,QAAM,CAACwpB,IAAD,CADJ,cACcxpB,QAAM,CAACypB,IAAD,CADpB,cAC8BzpB,QAAM,CAAC0pB,IAAD,CADpC,cAC8C1pB,QAAM,CAAC2pB,IAAD,CADpD,cAC8D3pB,QAAM,CACvE4I,CADuE,CADpE,cAGA5I,QAAM,CAAC2kB,CAAD,CAHN,QAAP;GAvFW;EA8FbJ,gBA9Fa,4BA8FIqF,GA9FJ,EA8FSC,GA9FT,EA8FcjhB,CA9Fd,EA8FiB+b,CA9FjB,EA8FoB;WACxB,KAAKnP,UAAL,WACFxV,QAAM,CAAC4pB,GAAD,CADJ,cACa5pB,QAAM,CAAC6pB,GAAD,CADnB,cAC4B7pB,QAAM,CAAC4I,CAAD,CADlC,cACyC5I,QAAM,CAAC2kB,CAAD,CAD/C,QAAP;GA/FW;EAoGbmF,IApGa,gBAoGRlhB,CApGQ,EAoGL+b,CApGK,EAoGFwD,CApGE,EAoGCnF,CApGD,EAoGI;WACR,KAAKxN,UAAL,WACFxV,QAAM,CAAC4I,CAAD,CADJ,cACW5I,QAAM,CAAC2kB,CAAD,CADjB,cACwB3kB,QAAM,CAACmoB,CAAD,CAD9B,cACqCnoB,QAAM,CAACgjB,CAAD,CAD3C,SAAP;GArGW;EA0Gb+G,WA1Ga,uBA0GDnhB,CA1GC,EA0GE+b,CA1GF,EA0GKwD,CA1GL,EA0GQnF,CA1GR,EA0GWtX,CA1GX,EA0Gc;QACrBA,CAAC,IAAI,IAAT,EAAe;MACbA,CAAC,GAAG,CAAJ;;;IAEFA,CAAC,GAAGxL,IAAI,CAAC2O,GAAL,CAASnD,CAAT,EAAY,MAAMyc,CAAlB,EAAqB,MAAMnF,CAA3B,CAAJ,CAJyB;;QAOnB9jB,CAAC,GAAGwM,CAAC,IAAI,MAAMkc,KAAV,CAAX;SAEKvD,MAAL,CAAYzb,CAAC,GAAG8C,CAAhB,EAAmBiZ,CAAnB;SACKF,MAAL,CAAY7b,CAAC,GAAGuf,CAAJ,GAAQzc,CAApB,EAAuBiZ,CAAvB;SACKL,aAAL,CAAmB1b,CAAC,GAAGuf,CAAJ,GAAQjpB,CAA3B,EAA8BylB,CAA9B,EAAiC/b,CAAC,GAAGuf,CAArC,EAAwCxD,CAAC,GAAGzlB,CAA5C,EAA+C0J,CAAC,GAAGuf,CAAnD,EAAsDxD,CAAC,GAAGjZ,CAA1D;SACK+Y,MAAL,CAAY7b,CAAC,GAAGuf,CAAhB,EAAmBxD,CAAC,GAAG3B,CAAJ,GAAQtX,CAA3B;SACK4Y,aAAL,CAAmB1b,CAAC,GAAGuf,CAAvB,EAA0BxD,CAAC,GAAG3B,CAAJ,GAAQ9jB,CAAlC,EAAqC0J,CAAC,GAAGuf,CAAJ,GAAQjpB,CAA7C,EAAgDylB,CAAC,GAAG3B,CAApD,EAAuDpa,CAAC,GAAGuf,CAAJ,GAAQzc,CAA/D,EAAkEiZ,CAAC,GAAG3B,CAAtE;SACKyB,MAAL,CAAY7b,CAAC,GAAG8C,CAAhB,EAAmBiZ,CAAC,GAAG3B,CAAvB;SACKsB,aAAL,CAAmB1b,CAAC,GAAG1J,CAAvB,EAA0BylB,CAAC,GAAG3B,CAA9B,EAAiCpa,CAAjC,EAAoC+b,CAAC,GAAG3B,CAAJ,GAAQ9jB,CAA5C,EAA+C0J,CAA/C,EAAkD+b,CAAC,GAAG3B,CAAJ,GAAQtX,CAA1D;SACK+Y,MAAL,CAAY7b,CAAZ,EAAe+b,CAAC,GAAGjZ,CAAnB;SACK4Y,aAAL,CAAmB1b,CAAnB,EAAsB+b,CAAC,GAAGzlB,CAA1B,EAA6B0J,CAAC,GAAG1J,CAAjC,EAAoCylB,CAApC,EAAuC/b,CAAC,GAAG8C,CAA3C,EAA8CiZ,CAA9C;WACO,KAAKD,SAAL,EAAP;GA5HW;EA+HbsF,OA/Ha,mBA+HLphB,CA/HK,EA+HF+b,CA/HE,EA+HCxO,EA/HD,EA+HKC,EA/HL,EA+HS;;QAEhBA,EAAE,IAAI,IAAV,EAAgB;MACdA,EAAE,GAAGD,EAAL;;;IAEFvN,CAAC,IAAIuN,EAAL;IACAwO,CAAC,IAAIvO,EAAL;QACMsP,EAAE,GAAGvP,EAAE,GAAGyR,KAAhB;QACMjC,EAAE,GAAGvP,EAAE,GAAGwR,KAAhB;QACMqC,EAAE,GAAGrhB,CAAC,GAAGuN,EAAE,GAAG,CAApB;QACM+T,EAAE,GAAGvF,CAAC,GAAGvO,EAAE,GAAG,CAApB;QACM+T,EAAE,GAAGvhB,CAAC,GAAGuN,EAAf;QACMiU,EAAE,GAAGzF,CAAC,GAAGvO,EAAf;SAEKiO,MAAL,CAAYzb,CAAZ,EAAewhB,EAAf;SACK9F,aAAL,CAAmB1b,CAAnB,EAAsBwhB,EAAE,GAAGzE,EAA3B,EAA+BwE,EAAE,GAAGzE,EAApC,EAAwCf,CAAxC,EAA2CwF,EAA3C,EAA+CxF,CAA/C;SACKL,aAAL,CAAmB6F,EAAE,GAAGzE,EAAxB,EAA4Bf,CAA5B,EAA+BsF,EAA/B,EAAmCG,EAAE,GAAGzE,EAAxC,EAA4CsE,EAA5C,EAAgDG,EAAhD;SACK9F,aAAL,CAAmB2F,EAAnB,EAAuBG,EAAE,GAAGzE,EAA5B,EAAgCwE,EAAE,GAAGzE,EAArC,EAAyCwE,EAAzC,EAA6CC,EAA7C,EAAiDD,EAAjD;SACK5F,aAAL,CAAmB6F,EAAE,GAAGzE,EAAxB,EAA4BwE,EAA5B,EAAgCthB,CAAhC,EAAmCwhB,EAAE,GAAGzE,EAAxC,EAA4C/c,CAA5C,EAA+CwhB,EAA/C;WACO,KAAK1F,SAAL,EAAP;GAlJW;EAqJb2F,MArJa,kBAqJNzhB,CArJM,EAqJH+b,CArJG,EAqJA2F,MArJA,EAqJQ;WACZ,KAAKN,OAAL,CAAaphB,CAAb,EAAgB+b,CAAhB,EAAmB2F,MAAnB,CAAP;GAtJW;EAyJbC,GAzJa,eAyJT3hB,CAzJS,EAyJN+b,CAzJM,EAyJH2F,MAzJG,EAyJKE,UAzJL,EAyJiBC,QAzJjB,EAyJ2BC,aAzJ3B,EAyJ0C;QACjDA,aAAa,IAAI,IAArB,EAA2B;MACzBA,aAAa,GAAG,KAAhB;;;QAEIC,MAAM,GAAG,MAAMzqB,IAAI,CAAC2lB,EAA1B;QACM+E,OAAO,GAAG,MAAM1qB,IAAI,CAAC2lB,EAA3B;QAEIgF,QAAQ,GAAGJ,QAAQ,GAAGD,UAA1B;;QAEItqB,IAAI,CAACgmB,GAAL,CAAS2E,QAAT,IAAqBF,MAAzB,EAAiC;;MAE/BE,QAAQ,GAAGF,MAAX;KAFF,MAGO,IAAIE,QAAQ,KAAK,CAAb,IAAkBH,aAAa,KAAKG,QAAQ,GAAG,CAAnD,EAAsD;;UAErDC,GAAG,GAAGJ,aAAa,GAAG,CAAC,CAAJ,GAAQ,CAAjC;MACAG,QAAQ,GAAGC,GAAG,GAAGH,MAAN,GAAeE,QAA1B;;;QAGIE,OAAO,GAAG7qB,IAAI,CAACgQ,IAAL,CAAUhQ,IAAI,CAACgmB,GAAL,CAAS2E,QAAT,IAAqBD,OAA/B,CAAhB;QACMI,MAAM,GAAGH,QAAQ,GAAGE,OAA1B;QACME,SAAS,GAAID,MAAM,GAAGJ,OAAV,GAAqBhD,KAArB,GAA6B0C,MAA/C;QACIY,MAAM,GAAGV,UAAb,CArBqD;;QAwBjDW,OAAO,GAAG,CAACjrB,IAAI,CAAC6lB,GAAL,CAASmF,MAAT,CAAD,GAAoBD,SAAlC;QACIG,OAAO,GAAGlrB,IAAI,CAAC+lB,GAAL,CAASiF,MAAT,IAAmBD,SAAjC,CAzBqD;;QA4BjDI,EAAE,GAAGziB,CAAC,GAAG1I,IAAI,CAAC+lB,GAAL,CAASiF,MAAT,IAAmBZ,MAAhC;QACIgB,EAAE,GAAG3G,CAAC,GAAGzkB,IAAI,CAAC6lB,GAAL,CAASmF,MAAT,IAAmBZ,MAAhC,CA7BqD;;SAgChDjG,MAAL,CAAYgH,EAAZ,EAAgBC,EAAhB;;SAEK,IAAIC,MAAM,GAAG,CAAlB,EAAqBA,MAAM,GAAGR,OAA9B,EAAuCQ,MAAM,EAA7C,EAAiD;;UAEzC/B,IAAI,GAAG6B,EAAE,GAAGF,OAAlB;UACM1B,IAAI,GAAG6B,EAAE,GAAGF,OAAlB,CAH+C;;MAM/CF,MAAM,IAAIF,MAAV,CAN+C;;MAS/CK,EAAE,GAAGziB,CAAC,GAAG1I,IAAI,CAAC+lB,GAAL,CAASiF,MAAT,IAAmBZ,MAA5B;MACAgB,EAAE,GAAG3G,CAAC,GAAGzkB,IAAI,CAAC6lB,GAAL,CAASmF,MAAT,IAAmBZ,MAA5B,CAV+C;;MAa/Ca,OAAO,GAAG,CAACjrB,IAAI,CAAC6lB,GAAL,CAASmF,MAAT,CAAD,GAAoBD,SAA9B;MACAG,OAAO,GAAGlrB,IAAI,CAAC+lB,GAAL,CAASiF,MAAT,IAAmBD,SAA7B,CAd+C;;UAiBzCvB,IAAI,GAAG2B,EAAE,GAAGF,OAAlB;UACMxB,IAAI,GAAG2B,EAAE,GAAGF,OAAlB,CAlB+C;;WAqB1C9G,aAAL,CAAmBkF,IAAnB,EAAyBC,IAAzB,EAA+BC,IAA/B,EAAqCC,IAArC,EAA2C0B,EAA3C,EAA+CC,EAA/C;;;WAGK,IAAP;GAnNW;EAsNbE,OAtNa,qBAsNM;sCAARC,MAAQ;MAARA,MAAQ;;;SACZpH,MAAL,gCAAgBoH,MAAM,CAACC,KAAP,MAAkB,EAAlC;;+BACkBD,MAAlB,6BAA0B;UAAjBE,KAAK,cAAT;WACElH,MAAL,gCAAgBkH,KAAK,IAAI,EAAzB;;;WAEK,KAAKjH,SAAL,EAAP;GA3NW;EA8Nbf,IA9Na,gBA8NRA,KA9NQ,EA8NF;IACTgE,OAAO,CAACle,KAAR,CAAc,IAAd,EAAoBka,KAApB;WACO,IAAP;GAhOW;EAmObiI,YAnOa,wBAmOAC,IAnOA,EAmOM;QACb,YAAYC,IAAZ,CAAiBD,IAAjB,CAAJ,EAA4B;aACnB,GAAP;;;WAGK,EAAP;GAxOW;EA2ObE,IA3Oa,gBA2ORta,KA3OQ,EA2ODoa,IA3OC,EA2OK;QACZ,0BAA0BC,IAA1B,CAA+Bra,KAA/B,CAAJ,EAA2C;MACzCoa,IAAI,GAAGpa,KAAP;MACAA,KAAK,GAAG,IAAR;;;QAGEA,KAAJ,EAAW;WACJ4G,SAAL,CAAe5G,KAAf;;;WAEK,KAAK+D,UAAL,YAAoB,KAAKoW,YAAL,CAAkBC,IAAlB,CAApB,EAAP;GApPW;EAuPb/W,MAvPa,kBAuPNrD,KAvPM,EAuPC;QACRA,KAAJ,EAAW;WACJgH,WAAL,CAAiBhH,KAAjB;;;WAEK,KAAK+D,UAAL,CAAgB,GAAhB,CAAP;GA3PW;EA8PbwW,aA9Pa,yBA8PC3T,SA9PD,EA8PYI,WA9PZ,EA8PyBoT,IA9PzB,EA8P+B;QACtCpT,WAAW,IAAI,IAAnB,EAAyB;MACvBA,WAAW,GAAGJ,SAAd;;;QAEI4T,UAAU,GAAG,yBAAnB;;QACIA,UAAU,CAACH,IAAX,CAAgBzT,SAAhB,CAAJ,EAAgC;MAC9BwT,IAAI,GAAGxT,SAAP;MACAA,SAAS,GAAG,IAAZ;;;QAGE4T,UAAU,CAACH,IAAX,CAAgBrT,WAAhB,CAAJ,EAAkC;MAChCoT,IAAI,GAAGpT,WAAP;MACAA,WAAW,GAAGJ,SAAd;;;QAGEA,SAAJ,EAAe;WACRA,SAAL,CAAeA,SAAf;WACKI,WAAL,CAAiBA,WAAjB;;;WAGK,KAAKjD,UAAL,YAAoB,KAAKoW,YAAL,CAAkBC,IAAlB,CAApB,EAAP;GAlRW;EAqRbK,IArRa,gBAqRRL,IArRQ,EAqRF;WACF,KAAKrW,UAAL,YAAoB,KAAKoW,YAAL,CAAkBC,IAAlB,CAApB,QAAP;GAtRW;EAyRbta,SAzRa,qBAyRHO,GAzRG,EAyREC,GAzRF,EAyROC,GAzRP,EAyRYC,GAzRZ,EAyRiBC,EAzRjB,EAyRqBC,EAzRrB,EAyRyB;;QAEhCL,GAAG,KAAK,CAAR,IAAaC,GAAG,KAAK,CAArB,IAA0BC,GAAG,KAAK,CAAlC,IAAuCC,GAAG,KAAK,CAA/C,IAAoDC,EAAE,KAAK,CAA3D,IAAgEC,EAAE,KAAK,CAA3E,EAA8E;;aAErE,IAAP;;;QAEIC,CAAC,GAAG,KAAK2C,IAAf;;4BACiC3C,CAPG;QAO7B4C,EAP6B;QAOzBC,EAPyB;QAOrBC,EAPqB;QAOjBC,EAPiB;QAObC,EAPa;QAOTC,EAPS;;IAQpCjD,CAAC,CAAC,CAAD,CAAD,GAAO4C,EAAE,GAAGlD,GAAL,GAAWoD,EAAE,GAAGnD,GAAvB;IACAK,CAAC,CAAC,CAAD,CAAD,GAAO6C,EAAE,GAAGnD,GAAL,GAAWqD,EAAE,GAAGpD,GAAvB;IACAK,CAAC,CAAC,CAAD,CAAD,GAAO4C,EAAE,GAAGhD,GAAL,GAAWkD,EAAE,GAAGjD,GAAvB;IACAG,CAAC,CAAC,CAAD,CAAD,GAAO6C,EAAE,GAAGjD,GAAL,GAAWmD,EAAE,GAAGlD,GAAvB;IACAG,CAAC,CAAC,CAAD,CAAD,GAAO4C,EAAE,GAAG9C,EAAL,GAAUgD,EAAE,GAAG/C,EAAf,GAAoBiD,EAA3B;IACAhD,CAAC,CAAC,CAAD,CAAD,GAAO6C,EAAE,GAAG/C,EAAL,GAAUiD,EAAE,GAAGhD,EAAf,GAAoBkD,EAA3B;QAEM8W,MAAM,GAAG,CAACra,GAAD,EAAMC,GAAN,EAAWC,GAAX,EAAgBC,GAAhB,EAAqBC,EAArB,EAAyBC,EAAzB,EAA6BtS,GAA7B,CAAiC,UAAA4L,CAAC;aAAIzL,QAAM,CAACyL,CAAD,CAAV;KAAlC,EAAiD/N,IAAjD,CAAsD,GAAtD,CAAf;WACO,KAAK8X,UAAL,WAAmB2W,MAAnB,SAAP;GAzSW;EA4SbC,SA5Sa,qBA4SHxjB,CA5SG,EA4SA+b,CA5SA,EA4SG;WACP,KAAKpT,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAxB,EAA2B3I,CAA3B,EAA8B+b,CAA9B,CAAP;GA7SW;EAgTb0H,MAhTa,kBAgTNC,KAhTM,EAgTe;QAAdjwB,OAAc,uEAAJ,EAAI;QACtBsoB,CAAJ;QACM4H,GAAG,GAAID,KAAK,GAAGpsB,IAAI,CAAC2lB,EAAd,GAAoB,GAAhC;QACMI,GAAG,GAAG/lB,IAAI,CAAC+lB,GAAL,CAASsG,GAAT,CAAZ;QACMxG,GAAG,GAAG7lB,IAAI,CAAC6lB,GAAL,CAASwG,GAAT,CAAZ;QACI3jB,CAAC,GAAI+b,CAAC,GAAG,CAAb;;QAEItoB,OAAO,CAACmwB,MAAR,IAAkB,IAAtB,EAA4B;2CACjBnwB,OAAO,CAACmwB,MADS;;MACzB5jB,CADyB;MACtB+b,CADsB;UAEpBjP,EAAE,GAAG9M,CAAC,GAAGqd,GAAJ,GAAUtB,CAAC,GAAGoB,GAAzB;UACMpQ,EAAE,GAAG/M,CAAC,GAAGmd,GAAJ,GAAUpB,CAAC,GAAGsB,GAAzB;MACArd,CAAC,IAAI8M,EAAL;MACAiP,CAAC,IAAIhP,EAAL;;;WAGK,KAAKpE,SAAL,CAAe0U,GAAf,EAAoBF,GAApB,EAAyB,CAACA,GAA1B,EAA+BE,GAA/B,EAAoCrd,CAApC,EAAuC+b,CAAvC,CAAP;GA/TW;EAkUb8H,KAlUa,iBAkUPC,OAlUO,EAkUEC,OAlUF,EAkUyB;QAAdtwB,OAAc,uEAAJ,EAAI;QAChCsoB,CAAJ;;QACIgI,OAAO,IAAI,IAAf,EAAqB;MACnBA,OAAO,GAAGD,OAAV;;;QAEE,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;MAC/BtwB,OAAO,GAAGswB,OAAV;MACAA,OAAO,GAAGD,OAAV;;;QAGE9jB,CAAC,GAAI+b,CAAC,GAAG,CAAb;;QACItoB,OAAO,CAACmwB,MAAR,IAAkB,IAAtB,EAA4B;4CACjBnwB,OAAO,CAACmwB,MADS;;MACzB5jB,CADyB;MACtB+b,CADsB;MAE1B/b,CAAC,IAAI8jB,OAAO,GAAG9jB,CAAf;MACA+b,CAAC,IAAIgI,OAAO,GAAGhI,CAAf;;;WAGK,KAAKpT,SAAL,CAAemb,OAAf,EAAwB,CAAxB,EAA2B,CAA3B,EAA8BC,OAA9B,EAAuC/jB,CAAvC,EAA0C+b,CAA1C,CAAP;;CAnVJ;;ACNA,IAAMiI,YAAY,GAAG;OACd,GADc;QAEb,GAFa;QAGb,GAHa;QAIb,GAJa;QAKb,GALa;QAMb,GANa;QAOb,GAPa;QAQb,GARa;QASb,GATa;QAUb,GAVa;QAWb,GAXa;QAYb,GAZa;QAab,GAba;QAcb,GAda;QAeb,GAfa;QAgBb,GAhBa;QAiBb,GAjBa;OAkBd,GAlBc;QAmBb,GAnBa;OAoBd,GApBc;OAqBd,GArBc;OAsBd,GAtBc;OAuBd,GAvBc;OAwBd,GAxBc;OAyBd,GAzBc;OA0Bd,GA1Bc;OA2Bd;CA3BP;AA8BA,IAAMC,UAAU,GAAG,q2GAwEjBC,KAxEiB,CAwEX,KAxEW,CAAnB;;IA0EMC;;;yBACQC,UAAU;aACb,IAAID,OAAJ,CAAYE,EAAE,CAACC,YAAH,CAAgBF,QAAhB,EAA0B,MAA1B,CAAZ,CAAP;;;;mBAGUG,QAAZ,EAAsB;;;SACfA,QAAL,GAAgBA,QAAhB;SACKC,UAAL,GAAkB,EAAlB;SACKC,WAAL,GAAmB,EAAnB;SACKC,aAAL,GAAqB,EAArB;SACKC,SAAL,GAAiB,EAAjB;SAEK7J,KAAL,GAPoB;;SASf8J,UAAL,GAAkB,IAAI3vB,KAAJ,CAAU,GAAV,CAAlB;;SACK,IAAI4vB,IAAI,GAAG,CAAhB,EAAmBA,IAAI,IAAI,GAA3B,EAAgCA,IAAI,EAApC,EAAwC;WACjCD,UAAL,CAAgBC,IAAhB,IAAwB,KAAKJ,WAAL,CAAiBR,UAAU,CAACY,IAAD,CAA3B,CAAxB;;;SAGGvU,IAAL,GAAY,KAAKkU,UAAL,CAAgB,UAAhB,EAA4BN,KAA5B,CAAkC,KAAlC,EAAyCjtB,GAAzC,CAA6C,UAAAC,CAAC;aAAI,CAACA,CAAL;KAA9C,CAAZ;SACK4tB,QAAL,GAAgB,EAAE,KAAKN,UAAL,CAAgB,UAAhB,KAA+B,CAAjC,CAAhB;SACKO,SAAL,GAAiB,EAAE,KAAKP,UAAL,CAAgB,WAAhB,KAAgC,CAAlC,CAAjB;SACKQ,OAAL,GAAe,EAAE,KAAKR,UAAL,CAAgB,SAAhB,KAA8B,CAAhC,CAAf;SACKS,SAAL,GAAiB,EAAE,KAAKT,UAAL,CAAgB,WAAhB,KAAgC,CAAlC,CAAjB;SACKU,OAAL,GACE,KAAK5U,IAAL,CAAU,CAAV,IAAe,KAAKA,IAAL,CAAU,CAAV,CAAf,IAA+B,KAAKwU,QAAL,GAAgB,KAAKC,SAApD,CADF;;;;;4BAIM;UACFI,OAAO,GAAG,EAAd;;iDACiB,KAAKZ,QAAL,CAAcL,KAAd,CAAoB,IAApB,CAFX;;;;4DAEsC;cAAnCkB,IAAmC;cACtCC,KAAJ;cACInxB,CAAJ;;cACKmxB,KAAK,GAAGD,IAAI,CAACC,KAAL,CAAW,aAAX,CAAb,EAAyC;YACvCF,OAAO,GAAGE,KAAK,CAAC,CAAD,CAAf;;WADF,MAGO,IAAKA,KAAK,GAAGD,IAAI,CAACC,KAAL,CAAW,WAAX,CAAb,EAAuC;YAC5CF,OAAO,GAAG,EAAV;;;;kBAIMA,OAAR;iBACO,aAAL;cACEE,KAAK,GAAGD,IAAI,CAACC,KAAL,CAAW,eAAX,CAAR;kBACIzxB,GAAG,GAAGyxB,KAAK,CAAC,CAAD,CAAf;kBACI/mB,KAAK,GAAG+mB,KAAK,CAAC,CAAD,CAAjB;;kBAEKnxB,CAAC,GAAG,KAAKswB,UAAL,CAAgB5wB,GAAhB,CAAT,EAAgC;oBAC1B,CAACqB,KAAK,CAAC8B,OAAN,CAAc7C,CAAd,CAAL,EAAuB;kBACrBA,CAAC,GAAG,KAAKswB,UAAL,CAAgB5wB,GAAhB,IAAuB,CAACM,CAAD,CAA3B;;;gBAEFA,CAAC,CAACO,IAAF,CAAO6J,KAAP;eAJF,MAKO;qBACAkmB,UAAL,CAAgB5wB,GAAhB,IAAuB0K,KAAvB;;;;;iBAIC,aAAL;kBACM,CAAC,SAAS4kB,IAAT,CAAckC,IAAd,CAAL,EAA0B;;;;kBAGtBpV,IAAI,GAAGoV,IAAI,CAACC,KAAL,CAAW,oBAAX,EAAiC,CAAjC,CAAX;mBACKZ,WAAL,CAAiBzU,IAAjB,IAAyB,CAACoV,IAAI,CAACC,KAAL,CAAW,kBAAX,EAA+B,CAA/B,CAA1B;;;iBAGG,WAAL;cACEA,KAAK,GAAGD,IAAI,CAACC,KAAL,CAAW,sCAAX,CAAR;;kBACIA,KAAJ,EAAW;qBACJV,SAAL,CAAeU,KAAK,CAAC,CAAD,CAAL,GAAW,IAAX,GAAkBA,KAAK,CAAC,CAAD,CAAtC,IAA6ClW,QAAQ,CAACkW,KAAK,CAAC,CAAD,CAAN,CAArD;;;;;;;;;;;;;;+BAOCC,MAAM;UACTC,GAAG,GAAG,EAAZ;;WACK,IAAI/vB,CAAC,GAAG,CAAR,EAAWgwB,GAAG,GAAGF,IAAI,CAAChxB,MAA3B,EAAmCkB,CAAC,GAAGgwB,GAAvC,EAA4ChwB,CAAC,EAA7C,EAAiD;YAC3CqvB,IAAI,GAAGS,IAAI,CAACvvB,UAAL,CAAgBP,CAAhB,CAAX;QACAqvB,IAAI,GAAGb,YAAY,CAACa,IAAD,CAAZ,IAAsBA,IAA7B;QACAU,GAAG,CAAC9wB,IAAJ,CAASowB,IAAI,CAACzuB,QAAL,CAAc,EAAd,CAAT;;;aAGKmvB,GAAP;;;;oCAGc1vB,QAAQ;UAChB4vB,MAAM,GAAG,EAAf;;WAEK,IAAIjwB,CAAC,GAAG,CAAR,EAAWgwB,GAAG,GAAG3vB,MAAM,CAACvB,MAA7B,EAAqCkB,CAAC,GAAGgwB,GAAzC,EAA8ChwB,CAAC,EAA/C,EAAmD;YAC3CkwB,QAAQ,GAAG7vB,MAAM,CAACE,UAAP,CAAkBP,CAAlB,CAAjB;QACAiwB,MAAM,CAAChxB,IAAP,CAAY,KAAKkxB,gBAAL,CAAsBD,QAAtB,CAAZ;;;aAGKD,MAAP;;;;qCAGe1mB,WAAW;aACnBklB,UAAU,CAACD,YAAY,CAACjlB,SAAD,CAAZ,IAA2BA,SAA5B,CAAV,IAAoD,SAA3D;;;;iCAGW6mB,OAAO;aACX,KAAKnB,WAAL,CAAiBmB,KAAjB,KAA2B,CAAlC;;;;gCAGU5sB,MAAME,OAAO;aAChB,KAAKyrB,SAAL,CAAe3rB,IAAI,GAAG,IAAP,GAAcE,KAA7B,KAAuC,CAA9C;;;;sCAGgBusB,QAAQ;UAClBI,QAAQ,GAAG,EAAjB;;WAEK,IAAI7d,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGyd,MAAM,CAACnxB,MAAnC,EAA2C0T,KAAK,EAAhD,EAAoD;YAC5ChP,IAAI,GAAGysB,MAAM,CAACzd,KAAD,CAAnB;YACM9O,KAAK,GAAGusB,MAAM,CAACzd,KAAK,GAAG,CAAT,CAApB;QACA6d,QAAQ,CAACpxB,IAAT,CAAc,KAAKqxB,YAAL,CAAkB9sB,IAAlB,IAA0B,KAAK+sB,WAAL,CAAiB/sB,IAAjB,EAAuBE,KAAvB,CAAxC;;;aAGK2sB,QAAP;;;;;;;IChOEG;qBACU;;;;;;6BAEL;YACD,IAAIzyB,KAAJ,CAAU,mCAAV,CAAN;;;;oCAGc;YACR,IAAIA,KAAJ,CAAU,mCAAV,CAAN;;;;0BAGI;aACG,KAAK0J,UAAL,IAAmB,IAAnB,GACH,KAAKA,UADF,GAEF,KAAKA,UAAL,GAAkB,KAAKxF,QAAL,CAAcqF,GAAd,EAFvB;;;;+BAKS;UACL,KAAK4L,QAAL,IAAiB,KAAKzL,UAAL,IAAmB,IAAxC,EAA8C;;;;WAIzC4N,KAAL;aACQ,KAAKnC,QAAL,GAAgB,IAAxB;;;;4BAGM;YACA,IAAInV,KAAJ,CAAU,mCAAV,CAAN;;;;+BAGS8I,MAAM4pB,YAAY;UACvBA,UAAU,IAAI,IAAlB,EAAwB;QACtBA,UAAU,GAAG,KAAb;;;UAEIC,GAAG,GAAGD,UAAU,GAAG,KAAKf,OAAR,GAAkB,CAAxC;aACQ,CAAC,KAAKJ,QAAL,GAAgBoB,GAAhB,GAAsB,KAAKnB,SAA5B,IAAyC,IAA1C,GAAkD1oB,IAAzD;;;;;;;AC9BJ,IAAM8pB,cAAc,GAAG;EACrBC,OADqB,qBACX;WACD/B,EAAE,CAACC,YAAH,CAAgB+B,SAAS,GAAG,mBAA5B,EAAiD,MAAjD,CAAP;GAFmB;gBAAA,yBAIJ;WACRhC,EAAE,CAACC,YAAH,CAAgB+B,SAAS,GAAG,wBAA5B,EAAsD,MAAtD,CAAP;GALmB;mBAAA,4BAOD;WACXhC,EAAE,CAACC,YAAH,CAAgB+B,SAAS,GAAG,2BAA5B,EAAyD,MAAzD,CAAP;GARmB;uBAAA,gCAUG;WACfhC,EAAE,CAACC,YAAH,CAAgB+B,SAAS,GAAG,+BAA5B,EAA6D,MAA7D,CAAP;GAXmB;EAarBC,SAbqB,uBAaT;WACHjC,EAAE,CAACC,YAAH,CAAgB+B,SAAS,GAAG,qBAA5B,EAAmD,MAAnD,CAAP;GAdmB;kBAAA,2BAgBF;WACVhC,EAAE,CAACC,YAAH,CAAgB+B,SAAS,GAAG,0BAA5B,EAAwD,MAAxD,CAAP;GAjBmB;qBAAA,8BAmBC;WACbhC,EAAE,CAACC,YAAH,CAAgB+B,SAAS,GAAG,6BAA5B,EAA2D,MAA3D,CAAP;GApBmB;yBAAA,kCAsBK;WACjBhC,EAAE,CAACC,YAAH,CACL+B,SAAS,GAAG,iCADP,EAEL,MAFK,CAAP;GAvBmB;eAAA,wBA4BL;WACPhC,EAAE,CAACC,YAAH,CAAgB+B,SAAS,GAAG,uBAA5B,EAAqD,MAArD,CAAP;GA7BmB;cAAA,uBA+BN;WACNhC,EAAE,CAACC,YAAH,CAAgB+B,SAAS,GAAG,sBAA5B,EAAoD,MAApD,CAAP;GAhCmB;gBAAA,yBAkCJ;WACRhC,EAAE,CAACC,YAAH,CAAgB+B,SAAS,GAAG,wBAA5B,EAAsD,MAAtD,CAAP;GAnCmB;oBAAA,6BAqCA;WACZhC,EAAE,CAACC,YAAH,CAAgB+B,SAAS,GAAG,4BAA5B,EAA0D,MAA1D,CAAP;GAtCmB;EAwCrBE,MAxCqB,oBAwCZ;WACAlC,EAAE,CAACC,YAAH,CAAgB+B,SAAS,GAAG,kBAA5B,EAAgD,MAAhD,CAAP;GAzCmB;EA2CrBG,YA3CqB,0BA2CN;WACNnC,EAAE,CAACC,YAAH,CAAgB+B,SAAS,GAAG,wBAA5B,EAAsD,MAAtD,CAAP;;CA5CJ;;IAgDMI;;;;;wBACQhvB,QAAZ,EAAsBuY,IAAtB,EAA4BtY,EAA5B,EAAgC;;;;;;UAEzBD,QAAL,GAAgBA,QAAhB;UACKuY,IAAL,GAAYA,IAAZ;UACKtY,EAAL,GAAUA,EAAV;UACKgvB,IAAL,GAAY,IAAIvC,OAAJ,CAAYgC,cAAc,CAAC,MAAKnW,IAAN,CAAd,EAAZ,CAAZ;qBAQI,MAAK0W,IAbqB;UAOb5B,QAPa,cAO5BA,QAP4B;UAQZC,SARY,cAQ5BA,SAR4B;UASjBzU,IATiB,cAS5BA,IAT4B;UAUd4U,OAVc,cAU5BA,OAV4B;UAWdF,OAXc,cAW5BA,OAX4B;UAYZC,SAZY,cAY5BA,SAZ4B;;;;;;4BAgBxB;WACDhoB,UAAL,CAAgBtF,IAAhB,GAAuB;QACrBuF,IAAI,EAAE,MADe;QAErBypB,QAAQ,EAAE,KAAK3W,IAFM;QAGrB/E,OAAO,EAAE,OAHY;QAIrB2b,QAAQ,EAAE;OAJZ;aAOO,KAAK3pB,UAAL,CAAgBxH,GAAhB,EAAP;;;;2BAGK6vB,MAAM;UACLuB,OAAO,GAAG,KAAKH,IAAL,CAAUI,UAAV,CAAqBxB,IAArB,CAAhB;UACMG,MAAM,GAAG,KAAKiB,IAAL,CAAUK,eAAV,WAA6BzB,IAA7B,EAAf;UACMO,QAAQ,GAAG,KAAKa,IAAL,CAAUM,iBAAV,CAA4BvB,MAA5B,CAAjB;UACMwB,SAAS,GAAG,EAAlB;;WACK,IAAIzxB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiwB,MAAM,CAACnxB,MAA3B,EAAmCkB,CAAC,EAApC,EAAwC;YAChCowB,KAAK,GAAGH,MAAM,CAACjwB,CAAD,CAApB;QACAyxB,SAAS,CAACxyB,IAAV,CAAe;UACbyyB,QAAQ,EAAErB,QAAQ,CAACrwB,CAAD,CADL;UAEb2xB,QAAQ,EAAE,CAFG;UAGbC,OAAO,EAAE,CAHI;UAIbC,OAAO,EAAE,CAJI;UAKbC,YAAY,EAAE,KAAKZ,IAAL,CAAUZ,YAAV,CAAuBF,KAAvB;SALhB;;;aASK,CAACiB,OAAD,EAAUI,SAAV,CAAP;;;;kCAGYpxB,QAAQwG,MAAM;UACpBopB,MAAM,GAAG,KAAKiB,IAAL,CAAUK,eAAV,WAA6BlxB,MAA7B,EAAf;UACMgwB,QAAQ,GAAG,KAAKa,IAAL,CAAUM,iBAAV,CAA4BvB,MAA5B,CAAjB;UAEI9oB,KAAK,GAAG,CAAZ;;iDACoBkpB,QALM;;;;4DAKI;cAArB0B,OAAqB;UAC5B5qB,KAAK,IAAI4qB,OAAT;;;;;;;;UAGI1D,KAAK,GAAGxnB,IAAI,GAAG,IAArB;aACOM,KAAK,GAAGknB,KAAf;;;;mCAGoB7T,MAAM;aACnBA,IAAI,IAAImW,cAAf;;;;;EA7DuBH;;ACnD3B,IAAMwB,KAAK,GAAG,SAARA,KAAQ,CAASC,GAAT,EAAc;SACnB,cAAOA,GAAG,CAACrxB,QAAJ,CAAa,EAAb,CAAP,EAA0BlB,KAA1B,CAAgC,CAAC,CAAjC,CAAP;CADF;;IAIMwyB;;;;;wBACQjwB,QAAZ,EAAsBivB,IAAtB,EAA4BhvB,EAA5B,EAAgC;;;;;;UAEzBD,QAAL,GAAgBA,QAAhB;UACKivB,IAAL,GAAYA,IAAZ;UACKhvB,EAAL,GAAUA,EAAV;UACKiwB,MAAL,GAAc,MAAKjB,IAAL,CAAUkB,YAAV,EAAd;UACKC,OAAL,GAAe,CAAC,CAAC,CAAD,CAAD,CAAf;UACKC,MAAL,GAAc,CAAC,MAAKpB,IAAL,CAAUqB,QAAV,CAAmB,CAAnB,EAAsBT,YAAvB,CAAd;UAEKtX,IAAL,GAAY,MAAK0W,IAAL,CAAUsB,cAAtB;UACKnE,KAAL,GAAa,OAAO,MAAK6C,IAAL,CAAUuB,UAA9B;UACKnD,QAAL,GAAgB,MAAK4B,IAAL,CAAUwB,MAAV,GAAmB,MAAKrE,KAAxC;UACKkB,SAAL,GAAiB,MAAK2B,IAAL,CAAUyB,OAAV,GAAoB,MAAKtE,KAA1C;UACKmB,OAAL,GAAe,MAAK0B,IAAL,CAAU1B,OAAV,GAAoB,MAAKnB,KAAxC;UACKoB,SAAL,GAAiB,MAAKyB,IAAL,CAAUzB,SAAV,GAAsB,MAAKpB,KAA5C;UACKqB,OAAL,GAAe,MAAKwB,IAAL,CAAUxB,OAAV,GAAoB,MAAKrB,KAAxC;UACKvT,IAAL,GAAY,MAAKoW,IAAL,CAAUpW,IAAtB;;QAEI7Y,QAAQ,CAAChE,OAAT,CAAiB20B,eAAjB,KAAqC,KAAzC,EAAgD;YACzCC,WAAL,GAAmBt0B,MAAM,CAACiR,MAAP,CAAc,IAAd,CAAnB;;;;;;;;8BAIMsgB,MAAMgD,UAAU;UAClBC,GAAG,GAAG,KAAK7B,IAAL,CAAUpqB,MAAV,CAAiBgpB,IAAjB,EAAuBgD,QAAvB,CAAZ,CADwB;;WAInB,IAAI9yB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG+yB,GAAG,CAACtB,SAAJ,CAAc3yB,MAAlC,EAA0CkB,CAAC,EAA3C,EAA+C;YACvCgzB,QAAQ,GAAGD,GAAG,CAACtB,SAAJ,CAAczxB,CAAd,CAAjB;;aACK,IAAI5B,GAAT,IAAgB40B,QAAhB,EAA0B;UACxBA,QAAQ,CAAC50B,GAAD,CAAR,IAAiB,KAAKiwB,KAAtB;;;QAGF2E,QAAQ,CAAClB,YAAT,GAAwBiB,GAAG,CAAC9C,MAAJ,CAAWjwB,CAAX,EAAc8xB,YAAd,GAA6B,KAAKzD,KAA1D;;;aAGK0E,GAAP;;;;iCAGWjD,MAAM;UACb,CAAC,KAAK+C,WAAV,EAAuB;eACd,KAAKI,SAAL,CAAenD,IAAf,CAAP;;;UAEEoD,MAAJ;;UACKA,MAAM,GAAG,KAAKL,WAAL,CAAiB/C,IAAjB,CAAd,EAAuC;eAC9BoD,MAAP;;;UAGIH,GAAG,GAAG,KAAKE,SAAL,CAAenD,IAAf,CAAZ;WACK+C,WAAL,CAAiB/C,IAAjB,IAAyBiD,GAAzB;aACOA,GAAP;;;;2BAGKjD,MAAMgD,UAAUK,WAAW;;UAE5BL,QAAJ,EAAc;eACL,KAAKG,SAAL,CAAenD,IAAf,EAAqBgD,QAArB,CAAP;;;UAGE7C,MAAM,GAAGkD,SAAS,GAAG,IAAH,GAAU,EAAhC;UACI1B,SAAS,GAAG0B,SAAS,GAAG,IAAH,GAAU,EAAnC;UACIrB,YAAY,GAAG,CAAnB,CARgC;;;UAY5B9yB,IAAI,GAAG,CAAX;UACIwT,KAAK,GAAG,CAAZ;;aACOA,KAAK,IAAIsd,IAAI,CAAChxB,MAArB,EAA6B;YACvBs0B,MAAJ;;YAEG5gB,KAAK,KAAKsd,IAAI,CAAChxB,MAAf,IAAyBE,IAAI,GAAGwT,KAAjC,KACE4gB,MAAM,GAAGtD,IAAI,CAACrW,MAAL,CAAYjH,KAAZ,CAAV,EAA+B,CAAC,GAAD,EAAM,IAAN,EAAYsT,QAAZ,CAAqBsN,MAArB,CADhC,CADF,EAGE;cACML,GAAG,GAAG,KAAKM,YAAL,CAAkBvD,IAAI,CAACpwB,KAAL,CAAWV,IAAX,EAAiB,EAAEwT,KAAnB,CAAlB,CAAZ;;cACI,CAAC2gB,SAAL,EAAgB;YACdlD,MAAM,GAAGA,MAAM,CAAChtB,MAAP,CAAc8vB,GAAG,CAAC9C,MAAlB,CAAT;YACAwB,SAAS,GAAGA,SAAS,CAACxuB,MAAV,CAAiB8vB,GAAG,CAACtB,SAArB,CAAZ;;;UAGFK,YAAY,IAAIiB,GAAG,CAACjB,YAApB;UACA9yB,IAAI,GAAGwT,KAAP;SAXF,MAYO;UACLA,KAAK;;;;aAIF;QAAEyd,MAAM,EAANA,MAAF;QAAUwB,SAAS,EAATA,SAAV;QAAqBK,YAAY,EAAZA;OAA5B;;;;2BAGKhC,MAAMgD,UAAU;yBACS,KAAKhsB,MAAL,CAAYgpB,IAAZ,EAAkBgD,QAAlB,CADT;UACb7C,MADa,gBACbA,MADa;UACLwB,SADK,gBACLA,SADK;;UAGf1B,GAAG,GAAG,EAAZ;;WACK,IAAI/vB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiwB,MAAM,CAACnxB,MAA3B,EAAmCkB,CAAC,EAApC,EAAwC;YAChCowB,KAAK,GAAGH,MAAM,CAACjwB,CAAD,CAApB;YACMszB,GAAG,GAAG,KAAKnB,MAAL,CAAYoB,YAAZ,CAAyBnD,KAAK,CAACluB,EAA/B,CAAZ;QACA6tB,GAAG,CAAC9wB,IAAJ,CAAS,cAAOq0B,GAAG,CAAC1yB,QAAJ,CAAa,EAAb,CAAP,EAA0BlB,KAA1B,CAAgC,CAAC,CAAjC,CAAT;;YAEI,KAAK4yB,MAAL,CAAYgB,GAAZ,KAAoB,IAAxB,EAA8B;eACvBhB,MAAL,CAAYgB,GAAZ,IAAmBlD,KAAK,CAAC0B,YAAN,GAAqB,KAAKzD,KAA7C;;;YAEE,KAAKgE,OAAL,CAAaiB,GAAb,KAAqB,IAAzB,EAA+B;eACxBjB,OAAL,CAAaiB,GAAb,IAAoBlD,KAAK,CAACoD,UAA1B;;;;aAIG,CAACzD,GAAD,EAAM0B,SAAN,CAAP;;;;kCAGYpxB,QAAQwG,MAAMisB,UAAU;UAC9B3rB,KAAK,GAAG,KAAKL,MAAL,CAAYzG,MAAZ,EAAoByyB,QAApB,EAA8B,IAA9B,EAAoChB,YAAlD;UACMzD,KAAK,GAAGxnB,IAAI,GAAG,IAArB;aACOM,KAAK,GAAGknB,KAAf;;;;4BAGM;;;UACAoF,KAAK,GAAG,KAAKtB,MAAL,CAAYuB,GAAZ,IAAmB,IAAjC;UACMC,QAAQ,GAAG,KAAK1xB,QAAL,CAAcqF,GAAd,EAAjB;;UAEImsB,KAAJ,EAAW;QACTE,QAAQ,CAACxxB,IAAT,CAAcsT,OAAd,GAAwB,eAAxB;;;WAGG0c,MAAL,CACGyB,YADH,GAEGC,EAFH,CAEM,MAFN,EAEc,UAAA1xB,IAAI;eAAIwxB,QAAQ,CAAChxB,KAAT,CAAeR,IAAf,CAAJ;OAFlB,EAGG0xB,EAHH,CAGM,KAHN,EAGa;eAAMF,QAAQ,CAAC1zB,GAAT,EAAN;OAHb;UAKM6zB,WAAW,GACf,CAAC,CAAC,KAAK5C,IAAL,CAAU,MAAV,KAAqB,IAArB,GACE,KAAKA,IAAL,CAAU,MAAV,EAAkB6C,YADpB,GAEEC,SAFH,KAEiB,CAFlB,KAEwB,CAH1B;UAIIC,KAAK,GAAG,CAAZ;;UACI,KAAK/C,IAAL,CAAUgD,IAAV,CAAeC,YAAnB,EAAiC;QAC/BF,KAAK,IAAI,KAAK,CAAd;;;UAEE,KAAKH,WAAL,IAAoBA,WAAW,IAAI,CAAvC,EAA0C;QACxCG,KAAK,IAAI,KAAK,CAAd;;;MAEFA,KAAK,IAAI,KAAK,CAAd,CAxBM;;UAyBFH,WAAW,KAAK,EAApB,EAAwB;QACtBG,KAAK,IAAI,KAAK,CAAd;;;UAEE,KAAK/C,IAAL,CAAUkD,IAAV,CAAeC,QAAf,CAAwBC,MAA5B,EAAoC;QAClCL,KAAK,IAAI,KAAK,CAAd;OA7BI;;;UAiCAM,GAAG,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EACT9yB,GADS,CACL,UAAAzB,CAAC;eAAII,MAAM,CAACo0B,YAAP,CAAoB,CAAC,MAAI,CAACtyB,EAAL,CAAQ3B,UAAR,CAAmBP,CAAnB,KAAyB,EAA1B,IAAgC,EAApD,CAAJ;OADI,EAETV,IAFS,CAEJ,EAFI,CAAZ;UAGMkb,IAAI,GAAG+Z,GAAG,GAAG,GAAN,GAAY,KAAKrD,IAAL,CAAUsB,cAAnC;UAEQ1X,IAtCF,GAsCW,KAAKoW,IAtChB,CAsCEpW,IAtCF;UAuCA2Z,UAAU,GAAG,KAAKxyB,QAAL,CAAcqF,GAAd,CAAkB;QACnCI,IAAI,EAAE,gBAD6B;QAEnCgtB,QAAQ,EAAEla,IAFyB;QAGnCma,KAAK,EAAEV,KAH4B;QAInCW,QAAQ,EAAE,CACR9Z,IAAI,CAAC+Z,IAAL,GAAY,KAAKxG,KADT,EAERvT,IAAI,CAACga,IAAL,GAAY,KAAKzG,KAFT,EAGRvT,IAAI,CAACia,IAAL,GAAY,KAAK1G,KAHT,EAIRvT,IAAI,CAACka,IAAL,GAAY,KAAK3G,KAJT,CAJyB;QAUnC4G,WAAW,EAAE,KAAK/D,IAAL,CAAUgE,WAVY;QAWnCC,MAAM,EAAE,KAAK7F,QAXsB;QAYnC8F,OAAO,EAAE,KAAK7F,SAZqB;QAanC8F,SAAS,EAAE,CAAC,KAAKnE,IAAL,CAAUzB,SAAV,IAAuB,KAAKyB,IAAL,CAAUwB,MAAlC,IAA4C,KAAKrE,KAbzB;QAcnCiH,OAAO,EAAE,CAAC,KAAKpE,IAAL,CAAU1B,OAAV,IAAqB,CAAtB,IAA2B,KAAKnB,KAdN;QAenCkH,KAAK,EAAE;OAfU,CAAnB,CAvCM;;UAyDF9B,KAAJ,EAAW;QACTgB,UAAU,CAACtyB,IAAX,CAAgBqzB,SAAhB,GAA4B7B,QAA5B;OADF,MAEO;QACLc,UAAU,CAACtyB,IAAX,CAAgBszB,SAAhB,GAA4B9B,QAA5B;;;UAGE,KAAK1xB,QAAL,CAAckwB,MAAlB,EAA0B;YAClBuD,MAAM,GAAGj1B,MAAM,CAACC,IAAP,CAAY,YAAZ,EAA0B,KAA1B,CAAf;YACMi1B,SAAS,GAAG,KAAK1zB,QAAL,CAAcqF,GAAd,EAAlB;QACAquB,SAAS,CAAChzB,KAAV,CAAgB+yB,MAAhB;QACAC,SAAS,CAAC11B,GAAV;QAEAw0B,UAAU,CAACtyB,IAAX,CAAgBuzB,MAAhB,GAAyBC,SAAzB;;;MAGFlB,UAAU,CAACx0B,GAAX;UAEM21B,kBAAkB,GAAG;QACzBluB,IAAI,EAAE,MADmB;QAEzB+N,OAAO,EAAE,cAFgB;QAGzB0b,QAAQ,EAAE3W,IAHe;QAIzBqb,aAAa,EAAE;UACbC,QAAQ,EAAE,IAAI11B,MAAJ,CAAW,OAAX,CADG;UAEb21B,QAAQ,EAAE,IAAI31B,MAAJ,CAAW,UAAX,CAFG;UAGb41B,UAAU,EAAE;SAPW;QASzBC,cAAc,EAAExB,UATS;QAUzByB,CAAC,EAAE,CAAC,CAAD,EAAI,KAAK5D,MAAT;OAVL;;UAaI,CAACmB,KAAL,EAAY;QACVmC,kBAAkB,CAACngB,OAAnB,GAA6B,cAA7B;QACAmgB,kBAAkB,CAACO,WAAnB,GAAiC,UAAjC;;;UAGIC,cAAc,GAAG,KAAKn0B,QAAL,CAAcqF,GAAd,CAAkBsuB,kBAAlB,CAAvB;MAEAQ,cAAc,CAACn2B,GAAf;WAEKwH,UAAL,CAAgBtF,IAAhB,GAAuB;QACrBuF,IAAI,EAAE,MADe;QAErB+N,OAAO,EAAE,OAFY;QAGrB0b,QAAQ,EAAE3W,IAHW;QAIrB4W,QAAQ,EAAE,YAJW;QAKrBiF,eAAe,EAAE,CAACD,cAAD,CALI;QAMrBE,SAAS,EAAE,KAAKC,aAAL;OANb;aASO,KAAK9uB,UAAL,CAAgBxH,GAAhB,EAAP;;;;;;;oCAMc;UACRu2B,IAAI,GAAG,KAAKv0B,QAAL,CAAcqF,GAAd,EAAb;UAEMmvB,OAAO,GAAG,EAAhB;;iDACuB,KAAKpE,OAJd;;;;4DAIuB;cAA5BmB,UAA4B;cAC7BnC,OAAO,GAAG,EAAhB,CADmC;;sDAIjBmC,UAJiB;;;;mEAIL;kBAArB1qB,KAAqB;;kBACxBA,KAAK,GAAG,MAAZ,EAAoB;gBAClBA,KAAK,IAAI,OAAT;gBACAuoB,OAAO,CAACpyB,IAAR,CAAa+yB,KAAK,CAAGlpB,KAAK,KAAK,EAAX,GAAiB,KAAlB,GAA2B,MAA5B,CAAlB;gBACAA,KAAK,GAAG,SAAUA,KAAK,GAAG,KAA1B;;;cAGFuoB,OAAO,CAACpyB,IAAR,CAAa+yB,KAAK,CAAClpB,KAAD,CAAlB;;;;;;;;UAGF2tB,OAAO,CAACx3B,IAAR,YAAiBoyB,OAAO,CAAC/xB,IAAR,CAAa,GAAb,CAAjB;;;;;;;;UAGIo3B,SAAS,GAAG,GAAlB;UACMC,MAAM,GAAG70B,IAAI,CAACgQ,IAAL,CAAU2kB,OAAO,CAAC33B,MAAR,GAAiB43B,SAA3B,CAAf;UACME,MAAM,GAAG,EAAf;;WACK,IAAI52B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG22B,MAApB,EAA4B32B,CAAC,EAA7B,EAAiC;YACzB62B,KAAK,GAAG72B,CAAC,GAAG02B,SAAlB;YACMz2B,GAAG,GAAG6B,IAAI,CAAC2O,GAAL,CAAS,CAACzQ,CAAC,GAAG,CAAL,IAAU02B,SAAnB,EAA8BD,OAAO,CAAC33B,MAAtC,CAAZ;QACA83B,MAAM,CAAC33B,IAAP,YAAgB+yB,KAAK,CAAC6E,KAAD,CAArB,gBAAkC7E,KAAK,CAAC/xB,GAAG,GAAG,CAAP,CAAvC,gBAAsDw2B,OAAO,CAAC/2B,KAAR,CAAcm3B,KAAd,EAAqB52B,GAArB,EAA0BX,IAA1B,CAA+B,GAA/B,CAAtD;;;MAGFk3B,IAAI,CAACv2B,GAAL,2RAeF22B,MAAM,CAACt3B,IAAP,CAAY,IAAZ,CAfE;aAuBOk3B,IAAP;;;;;EAxRuBhG;;ICDrBsG;;;;;;;yBACQ70B,UAAU80B,KAAKC,QAAQ90B,IAAI;UACjCgvB,IAAJ;;UACI,OAAO6F,GAAP,KAAe,QAAnB,EAA6B;YACvB9F,YAAY,CAACgG,cAAb,CAA4BF,GAA5B,CAAJ,EAAsC;iBAC7B,IAAI9F,YAAJ,CAAiBhvB,QAAjB,EAA2B80B,GAA3B,EAAgC70B,EAAhC,CAAP;;;QAGF60B,GAAG,GAAGlI,EAAE,CAACC,YAAH,CAAgBiI,GAAhB,CAAN;;;UAEEt2B,MAAM,CAACM,QAAP,CAAgBg2B,GAAhB,CAAJ,EAA0B;QACxB7F,IAAI,GAAGgG,OAAO,CAAC1nB,MAAR,CAAeunB,GAAf,EAAoBC,MAApB,CAAP;OADF,MAEO,IAAID,GAAG,YAAYI,UAAnB,EAA+B;QACpCjG,IAAI,GAAGgG,OAAO,CAAC1nB,MAAR,CAAe/O,MAAM,CAACC,IAAP,CAAYq2B,GAAZ,CAAf,EAAiCC,MAAjC,CAAP;OADK,MAEA,IAAID,GAAG,YAAYK,WAAnB,EAAgC;QACrClG,IAAI,GAAGgG,OAAO,CAAC1nB,MAAR,CAAe/O,MAAM,CAACC,IAAP,CAAY,IAAIy2B,UAAJ,CAAeJ,GAAf,CAAZ,CAAf,EAAiDC,MAAjD,CAAP;;;UAGE9F,IAAI,IAAI,IAAZ,EAAkB;cACV,IAAInzB,KAAJ,CAAU,mDAAV,CAAN;;;aAGK,IAAIm0B,YAAJ,CAAiBjwB,QAAjB,EAA2BivB,IAA3B,EAAiChvB,EAAjC,CAAP;;;;;;;ACzBJ,iBAAe;EACbm1B,SADa,uBACwB;QAA3BC,WAA2B,uEAAb,WAAa;;SAE9BC,aAAL,GAAqB,EAArB;SACKC,UAAL,GAAkB,CAAlB,CAHmC;;SAM9BC,SAAL,GAAiB,EAAjB;SACKC,KAAL,GAAa,IAAb;SAEKC,gBAAL,GAAwB,EAAxB,CATmC;;QAY/BL,WAAJ,EAAiB;WACVpG,IAAL,CAAUoG,WAAV;;GAdS;EAkBbpG,IAlBa,gBAkBR6F,GAlBQ,EAkBHC,MAlBG,EAkBKnwB,IAlBL,EAkBW;QAClB+wB,QAAJ,EAAc1G,IAAd;;QACI,OAAO8F,MAAP,KAAkB,QAAtB,EAAgC;MAC9BnwB,IAAI,GAAGmwB,MAAP;MACAA,MAAM,GAAG,IAAT;KAJoB;;;QAQlB,OAAOD,GAAP,KAAe,QAAf,IAA2B,KAAKY,gBAAL,CAAsBZ,GAAtB,CAA/B,EAA2D;MACzDa,QAAQ,GAAGb,GAAX;kCACmB,KAAKY,gBAAL,CAAsBZ,GAAtB,CAFsC;MAEtDA,GAFsD,yBAEtDA,GAFsD;MAEjDC,MAFiD,yBAEjDA,MAFiD;KAA3D,MAGO;MACLY,QAAQ,GAAGZ,MAAM,IAAID,GAArB;;UACI,OAAOa,QAAP,KAAoB,QAAxB,EAAkC;QAChCA,QAAQ,GAAG,IAAX;;;;QAIA/wB,IAAI,IAAI,IAAZ,EAAkB;WACXgxB,QAAL,CAAchxB,IAAd;KAnBoB;;;QAuBjBqqB,IAAI,GAAG,KAAKqG,aAAL,CAAmBK,QAAnB,CAAZ,EAA2C;WACpCF,KAAL,GAAaxG,IAAb;aACO,IAAP;KAzBoB;;;QA6BhBhvB,EAAE,cAAO,EAAE,KAAKs1B,UAAd,CAAR;SACKE,KAAL,GAAaZ,cAAc,CAACgB,IAAf,CAAoB,IAApB,EAA0Bf,GAA1B,EAA+BC,MAA/B,EAAuC90B,EAAvC,CAAb,CA9BsB;;;QAkCjBgvB,IAAI,GAAG,KAAKqG,aAAL,CAAmB,KAAKG,KAAL,CAAWld,IAA9B,CAAZ,EAAkD;WAC3Ckd,KAAL,GAAaxG,IAAb;aACO,IAAP;KApCoB;;;QAwClB0G,QAAJ,EAAc;WACPL,aAAL,CAAmBK,QAAnB,IAA+B,KAAKF,KAApC;;;QAGE,KAAKA,KAAL,CAAWld,IAAf,EAAqB;WACd+c,aAAL,CAAmB,KAAKG,KAAL,CAAWld,IAA9B,IAAsC,KAAKkd,KAA3C;;;WAGK,IAAP;GAlEW;EAqEbG,QArEa,oBAqEJJ,SArEI,EAqEO;SACbA,SAAL,GAAiBA,SAAjB;WACO,IAAP;GAvEW;EA0EbM,iBA1Ea,6BA0EKtH,UA1EL,EA0EiB;QACxBA,UAAU,IAAI,IAAlB,EAAwB;MACtBA,UAAU,GAAG,KAAb;;;WAEK,KAAKiH,KAAL,CAAWM,UAAX,CAAsB,KAAKP,SAA3B,EAAsChH,UAAtC,CAAP;GA9EW;EAiFbwH,YAjFa,wBAiFAzd,IAjFA,EAiFMuc,GAjFN,EAiFWC,MAjFX,EAiFmB;SACzBW,gBAAL,CAAsBnd,IAAtB,IAA8B;MAC5Buc,GAAG,EAAHA,GAD4B;MAE5BC,MAAM,EAANA;KAFF;WAKO,IAAP;;CAvFJ;;ACCA,IAAMkB,WAAW,GAAG,MAApB;AACA,IAAMC,MAAM,GAAG,GAAf;;IAEMC;;;;;uBACQn2B,QAAZ,EAAsBhE,OAAtB,EAA+B;;;;;;UAExBgE,QAAL,GAAgBA,QAAhB;UACKo2B,MAAL,GAAcp6B,OAAO,CAACo6B,MAAR,IAAkB,CAAhC;UACKC,gBAAL,GAAwBr6B,OAAO,CAACq6B,gBAAR,IAA4B,CAApD;UACKC,WAAL,GAAmBt6B,OAAO,CAACs6B,WAAR,KAAwB,CAA3C;UACKC,OAAL,GAAev6B,OAAO,CAACu6B,OAAR,IAAmB,CAAlC;UACKC,SAAL,GAAiBx6B,OAAO,CAACw6B,SAAR,IAAqB,IAArB,GAA4Bx6B,OAAO,CAACw6B,SAApC,GAAgD,EAAjE,CAP6B;;UAQxB3O,SAAL,GACE,CAAC7rB,OAAO,CAACkJ,KAAR,GAAgB,MAAKsxB,SAAL,IAAkB,MAAKD,OAAL,GAAe,CAAjC,CAAjB,IAAwD,MAAKA,OAD/D;UAEKE,SAAL,GAAiB,MAAK5O,SAAtB;UACK6O,MAAL,GAAc,MAAK12B,QAAL,CAAcuI,CAA5B;UACKouB,MAAL,GAAc,MAAK32B,QAAL,CAAcskB,CAA5B;UACKsS,MAAL,GAAc,CAAd;UACKC,QAAL,GAAgB76B,OAAO,CAAC66B,QAAxB;UACKC,UAAL,GAAkB,CAAlB;UACKjG,QAAL,GAAgB70B,OAAO,CAAC60B,QAAxB,CAhB6B;;QAmBzB70B,OAAO,CAACmJ,MAAR,IAAkB,IAAtB,EAA4B;YACrBA,MAAL,GAAcnJ,OAAO,CAACmJ,MAAtB;YACK4tB,IAAL,GAAY,MAAK4D,MAAL,GAAc36B,OAAO,CAACmJ,MAAlC;KAFF,MAGO;YACA4tB,IAAL,GAAY,MAAK/yB,QAAL,CAAcsT,IAAd,CAAmByf,IAAnB,EAAZ;KAvB2B;;;UA2BxBnB,EAAL,CAAQ,WAAR,EAAqB,UAAA51B,OAAO,EAAI;;;;UAIxBo6B,MAAM,GAAG,MAAKU,UAAL,IAAmB,MAAKV,MAAvC;YACKp2B,QAAL,CAAcuI,CAAd,IAAmB6tB,MAAnB;YACKvO,SAAL,IAAkBuO,MAAlB;aAEO,MAAKW,IAAL,CAAU,MAAV,EAAkB,YAAM;cACxB/2B,QAAL,CAAcuI,CAAd,IAAmB6tB,MAAnB;cACKvO,SAAL,IAAkBuO,MAAlB;;YACIp6B,OAAO,CAACg7B,SAAR,IAAqB,CAAC,MAAKF,UAA/B,EAA2C;gBACpCA,UAAL,GAAkB,MAAKV,MAAvB;;;YAEE,CAACp6B,OAAO,CAACg7B,SAAb,EAAwB;iBACd,MAAKF,UAAL,GAAkB,CAA1B;;OAPG,CAAP;KARF,EA3B6B;;;UAgDxBlF,EAAL,CAAQ,UAAR,EAAoB,UAAA51B,OAAO,EAAI;UACrBi7B,KADqB,GACXj7B,OADW,CACrBi7B,KADqB;;UAEzBA,KAAK,KAAK,SAAd,EAAyB;QACvBj7B,OAAO,CAACi7B,KAAR,GAAgB,MAAhB;;;YAEGC,QAAL,GAAgB,IAAhB;aAEO,MAAKH,IAAL,CAAU,MAAV,EAAkB,YAAM;cACxB/2B,QAAL,CAAcskB,CAAd,IAAmBtoB,OAAO,CAACm7B,YAAR,IAAwB,CAA3C;QACAn7B,OAAO,CAACi7B,KAAR,GAAgBA,KAAhB;eACQ,MAAKC,QAAL,GAAgB,KAAxB;OAHK,CAAP;KAPF;;;;;;;8BAeQE,MAAM;aAEZ,KAAKp3B,QAAL,CAAcq3B,aAAd,CAA4BD,IAA5B,EAAkC,IAAlC,IACA,KAAKf,gBADL,GAEA,KAAKC,WAHP;;;;2BAOKc,MAAMtP,GAAG;UACVsP,IAAI,CAACA,IAAI,CAACv6B,MAAL,GAAc,CAAf,CAAJ,IAAyBo5B,WAA7B,EAA0C;eACjCnO,CAAC,IAAI,KAAK2O,SAAjB;;;aAEK3O,CAAC,GAAG,KAAKwP,SAAL,CAAepB,MAAf,CAAJ,IAA8B,KAAKO,SAA1C;;;;6BAGO5I,MAAM7b,IAAI;;UAEbulB,EAAJ;UACMC,OAAO,GAAG,IAAIC,WAAJ,CAAgB5J,IAAhB,CAAhB;UACI9wB,IAAI,GAAG,IAAX;UACM26B,UAAU,GAAGp7B,MAAM,CAACiR,MAAP,CAAc,IAAd,CAAnB;;aAEQgqB,EAAE,GAAGC,OAAO,CAACG,SAAR,EAAb,EAAmC;YAC7BC,cAAJ;YACIR,IAAI,GAAGvJ,IAAI,CAACpwB,KAAL,CACT,CAACV,IAAI,IAAI,IAAR,GAAeA,IAAI,CAACg0B,QAApB,GAA+BgB,SAAhC,KAA8C,CADrC,EAETwF,EAAE,CAACxG,QAFM,CAAX;YAIIjJ,CAAC,GACH4P,UAAU,CAACN,IAAD,CAAV,IAAoB,IAApB,GACIM,UAAU,CAACN,IAAD,CADd,GAEKM,UAAU,CAACN,IAAD,CAAV,GAAmB,KAAKE,SAAL,CAAeF,IAAf,CAH1B,CANiC;;;YAa7BtP,CAAC,GAAG,KAAKD,SAAL,GAAiB,KAAKiP,UAA9B,EAA0C;;cAEpCe,GAAG,GAAG96B,IAAV;cACM+6B,GAAG,GAAG,EAAZ;;iBAEOV,IAAI,CAACv6B,MAAZ,EAAoB;;gBAEdiB,CAAJ,EAAOi6B,SAAP;;gBACIjQ,CAAC,GAAG,KAAK2O,SAAb,EAAwB;;;cAGtB34B,CAAC,GAAG+B,IAAI,CAACgQ,IAAL,CAAU,KAAK4mB,SAAL,IAAkB3O,CAAC,GAAGsP,IAAI,CAACv6B,MAA3B,CAAV,CAAJ;cACAirB,CAAC,GAAG,KAAKwP,SAAL,CAAeF,IAAI,CAAC35B,KAAL,CAAW,CAAX,EAAcK,CAAd,CAAf,CAAJ;cACAi6B,SAAS,GAAGjQ,CAAC,IAAI,KAAK2O,SAAV,IAAuB34B,CAAC,GAAGs5B,IAAI,CAACv6B,MAA5C;aALF,MAMO;cACLiB,CAAC,GAAGs5B,IAAI,CAACv6B,MAAT;;;gBAEEm7B,UAAU,GAAGlQ,CAAC,GAAG,KAAK2O,SAAT,IAAsB34B,CAAC,GAAG,CAA3C,CAZkB;;mBAcXk6B,UAAU,IAAID,SAArB,EAAgC;kBAC1BC,UAAJ,EAAgB;gBACdlQ,CAAC,GAAG,KAAKwP,SAAL,CAAeF,IAAI,CAAC35B,KAAL,CAAW,CAAX,EAAc,EAAEK,CAAhB,CAAf,CAAJ;gBACAk6B,UAAU,GAAGlQ,CAAC,GAAG,KAAK2O,SAAT,IAAsB34B,CAAC,GAAG,CAAvC;eAFF,MAGO;gBACLgqB,CAAC,GAAG,KAAKwP,SAAL,CAAeF,IAAI,CAAC35B,KAAL,CAAW,CAAX,EAAc,EAAEK,CAAhB,CAAf,CAAJ;gBACAk6B,UAAU,GAAGlQ,CAAC,GAAG,KAAK2O,SAAT,IAAsB34B,CAAC,GAAG,CAAvC;gBACAi6B,SAAS,GAAGjQ,CAAC,IAAI,KAAK2O,SAAV,IAAuB34B,CAAC,GAAGs5B,IAAI,CAACv6B,MAA5C;;aArBc;;;gBA0BdiB,CAAC,KAAK,CAAN,IAAW,KAAK24B,SAAL,KAAmB,KAAK5O,SAAvC,EAAkD;cAChD/pB,CAAC,GAAG,CAAJ;aA3BgB;;;YA+BlBg6B,GAAG,CAACG,QAAJ,GAAeV,EAAE,CAACU,QAAH,IAAen6B,CAAC,GAAGs5B,IAAI,CAACv6B,MAAvC;YACA+6B,cAAc,GAAG5lB,EAAE,CAAColB,IAAI,CAAC35B,KAAL,CAAW,CAAX,EAAcK,CAAd,CAAD,EAAmBgqB,CAAnB,EAAsBgQ,GAAtB,EAA2BD,GAA3B,CAAnB;YACAA,GAAG,GAAG;cAAEI,QAAQ,EAAE;aAAlB,CAjCkB;;YAoClBb,IAAI,GAAGA,IAAI,CAAC35B,KAAL,CAAWK,CAAX,CAAP;YACAgqB,CAAC,GAAG,KAAKwP,SAAL,CAAeF,IAAf,CAAJ;;gBAEIQ,cAAc,KAAK,KAAvB,EAA8B;;;;SA5ClC,MAgDO;;UAELA,cAAc,GAAG5lB,EAAE,CAAColB,IAAD,EAAOtP,CAAP,EAAUyP,EAAV,EAAcx6B,IAAd,CAAnB;;;YAGE66B,cAAc,KAAK,KAAvB,EAA8B;;;;QAG9B76B,IAAI,GAAGw6B,EAAP;;;;;yBAIC1J,MAAM7xB,SAAS;;;;UAEdA,OAAO,CAACo6B,MAAR,IAAkB,IAAtB,EAA4B;aACrBA,MAAL,GAAcp6B,OAAO,CAACo6B,MAAtB;;;UAEEp6B,OAAO,CAACq6B,gBAAR,IAA4B,IAAhC,EAAsC;aAC/BA,gBAAL,GAAwBr6B,OAAO,CAACq6B,gBAAhC;;;UAEEr6B,OAAO,CAACs6B,WAAR,IAAuB,IAA3B,EAAiC;aAC1BA,WAAL,GAAmBt6B,OAAO,CAACs6B,WAA3B;;;UAEEt6B,OAAO,CAAC66B,QAAR,IAAoB,IAAxB,EAA8B;aACvBA,QAAL,GAAgB76B,OAAO,CAAC66B,QAAxB;OAZgB;;;;;UAkBZqB,KAAK,GAAG,KAAKl4B,QAAL,CAAcskB,CAAd,GAAkB,KAAKtkB,QAAL,CAAc81B,iBAAd,CAAgC,IAAhC,CAAhC;;UACI,KAAK91B,QAAL,CAAcskB,CAAd,GAAkB,KAAKyO,IAAvB,IAA+BmF,KAAK,GAAG,KAAKnF,IAAhD,EAAsD;aAC/CoF,WAAL;;;UAGE53B,MAAM,GAAG,EAAb;UACI63B,SAAS,GAAG,CAAhB;UACIC,EAAE,GAAG,CAAT;UACIC,EAAE,GAAG,CAAT;UAEMhU,CA5BY,GA4BN,KAAKtkB,QA5BC,CA4BZskB,CA5BY;;UA6BZiU,QAAQ,GAAG,SAAXA,QAAW,GAAM;QACrBv8B,OAAO,CAACo8B,SAAR,GAAoBA,SAAS,GAAG,MAAI,CAAC9B,WAAL,IAAoB+B,EAAE,GAAG,CAAzB,CAAhC;QACAr8B,OAAO,CAACw8B,SAAR,GAAoBH,EAApB;QACAr8B,OAAO,CAAC6rB,SAAR,GAAoB,MAAI,CAACA,SAAzB;QACGvD,CAJkB,GAIZ,MAAI,CAACtkB,QAJO,CAIlBskB,CAJkB;;QAKrB,MAAI,CAACmU,IAAL,CAAU,MAAV,EAAkBl4B,MAAlB,EAA0BvE,OAA1B,EAAmC,MAAnC;;eACOs8B,EAAE,EAAT;OANF;;WASKG,IAAL,CAAU,cAAV,EAA0Bz8B,OAA1B,EAAmC,IAAnC;WAEK08B,QAAL,CAAc7K,IAAd,EAAoB,UAACuJ,IAAD,EAAOtP,CAAP,EAAUyP,EAAV,EAAcx6B,IAAd,EAAuB;YACrCA,IAAI,IAAI,IAAR,IAAgBA,IAAI,CAACk7B,QAAzB,EAAmC;UACjC,MAAI,CAACQ,IAAL,CAAU,WAAV,EAAuBz8B,OAAvB,EAAgC,MAAhC;;UACA,MAAI,CAACy6B,SAAL,GAAiB,MAAI,CAAC5O,SAAtB;;;YAGE,MAAI,CAAC8Q,MAAL,CAAYvB,IAAZ,EAAkBtP,CAAlB,CAAJ,EAA0B;UACxBvnB,MAAM,IAAI62B,IAAV;UACAgB,SAAS,IAAItQ,CAAb;UACAuQ,EAAE;;;YAGAd,EAAE,CAACU,QAAH,IAAe,CAAC,MAAI,CAACU,MAAL,CAAYvB,IAAZ,EAAkBtP,CAAlB,CAApB,EAA0C;;;cAGlC8Q,EAAE,GAAG,MAAI,CAAC54B,QAAL,CAAc81B,iBAAd,CAAgC,IAAhC,CAAX;;cAEE,MAAI,CAAC3wB,MAAL,IAAe,IAAf,IACA,MAAI,CAAC0xB,QADL,IAEA,MAAI,CAAC72B,QAAL,CAAcskB,CAAd,GAAkBsU,EAAE,GAAG,CAAvB,GAA2B,MAAI,CAAC7F,IAFhC,IAGA,MAAI,CAAC6D,MAAL,IAAe,MAAI,CAACL,OAJtB,EAKE;gBACI,MAAI,CAACM,QAAL,KAAkB,IAAtB,EAA4B;cAC1B,MAAI,CAACA,QAAL,GAAgB,GAAhB;aAFF;;;YAIAt2B,MAAM,GAAGA,MAAM,CAAC3B,OAAP,CAAe,MAAf,EAAuB,EAAvB,CAAT;YACAw5B,SAAS,GAAG,MAAI,CAACd,SAAL,CAAe/2B,MAAM,GAAG,MAAI,CAACs2B,QAA7B,CAAZ,CALA;;;mBASOt2B,MAAM,IAAI63B,SAAS,GAAG,MAAI,CAACvQ,SAAlC,EAA6C;cAC3CtnB,MAAM,GAAGA,MAAM,CAAC9C,KAAP,CAAa,CAAb,EAAgB,CAAC,CAAjB,EAAoBmB,OAApB,CAA4B,MAA5B,EAAoC,EAApC,CAAT;cACAw5B,SAAS,GAAG,MAAI,CAACd,SAAL,CAAe/2B,MAAM,GAAG,MAAI,CAACs2B,QAA7B,CAAZ;aAXF;;;gBAcIuB,SAAS,IAAI,MAAI,CAACvQ,SAAtB,EAAiC;cAC/BtnB,MAAM,GAAGA,MAAM,GAAG,MAAI,CAACs2B,QAAvB;;;YAGFuB,SAAS,GAAG,MAAI,CAACd,SAAL,CAAe/2B,MAAf,CAAZ;;;cAGEg3B,EAAE,CAACU,QAAP,EAAiB;gBACXnQ,CAAC,GAAG,MAAI,CAAC2O,SAAb,EAAwB;cACtB8B,QAAQ;cACRh4B,MAAM,GAAG62B,IAAT;cACAgB,SAAS,GAAGtQ,CAAZ;cACAuQ,EAAE,GAAG,CAAL;;;YAGF,MAAI,CAACI,IAAL,CAAU,UAAV,EAAsBz8B,OAAtB,EAA+B,MAA/B;WAtCsC;;;cA0CpCuE,MAAM,CAACA,MAAM,CAAC1D,MAAP,GAAgB,CAAjB,CAAN,IAA6Bo5B,WAAjC,EAA8C;YAC5C11B,MAAM,GAAGA,MAAM,CAAC9C,KAAP,CAAa,CAAb,EAAgB,CAAC,CAAjB,IAAsBy4B,MAA/B;YACA,MAAI,CAACO,SAAL,IAAkB,MAAI,CAACa,SAAL,CAAepB,MAAf,CAAlB;;;UAGFqC,QAAQ,GA/CgC;;;cAmDpC,MAAI,CAACv4B,QAAL,CAAcskB,CAAd,GAAkBsU,EAAlB,GAAuB,MAAI,CAAC7F,IAAhC,EAAsC;gBAC9B6E,cAAc,GAAG,MAAI,CAACO,WAAL,EAAvB,CADoC;;;gBAIhC,CAACP,cAAL,EAAqB;cACnBS,EAAE,GAAG,CAAL;cACA93B,MAAM,GAAG,EAAT;qBACO,KAAP;;WA1DoC;;;cA+DpCg3B,EAAE,CAACU,QAAP,EAAiB;YACf,MAAI,CAACxB,SAAL,GAAiB,MAAI,CAAC5O,SAAtB;YACAtnB,MAAM,GAAG,EAAT;YACA63B,SAAS,GAAG,CAAZ;mBACQC,EAAE,GAAG,CAAb;WAJF,MAKO;;YAEL,MAAI,CAAC5B,SAAL,GAAiB,MAAI,CAAC5O,SAAL,GAAiBC,CAAlC;YACAvnB,MAAM,GAAG62B,IAAT;YACAgB,SAAS,GAAGtQ,CAAZ;mBACQuQ,EAAE,GAAG,CAAb;;SAzEJ,MA2EO;iBACG,MAAI,CAAC5B,SAAL,IAAkB3O,CAA1B;;OAxFJ;;UA4FIuQ,EAAE,GAAG,CAAT,EAAY;aACLI,IAAL,CAAU,UAAV,EAAsBz8B,OAAtB,EAA+B,IAA/B;QACAu8B,QAAQ;;;WAGLE,IAAL,CAAU,YAAV,EAAwBz8B,OAAxB,EAAiC,IAAjC,EAzIkB;;;;UA8IdA,OAAO,CAACg7B,SAAR,KAAsB,IAA1B,EAAgC;YAC1BsB,EAAE,GAAG,CAAT,EAAY;eACLxB,UAAL,GAAkB,CAAlB;;;aAEGA,UAAL,IAAmB96B,OAAO,CAACo8B,SAAR,IAAqB,CAAxC;eACQ,KAAKp4B,QAAL,CAAcskB,CAAd,GAAkBA,CAA1B;OALF,MAMO;eACG,KAAKtkB,QAAL,CAAcuI,CAAd,GAAkB,KAAKmuB,MAA/B;;;;;gCAIQ16B,SAAS;WACdy8B,IAAL,CAAU,YAAV,EAAwBz8B,OAAxB,EAAiC,IAAjC;;UAEI,EAAE,KAAK46B,MAAP,GAAgB,KAAKL,OAAzB,EAAkC;;;YAG5B,KAAKpxB,MAAL,IAAe,IAAnB,EAAyB;iBAChB,KAAP;;;aAGGnF,QAAL,CAAc64B,iBAAd;aACKjC,MAAL,GAAc,CAAd;aACKD,MAAL,GAAc,KAAK32B,QAAL,CAAcsT,IAAd,CAAmBvO,OAAnB,CAA2BzD,GAAzC;aACKyxB,IAAL,GAAY,KAAK/yB,QAAL,CAAcsT,IAAd,CAAmByf,IAAnB,EAAZ;aACK/yB,QAAL,CAAcuI,CAAd,GAAkB,KAAKmuB,MAAvB;;YACI,KAAK12B,QAAL,CAAcmY,UAAlB,EAA8B;;;iCACvBnY,QAAL,EAAcgY,SAAd,0CAA2B,KAAKhY,QAAL,CAAcmY,UAAzC;;;aAEGsgB,IAAL,CAAU,WAAV,EAAuBz8B,OAAvB,EAAgC,IAAhC;OAfF,MAgBO;aACAgE,QAAL,CAAcuI,CAAd,IAAmB,KAAKsf,SAAL,GAAiB,KAAK2O,SAAzC;aACKx2B,QAAL,CAAcskB,CAAd,GAAkB,KAAKqS,MAAvB;aACK8B,IAAL,CAAU,aAAV,EAAyBz8B,OAAzB,EAAkC,IAAlC;;;WAGGy8B,IAAL,CAAU,cAAV,EAA0Bz8B,OAA1B,EAAmC,IAAnC;aACO,IAAP;;;;;EAlVsB88B;;ICHlBn5B,WAAW1C,UAAX0C;AAER,gBAAe;EACbo5B,QADa,sBACF;SACJC,KAAL,GAAa,KAAKA,KAAL,CAAWC,IAAX,CAAgB,IAAhB,CAAb,CADS;;SAGJ1wB,CAAL,GAAS,CAAT;SACK+b,CAAL,GAAS,CAAT;WACQ,KAAK4U,QAAL,GAAgB,CAAxB;GANW;EASbzL,OATa,mBASLyL,QATK,EASK;SACXA,QAAL,GAAgBA,QAAhB;WACO,IAAP;GAXW;EAcbC,QAda,oBAcJC,KAdI,EAcG;QACVA,KAAK,IAAI,IAAb,EAAmB;MACjBA,KAAK,GAAG,CAAR;;;SAEG9U,CAAL,IAAU,KAAKwR,iBAAL,CAAuB,IAAvB,IAA+BsD,KAA/B,GAAuC,KAAKF,QAAtD;WACO,IAAP;GAnBW;EAsBbG,MAtBa,kBAsBND,KAtBM,EAsBC;QACRA,KAAK,IAAI,IAAb,EAAmB;MACjBA,KAAK,GAAG,CAAR;;;SAEG9U,CAAL,IAAU,KAAKwR,iBAAL,CAAuB,IAAvB,IAA+BsD,KAA/B,GAAuC,KAAKF,QAAtD;WACO,IAAP;GA3BW;EA8BbI,KA9Ba,iBA8BPzL,IA9BO,EA8BDtlB,CA9BC,EA8BE+b,CA9BF,EA8BKtoB,OA9BL,EA8Bcu9B,YA9Bd,EA8B4B;;;IACvCv9B,OAAO,GAAG,KAAKw9B,YAAL,CAAkBjxB,CAAlB,EAAqB+b,CAArB,EAAwBtoB,OAAxB,CAAV,CADuC;;IAIvC6xB,IAAI,GAAGA,IAAI,IAAI,IAAR,GAAe,EAAf,aAAuBA,IAAvB,CAAP,CAJuC;;QAOnC7xB,OAAO,CAACs6B,WAAZ,EAAyB;MACvBzI,IAAI,GAAGA,IAAI,CAACjvB,OAAL,CAAa,SAAb,EAAwB,GAAxB,CAAP;;;QAGI66B,YAAY,GAAG,SAAfA,YAAe,GAAM;UACrBz9B,OAAO,CAAC09B,YAAZ,EAA0B;QACxB19B,OAAO,CAAC09B,YAAR,CAAqBC,GAArB,CAAyB,KAAI,CAACC,MAAL,CAAY59B,OAAO,CAAC69B,UAAR,IAAsB,GAAlC,EACvB,CAAE,KAAI,CAACC,oBAAL,CAA0B99B,OAAO,CAAC69B,UAAR,IAAsB,GAAhD,CAAF,CADuB,CAAzB;;KAFJ,CAXuC;;;QAmBnC79B,OAAO,CAACkJ,KAAZ,EAAmB;UACb60B,OAAO,GAAG,KAAKC,QAAnB;;UACI,CAACD,OAAL,EAAc;QACZA,OAAO,GAAG,IAAI5D,WAAJ,CAAgB,IAAhB,EAAsBn6B,OAAtB,CAAV;QACA+9B,OAAO,CAACnI,EAAR,CAAW,MAAX,EAAmB2H,YAAnB;QACAQ,OAAO,CAACnI,EAAR,CAAW,WAAX,EAAwB6H,YAAxB;;;WAGGO,QAAL,GAAgBh+B,OAAO,CAACg7B,SAAR,GAAoB+C,OAApB,GAA8B,IAA9C;WACKE,YAAL,GAAoBj+B,OAAO,CAACg7B,SAAR,GAAoBh7B,OAApB,GAA8B,IAAlD;MACA+9B,OAAO,CAACG,IAAR,CAAarM,IAAb,EAAmB7xB,OAAnB,EAViB;KAAnB,MAaO;iDACY6xB,IAAI,CAACpB,KAAL,CAAW,IAAX,CADZ;;;;4DAC8B;cAA1BkB,IAA0B;UACjC8L,YAAY;UACZF,YAAY,CAAC5L,IAAD,EAAO3xB,OAAP,CAAZ;;;;;;;;;WAIG,IAAP;GArEW;EAwEb6xB,IAxEa,gBAwERA,MAxEQ,EAwEFtlB,CAxEE,EAwEC+b,CAxED,EAwEItoB,OAxEJ,EAwEa;WACjB,KAAKs9B,KAAL,CAAWzL,MAAX,EAAiBtlB,CAAjB,EAAoB+b,CAApB,EAAuBtoB,OAAvB,EAAgC,KAAKg9B,KAArC,CAAP;GAzEW;EA4Eb3B,aA5Ea,yBA4ECj5B,MA5ED,EA4EuB;QAAdpC,OAAc,uEAAJ,EAAI;WAEhC,KAAKy5B,KAAL,CAAW4B,aAAX,CAAyBj5B,MAAzB,EAAiC,KAAKo3B,SAAtC,EAAiDx5B,OAAO,CAAC60B,QAAzD,IACA,CAAC70B,OAAO,CAACq6B,gBAAR,IAA4B,CAA7B,KAAmCj4B,MAAM,CAACvB,MAAP,GAAgB,CAAnD,CAFF;GA7EW;EAmFbs9B,cAnFa,0BAmFEtM,IAnFF,EAmFQ7xB,OAnFR,EAmFiB;;;QACpBuM,CADoB,GACX,IADW,CACpBA,CADoB;QACjB+b,CADiB,GACX,IADW,CACjBA,CADiB;IAG5BtoB,OAAO,GAAG,KAAKw9B,YAAL,CAAkBx9B,OAAlB,CAAV;IACAA,OAAO,CAACmJ,MAAR,GAAiBi1B,QAAjB,CAJ4B;;QAMtB3M,OAAO,GAAGzxB,OAAO,CAACyxB,OAAR,IAAmB,KAAKyL,QAAxB,IAAoC,CAApD;;SACKI,KAAL,CAAWzL,IAAX,EAAiB,KAAKtlB,CAAtB,EAAyB,KAAK+b,CAA9B,EAAiCtoB,OAAjC,EAA0C,YAAM;aACtC,MAAI,CAACsoB,CAAL,IAAU,MAAI,CAACwR,iBAAL,CAAuB,IAAvB,IAA+BrI,OAAjD;KADF;;QAIMtoB,MAAM,GAAG,KAAKmf,CAAL,GAASA,CAAxB;SACK/b,CAAL,GAASA,CAAT;SACK+b,CAAL,GAASA,CAAT;WAEOnf,MAAP;GAlGW;EAqGbk1B,IArGa,gBAqGRA,KArGQ,EAqGF9xB,CArGE,EAqGC+b,CArGD,EAqGItoB,OArGJ,EAqGa+9B,OArGb,EAqGsB;IACjC/9B,OAAO,GAAG,KAAKw9B,YAAL,CAAkBjxB,CAAlB,EAAqB+b,CAArB,EAAwBtoB,OAAxB,CAAV;QAEMs+B,QAAQ,GAAGt+B,OAAO,CAACs+B,QAAR,IAAoB,QAArC;QACMC,IAAI,GAAG16B,IAAI,CAACC,KAAL,CAAY,KAAK21B,KAAL,CAAWpI,QAAX,GAAsB,IAAvB,GAA+B,KAAKmI,SAA/C,CAAb;QACMgF,OAAO,GAAGD,IAAI,GAAG,CAAvB;QACMlvB,CAAC,GAAGrP,OAAO,CAACy+B,YAAR,IAAwBF,IAAI,GAAG,CAAzC;QACMnE,MAAM,GACVp6B,OAAO,CAAC0+B,UAAR,KAAuBJ,QAAQ,KAAK,QAAb,GAAwBjvB,CAAC,GAAG,CAA5B,GAAgCkvB,IAAI,GAAG,CAA9D,CADF;QAEMI,UAAU,GACd3+B,OAAO,CAAC4+B,YAAR,KAAyBN,QAAQ,KAAK,QAAb,GAAwBjvB,CAAC,GAAG,CAA5B,GAAgCkvB,IAAI,GAAG,CAAhE,CADF;QAGIM,KAAK,GAAG,CAAZ;QACMt7B,KAAK,GAAG,EAAd;QACMu7B,MAAM,GAAG,EAAf;QACMC,OAAO,GAAG,EAAhB;;QAEIC,OAAO,GAAG,SAAVA,OAAU,CAASX,IAAT,EAAe;UACvBz6B,CAAC,GAAG,CAAR;;WACK,IAAI7B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGs8B,IAAI,CAACx9B,MAAzB,EAAiCkB,CAAC,EAAlC,EAAsC;YAC9Bk9B,IAAI,GAAGZ,IAAI,CAACt8B,CAAD,CAAjB;;YACIP,KAAK,CAAC8B,OAAN,CAAc27B,IAAd,CAAJ,EAAyB;UACvBJ,KAAK;UACLG,OAAO,CAACC,IAAD,CAAP;UACAJ,KAAK;SAHP,MAIO;UACLt7B,KAAK,CAACvC,IAAN,CAAWi+B,IAAX;UACAH,MAAM,CAAC99B,IAAP,CAAY69B,KAAZ;;cACIP,QAAQ,KAAK,QAAjB,EAA2B;YACzBS,OAAO,CAAC/9B,IAAR,CAAa4C,CAAC,EAAd;;;;KAZR;;IAkBAo7B,OAAO,CAACX,KAAD,CAAP;;QAEMa,KAAK,GAAG,SAARA,KAAQ,CAASt7B,CAAT,EAAY;cAChB06B,QAAR;aACO,UAAL;2BACY16B,CAAV;;aACG,UAAL;cACMu7B,MAAM,GAAGh9B,MAAM,CAACo0B,YAAP,CAAqB,CAAC3yB,CAAC,GAAG,CAAL,IAAU,EAAX,GAAiB,EAArC,CAAb;cACIw7B,KAAK,GAAGv7B,IAAI,CAACqH,KAAL,CAAW,CAACtH,CAAC,GAAG,CAAL,IAAU,EAAV,GAAe,CAA1B,CAAZ;cACIiuB,IAAI,GAAGrwB,KAAK,CAAC49B,KAAK,GAAG,CAAT,CAAL,CAAiB/9B,IAAjB,CAAsB89B,MAAtB,CAAX;2BACUtN,IAAV;;KARN;;QAYMwN,YAAY,GAAG,SAAfA,YAAe,CAASC,QAAT,EAAmB;;;MACtCvB,OAAO,GAAG,IAAI5D,WAAJ,CAAgB,IAAhB,EAAsBn6B,OAAtB,CAAV;MACA+9B,OAAO,CAACnI,EAAR,CAAW,MAAX,EAAmB,KAAKoH,KAAxB;MAEA6B,KAAK,GAAG,CAAR;UACI98B,CAAC,GAAG,CAAR;MACAg8B,OAAO,CAAChD,IAAR,CAAa,WAAb,EAA0B,YAAM;YAC1BkE,IAAJ,EAAUM,QAAV,EAAoBC,SAApB,EAA+BC,QAA/B;;YACIz/B,OAAO,CAAC09B,YAAZ,EAA0B;cACpB19B,OAAO,CAAC0/B,WAAZ,EAAyB;sDACW1/B,OAAO,CAAC0/B,WADnB;;YACtBH,QADsB;YACZC,SADY;YACDC,QADC;WAAzB,MAEO;YACJF,QADI,GAC8B,IAD9B;YACMC,SADN,GACoC,KADpC;YACiBC,QADjB,GAC2C,OAD3C;;;;YAKLF,QAAJ,EAAc;UACZN,IAAI,GAAG,MAAI,CAACrB,MAAL,CAAY2B,QAAZ,CAAP;UACAv/B,OAAO,CAAC09B,YAAR,CAAqBC,GAArB,CAAyBsB,IAAzB;SAFF,MAGO,IAAIj/B,OAAO,CAAC09B,YAAZ,EAA0B;UAC/BuB,IAAI,GAAGj/B,OAAO,CAAC09B,YAAf;;;YAGE57B,CAAJ;;YACI,CAACA,CAAC,GAAGg9B,MAAM,CAAC/8B,CAAC,EAAF,CAAX,MAAsB88B,KAA1B,EAAiC;cACzBc,IAAI,GAAGhB,UAAU,IAAI78B,CAAC,GAAG+8B,KAAR,CAAvB;UACA,MAAI,CAACtyB,CAAL,IAAUozB,IAAV;UACA5B,OAAO,CAAClS,SAAR,IAAqB8T,IAArB;UACAd,KAAK,GAAG/8B,CAAR;;;YAGEm9B,IAAI,KAAKO,SAAS,IAAIC,QAAlB,CAAR,EAAqC;UACnCR,IAAI,CAACtB,GAAL,CAAS,MAAI,CAACC,MAAL,CAAY4B,SAAS,IAAIC,QAAzB,EACP,CAAC,MAAI,CAAC3B,oBAAL,CAA0B0B,SAAS,IAAIC,QAAvC,CAAD,CADO,CAAT;;;gBAGMnB,QAAR;eACO,QAAL;YACE,MAAI,CAACtQ,MAAL,CAAY,MAAI,CAACzhB,CAAL,GAAS6tB,MAAT,GAAkB/qB,CAA9B,EAAiC,MAAI,CAACiZ,CAAL,GAASkW,OAA1C,EAAmDnvB,CAAnD;;YACA,MAAI,CAACqgB,IAAL;;;;eAEG,UAAL;eACK,UAAL;gBACMmC,IAAI,GAAGqN,KAAK,CAACH,OAAO,CAACh9B,CAAC,GAAG,CAAL,CAAR,CAAhB;;YACA,MAAI,CAAC69B,SAAL,CAAe/N,IAAf,EAAqB,MAAI,CAACtlB,CAAL,GAAS6tB,MAA9B,EAAsC,MAAI,CAAC9R,CAA3C,EAA8CtoB,OAA9C;;;;;YAIAi/B,IAAI,IAAIO,SAAR,IAAqBC,QAAzB,EAAmC;UACjCR,IAAI,CAACtB,GAAL,CAAS,MAAI,CAACC,MAAL,CAAY6B,QAAZ,EAAsB,CAAC,MAAI,CAAC3B,oBAAL,CAA0B2B,QAA1B,CAAD,CAAtB,CAAT;;;YAEER,IAAI,IAAIA,IAAI,KAAKj/B,OAAO,CAAC09B,YAA7B,EAA2C;UACzCuB,IAAI,CAACj9B,GAAL;;OA7CJ;MAiDA+7B,OAAO,CAACnI,EAAR,CAAW,cAAX,EAA2B,YAAM;YACzBzgB,GAAG,GAAGilB,MAAM,GAAGuE,UAAU,IAAIE,KAAK,GAAG,CAAZ,CAA/B;QACA,MAAI,CAACtyB,CAAL,IAAU4I,GAAV;eACQ4oB,OAAO,CAAClS,SAAR,IAAqB1W,GAA7B;OAHF;MAMA4oB,OAAO,CAACnI,EAAR,CAAW,YAAX,EAAyB,YAAM;YACvBzgB,GAAG,GAAGilB,MAAM,GAAGuE,UAAU,IAAIE,KAAK,GAAG,CAAZ,CAA/B;QACA,MAAI,CAACtyB,CAAL,IAAU4I,GAAV;eACQ4oB,OAAO,CAAClS,SAAR,IAAqB1W,GAA7B;OAHF;MAMA4oB,OAAO,CAACG,IAAR,CAAaoB,QAAb,EAAuBt/B,OAAvB;KAnEF;;SAuEK,IAAI+B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwB,KAAK,CAAC1C,MAA1B,EAAkCkB,CAAC,EAAnC,EAAuC;MACrCs9B,YAAY,CAAC37B,IAAb,CAAkB,IAAlB,EAAwBH,KAAK,CAACxB,CAAD,CAA7B;;;WAGK,IAAP;GAjOW;EAoOby7B,YApOa,0BAoOyB;QAAzBjxB,CAAyB,uEAArB,EAAqB;QAAjB+b,CAAiB;QAAdtoB,OAAc,uEAAJ,EAAI;;QAChC,OAAOuM,CAAP,KAAa,QAAjB,EAA2B;MACzBvM,OAAO,GAAGuM,CAAV;MACAA,CAAC,GAAG,IAAJ;KAHkC;;;QAO9Bye,MAAM,GAAG1qB,MAAM,CAACu/B,MAAP,CAAc,EAAd,EAAkB7/B,OAAlB,CAAf,CAPoC;;QAUhC,KAAKi+B,YAAT,EAAuB;WAChB,IAAI99B,GAAT,IAAgB,KAAK89B,YAArB,EAAmC;YAC3B79B,GAAG,GAAG,KAAK69B,YAAL,CAAkB99B,GAAlB,CAAZ;;YACIA,GAAG,KAAK,WAAZ,EAAyB;cACnB6qB,MAAM,CAAC7qB,GAAD,CAAN,KAAgB41B,SAApB,EAA+B;YAC7B/K,MAAM,CAAC7qB,GAAD,CAAN,GAAcC,GAAd;;;;KAf4B;;;QAsBhCmM,CAAC,IAAI,IAAT,EAAe;WACRA,CAAL,GAASA,CAAT;;;QAEE+b,CAAC,IAAI,IAAT,EAAe;WACRA,CAAL,GAASA,CAAT;KA1BkC;;;QA8BhC0C,MAAM,CAAC8U,SAAP,KAAqB,KAAzB,EAAgC;UAC1B9U,MAAM,CAAC9hB,KAAP,IAAgB,IAApB,EAA0B;QACxB8hB,MAAM,CAAC9hB,KAAP,GAAe,KAAKoO,IAAL,CAAUpO,KAAV,GAAkB,KAAKqD,CAAvB,GAA2B,KAAK+K,IAAL,CAAUvO,OAAV,CAAkBtD,KAA5D;;;MAEFulB,MAAM,CAAC9hB,KAAP,GAAerF,IAAI,CAAC2R,GAAL,CAASwV,MAAM,CAAC9hB,KAAhB,EAAuB,CAAvB,CAAf;;;QAGE,CAAC8hB,MAAM,CAACuP,OAAZ,EAAqB;MACnBvP,MAAM,CAACuP,OAAP,GAAiB,CAAjB;;;QAEEvP,MAAM,CAACwP,SAAP,IAAoB,IAAxB,EAA8B;MAC5BxP,MAAM,CAACwP,SAAP,GAAmB,EAAnB;KAzCkC;;;WA4C7BxP,MAAP;GAhRW;EAmRbgS,KAnRa,iBAmRPnL,IAnRO,EAmRsB;QAAvB7xB,OAAuB,uEAAb,EAAa;QAAT+9B,OAAS;;SAC5B6B,SAAL,CAAe/N,IAAf,EAAqB,KAAKtlB,CAA1B,EAA6B,KAAK+b,CAAlC,EAAqCtoB,OAArC;;QACMyxB,OAAO,GAAGzxB,OAAO,CAACyxB,OAAR,IAAmB,KAAKyL,QAAxB,IAAoC,CAApD;;QAEI,CAACa,OAAL,EAAc;aACJ,KAAKxxB,CAAL,IAAU,KAAK8uB,aAAL,CAAmBxJ,IAAnB,CAAlB;KADF,MAEO;aACG,KAAKvJ,CAAL,IAAU,KAAKwR,iBAAL,CAAuB,IAAvB,IAA+BrI,OAAjD;;GA1RS;EA8RbmO,SA9Ra,qBA8RH/N,IA9RG,EA8RGtlB,CA9RH,EA8RM+b,CA9RN,EA8RStoB,OA9RT,EA8RkB;;;QACzB8V,EAAJ,EAAQsd,OAAR,EAAiBrxB,CAAjB,EAAoByxB,SAApB,EAA+B4I,SAA/B,EAA0C5qB,KAA1C;IACAqgB,IAAI,GAAG,UAAGA,IAAH,EAAUjvB,OAAV,CAAkB,KAAlB,EAAyB,EAAzB,CAAP;;QACIivB,IAAI,CAAChxB,MAAL,KAAgB,CAApB,EAAuB;;KAHM;;;QAQvBo6B,KAAK,GAAGj7B,OAAO,CAACi7B,KAAR,IAAiB,MAA/B;QACIX,WAAW,GAAGt6B,OAAO,CAACs6B,WAAR,IAAuB,CAAzC;QACMD,gBAAgB,GAAGr6B,OAAO,CAACq6B,gBAAR,IAA4B,CAArD,CAV6B;;QAazBr6B,OAAO,CAACkJ,KAAZ,EAAmB;cACT+xB,KAAR;aACO,OAAL;UACEmB,SAAS,GAAG,KAAKf,aAAL,CAAmBxJ,IAAI,CAACjvB,OAAL,CAAa,MAAb,EAAqB,EAArB,CAAnB,EAA6C5C,OAA7C,CAAZ;UACAuM,CAAC,IAAIvM,OAAO,CAAC6rB,SAAR,GAAoBuQ,SAAzB;;;aAGG,QAAL;UACE7vB,CAAC,IAAIvM,OAAO,CAAC6rB,SAAR,GAAoB,CAApB,GAAwB7rB,OAAO,CAACo8B,SAAR,GAAoB,CAAjD;;;aAGG,SAAL;;UAEE5qB,KAAK,GAAGqgB,IAAI,CAACkO,IAAL,GAAYtP,KAAZ,CAAkB,KAAlB,CAAR;UACA2L,SAAS,GAAG,KAAKf,aAAL,CAAmBxJ,IAAI,CAACjvB,OAAL,CAAa,MAAb,EAAqB,EAArB,CAAnB,EAA6C5C,OAA7C,CAAZ;cACIggC,UAAU,GAAG,KAAK3E,aAAL,CAAmB,GAAnB,IAA0BhB,gBAA3C;UACAC,WAAW,GAAGz2B,IAAI,CAAC2R,GAAL,CACZ,CADY,EAEZ,CAACxV,OAAO,CAAC6rB,SAAR,GAAoBuQ,SAArB,IAAkCv4B,IAAI,CAAC2R,GAAL,CAAS,CAAT,EAAYhE,KAAK,CAAC3Q,MAAN,GAAe,CAA3B,CAAlC,GACEm/B,UAHU,CAAd;;;KA7BuB;;;QAuCzB,OAAOhgC,OAAO,CAACigC,QAAf,KAA4B,QAAhC,EAA0C;MACxCnqB,EAAE,GAAG,CAAC9V,OAAO,CAACigC,QAAd;KADF,MAEO;cACGjgC,OAAO,CAACigC,QAAhB;aACO,YAAL;UACEnqB,EAAE,GAAG,MAAM,KAAK2jB,KAAL,CAAWlI,OAAtB;;;aAEG,QAAL;aACK,aAAL;UACEzb,EAAE,GAAG,OAAO,KAAK2jB,KAAL,CAAWnI,SAAX,GAAuB,KAAKmI,KAAL,CAAWpI,QAAzC,CAAL;;;aAEG,QAAL;aACK,aAAL;UACEvb,EAAE,GAAG,KAAK2jB,KAAL,CAAWnI,SAAhB;;;aAEG,YAAL;UACExb,EAAE,GAAG,CAAL;;;aAEG,cAAL;UACEA,EAAE,GAAG,MAAM,KAAK2jB,KAAL,CAAWpI,QAAtB;;;aAEG,SAAL;UACEvb,EAAE,GAAG,MAAM,KAAK2jB,KAAL,CAAWpI,QAAtB;;;aAEG,KAAL;UACEvb,EAAE,GAAG,KAAK2jB,KAAL,CAAWpI,QAAhB;;;;UAGAvb,EAAE,GAAG,KAAK2jB,KAAL,CAAWpI,QAAhB;;;MAEJvb,EAAE,GAAIA,EAAE,GAAG,IAAN,GAAc,KAAK0jB,SAAxB;KArE2B;;;QAyEvB0G,aAAa,GACjBlgC,OAAO,CAACo8B,SAAR,GACA9B,WAAW,IAAIt6B,OAAO,CAACw8B,SAAR,GAAoB,CAAxB,CADX,GAEAnC,gBAAgB,IAAIxI,IAAI,CAAChxB,MAAL,GAAc,CAAlB,CAHlB,CAzE6B;;QA+EzBb,OAAO,CAACmgC,IAAR,IAAgB,IAApB,EAA0B;WACnBA,IAAL,CAAU5zB,CAAV,EAAa+b,CAAb,EAAgB4X,aAAhB,EAA+B,KAAKpG,iBAAL,EAA/B,EAAyD95B,OAAO,CAACmgC,IAAjE;;;QAEEngC,OAAO,CAACogC,IAAR,IAAgB,IAApB,EAA0B;WACnBA,IAAL,CAAU7zB,CAAV,EAAa+b,CAAb,EAAgB4X,aAAhB,EAA+B,KAAKpG,iBAAL,EAA/B,EAAyD95B,OAAO,CAACogC,IAAjE;;;QAEEpgC,OAAO,CAACqgC,WAAR,IAAuB,IAA3B,EAAiC;WAC1BC,mBAAL,CAAyBtgC,OAAO,CAACqgC,WAAjC,EAA8C,KAA9C,EAAqD9zB,CAArD,EAAwD+b,CAAxD,EAA2D,IAA3D;KAtF2B;;;QA0FzBtoB,OAAO,CAACugC,SAAZ,EAAuB;WAChB7U,IAAL;;UACI,CAAC1rB,OAAO,CAACyY,MAAb,EAAqB;aACd2D,WAAL,gCAAqB,KAAKD,UAAL,IAAmB,EAAxC;;;UAGI0P,SAAS,GACb,KAAK2N,SAAL,GAAiB,EAAjB,GAAsB,GAAtB,GAA4B31B,IAAI,CAACqH,KAAL,CAAW,KAAKsuB,SAAL,GAAiB,EAA5B,CAD9B;WAEK3N,SAAL,CAAeA,SAAf;UAEI2U,KAAK,GAAIlY,CAAC,GAAG,KAAKwR,iBAAL,EAAL,GAAkCjO,SAA9C;WACK7D,MAAL,CAAYzb,CAAZ,EAAei0B,KAAf;WACKpY,MAAL,CAAY7b,CAAC,GAAG2zB,aAAhB,EAA+BM,KAA/B;WACK/nB,MAAL;WACKkT,OAAL;KAxG2B;;;QA4GzB3rB,OAAO,CAACygC,MAAZ,EAAoB;WACb/U,IAAL;;UACI,CAAC1rB,OAAO,CAACyY,MAAb,EAAqB;aACd2D,WAAL,gCAAqB,KAAKD,UAAL,IAAmB,EAAxC;;;UAGI0P,UAAS,GACb,KAAK2N,SAAL,GAAiB,EAAjB,GAAsB,GAAtB,GAA4B31B,IAAI,CAACqH,KAAL,CAAW,KAAKsuB,SAAL,GAAiB,EAA5B,CAD9B;;WAEK3N,SAAL,CAAeA,UAAf;;UAEI2U,MAAK,GAAGlY,CAAC,GAAG,KAAKwR,iBAAL,KAA2B,CAA3C;;WACK9R,MAAL,CAAYzb,CAAZ,EAAei0B,MAAf;WACKpY,MAAL,CAAY7b,CAAC,GAAG2zB,aAAhB,EAA+BM,MAA/B;WACK/nB,MAAL;WACKkT,OAAL;;;SAGGD,IAAL,GA7H6B;;QAgIzB1rB,OAAO,CAAC0gC,OAAZ,EAAqB;UACfC,IAAJ;;UACI,OAAO3gC,OAAO,CAAC0gC,OAAf,KAA2B,QAA/B,EAAyC;QACvCC,IAAI,GAAG,CAAC98B,IAAI,CAACyhB,GAAL,CAAUtlB,OAAO,CAAC0gC,OAAR,GAAkB78B,IAAI,CAAC2lB,EAAxB,GAA8B,GAAvC,CAAR;OADF,MAEO;QACLmX,IAAI,GAAG,CAAC,IAAR;;;WAEGzrB,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAxB,EAA2B3I,CAA3B,EAA8B+b,CAA9B;WACKpT,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqByrB,IAArB,EAA2B,CAA3B,EAA8B,CAACA,IAAD,GAAQ7qB,EAAtC,EAA0C,CAA1C;WACKZ,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAxB,EAA2B,CAAC3I,CAA5B,EAA+B,CAAC+b,CAAhC;KAzI2B;;;SA6IxBpT,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAC,CAAzB,EAA4B,CAA5B,EAA+B,KAAKoC,IAAL,CAAUnO,MAAzC;IACAmf,CAAC,GAAG,KAAKhR,IAAL,CAAUnO,MAAV,GAAmBmf,CAAnB,GAAuBxS,EAA3B,CA9I6B;;QAiJzB,KAAKwB,IAAL,CAAUspB,KAAV,CAAgB,KAAKnH,KAAL,CAAWx1B,EAA3B,KAAkC,IAAtC,EAA4C;WACrCqT,IAAL,CAAUspB,KAAV,CAAgB,KAAKnH,KAAL,CAAWx1B,EAA3B,IAAiC,KAAKw1B,KAAL,CAAWpwB,GAAX,EAAjC;KAlJ2B;;;SAsJxB8P,UAAL,CAAgB,IAAhB,EAtJ6B;;SAyJxBA,UAAL,mBAA2BxV,QAAM,CAAC4I,CAAD,CAAjC,cAAwC5I,QAAM,CAAC2kB,CAAD,CAA9C,UAzJ6B;;SA4JxBnP,UAAL,YAAoB,KAAKsgB,KAAL,CAAWx1B,EAA/B,cAAqCN,QAAM,CAAC,KAAK61B,SAAN,CAA3C,UA5J6B;;QA+JvB3mB,IAAI,GAAG7S,OAAO,CAAC0vB,IAAR,IAAgB1vB,OAAO,CAACyY,MAAxB,GAAiC,CAAjC,GAAqCzY,OAAO,CAACyY,MAAR,GAAiB,CAAjB,GAAqB,CAAvE;;QACI5F,IAAJ,EAAU;WACHsG,UAAL,WAAmBtG,IAAnB;KAjK2B;;;QAqKzBwnB,gBAAJ,EAAsB;WACflhB,UAAL,WAAmBxV,QAAM,CAAC02B,gBAAD,CAAzB;KAtK2B;;;;;;QA6KzBC,WAAJ,EAAiB;MACf9oB,KAAK,GAAGqgB,IAAI,CAACkO,IAAL,GAAYtP,KAAZ,CAAkB,KAAlB,CAAR;MACA6J,WAAW,IAAI,KAAKe,aAAL,CAAmB,GAAnB,IAA0BhB,gBAAzC;MACAC,WAAW,IAAI,OAAO,KAAKd,SAA3B;MAEApG,OAAO,GAAG,EAAV;MACAI,SAAS,GAAG,EAAZ;;kDACiBhiB,KAPF;;;;+DAOS;cAAf4pB,IAAe;;mCACe,KAAK3B,KAAL,CAAWrjB,MAAX,CACnCglB,IADmC,EAEnCp7B,OAAO,CAAC60B,QAF2B,CADf;;cACfgM,WADe;cACFC,aADE;;UAKtB1N,OAAO,GAAGA,OAAO,CAACpuB,MAAR,CAAe67B,WAAf,CAAV;UACArN,SAAS,GAAGA,SAAS,CAACxuB,MAAV,CAAiB87B,aAAjB,CAAZ,CANsB;;;cAUhB/kB,KAAK,GAAG,EAAd;cACM9Z,MAAM,GAAGuxB,SAAS,CAACA,SAAS,CAAC3yB,MAAV,GAAmB,CAApB,CAAxB;;eACK,IAAIV,GAAT,IAAgB8B,MAAhB,EAAwB;gBAChB7B,GAAG,GAAG6B,MAAM,CAAC9B,GAAD,CAAlB;YACA4b,KAAK,CAAC5b,GAAD,CAAL,GAAaC,GAAb;;;UAEF2b,KAAK,CAAC0X,QAAN,IAAkB6G,WAAlB;UACA9G,SAAS,CAACA,SAAS,CAAC3yB,MAAV,GAAmB,CAApB,CAAT,GAAkCkb,KAAlC;;;;;;;KAxBJ,MA0BO;gCACkB,KAAK0d,KAAL,CAAWrjB,MAAX,CAAkByb,IAAlB,EAAwB7xB,OAAO,CAAC60B,QAAhC,CADlB;;;;MACJzB,OADI;MACKI,SADL;;;QAIDpD,KAAK,GAAG,KAAKoJ,SAAL,GAAiB,IAA/B;QACM1R,QAAQ,GAAG,EAAjB;QACI/mB,IAAI,GAAG,CAAX;QACIggC,SAAS,GAAG,KAAhB,CA9M6B;;QAiNvBC,UAAU,GAAG,SAAbA,UAAa,CAAAC,GAAG,EAAI;UACpBlgC,IAAI,GAAGkgC,GAAX,EAAgB;YACRxlB,GAAG,GAAG2X,OAAO,CAAC3xB,KAAR,CAAcV,IAAd,EAAoBkgC,GAApB,EAAyB5/B,IAAzB,CAA8B,EAA9B,CAAZ;YACMyyB,OAAO,GACXN,SAAS,CAACyN,GAAG,GAAG,CAAP,CAAT,CAAmBxN,QAAnB,GAA8BD,SAAS,CAACyN,GAAG,GAAG,CAAP,CAAT,CAAmBpN,YADnD;QAEA/L,QAAQ,CAAC9mB,IAAT,YAAkBya,GAAlB,eAA0B9X,QAAM,CAAC,CAACmwB,OAAF,CAAhC;;;aAGM/yB,IAAI,GAAGkgC,GAAf;KARF,CAjN6B;;;QA6NvBC,KAAK,GAAG,SAARA,KAAQ,CAAAn/B,CAAC,EAAI;MACjBi/B,UAAU,CAACj/B,CAAD,CAAV;;UAEI+lB,QAAQ,CAACjnB,MAAT,GAAkB,CAAtB,EAAyB;QACvB,MAAI,CAACsY,UAAL,YAAoB2O,QAAQ,CAACzmB,IAAT,CAAc,GAAd,CAApB;;eACQymB,QAAQ,CAACjnB,MAAT,GAAkB,CAA1B;;KALJ;;SASKkB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGyxB,SAAS,CAAC3yB,MAA1B,EAAkCkB,CAAC,EAAnC,EAAuC;;;UAG/BoT,GAAG,GAAGqe,SAAS,CAACzxB,CAAD,CAArB;;UACIoT,GAAG,CAACwe,OAAJ,IAAexe,GAAG,CAACye,OAAvB,EAAgC;;QAE9BsN,KAAK,CAACn/B,CAAD,CAAL,CAF8B;;aAKzBoX,UAAL,mBACaxV,QAAM,CAAC4I,CAAC,GAAG4I,GAAG,CAACwe,OAAJ,GAAcvD,KAAnB,CADnB,cACgDzsB,QAAM,CAClD2kB,CAAC,GAAGnT,GAAG,CAACye,OAAJ,GAAcxD,KADgC,CADtD;QAKA8Q,KAAK,CAACn/B,CAAC,GAAG,CAAL,CAAL;QAEAg/B,SAAS,GAAG,IAAZ;OAZF,MAaO;;YAEDA,SAAJ,EAAe;eACR5nB,UAAL,mBAA2BxV,QAAM,CAAC4I,CAAD,CAAjC,cAAwC5I,QAAM,CAAC2kB,CAAD,CAA9C;UACAyY,SAAS,GAAG,KAAZ;SAJG;;;YAQD5rB,GAAG,CAACse,QAAJ,GAAete,GAAG,CAAC0e,YAAnB,KAAoC,CAAxC,EAA2C;UACzCmN,UAAU,CAACj/B,CAAC,GAAG,CAAL,CAAV;;;;MAIJwK,CAAC,IAAI4I,GAAG,CAACse,QAAJ,GAAerD,KAApB;KApQ2B;;;IAwQ7B8Q,KAAK,CAACn/B,CAAD,CAAL,CAxQ6B;;SA2QxBoX,UAAL,CAAgB,IAAhB,EA3Q6B;;WA8QtB,KAAKwS,OAAL,EAAP;;CA5iBJ;;ACHA,IAAMwV,OAAO,GAAG,CACd,MADc,EAEd,MAFc,EAGd,MAHc,EAId,MAJc,EAKd,MALc,EAMd,MANc,EAOd,MAPc,EAQd,MARc,EASd,MATc,EAUd,MAVc,EAWd,MAXc,EAYd,MAZc,EAad,MAbc,EAcd,MAdc,EAed,MAfc,CAAhB;AAkBA,IAAMC,eAAe,GAAG;KACnB,YADmB;KAEnB,WAFmB;KAGnB;CAHL;;IAMMC;gBACQn9B,IAAZ,EAAkBg7B,KAAlB,EAAyB;;;QACnBoC,MAAJ;SACKp9B,IAAL,GAAYA,IAAZ;SACKg7B,KAAL,GAAaA,KAAb;;QACI,KAAKh7B,IAAL,CAAUq9B,YAAV,CAAuB,CAAvB,MAA8B,MAAlC,EAA0C;YAClC,uBAAN;KALqB;;;SASlBC,WAAL,GAAmBC,IAAI,CAACC,UAAL,CAAgB,KAAKx9B,IAArB,EAA2By9B,WAA3B,IAA0C,CAA7D;QAEIxsB,GAAG,GAAG,CAAV;;WACOA,GAAG,GAAG,KAAKjR,IAAL,CAAUrD,MAAvB,EAA+B;MAC7BygC,MAAM,GAAG,KAAKp9B,IAAL,CAAUq9B,YAAV,CAAuBpsB,GAAvB,CAAT;MACAA,GAAG,IAAI,CAAP;;UACIgsB,OAAO,CAACtZ,QAAR,CAAiByZ,MAAjB,CAAJ,EAA8B;;;;MAG9BnsB,GAAG,IAAI,KAAKjR,IAAL,CAAUq9B,YAAV,CAAuBpsB,GAAvB,CAAP;;;QAGE,CAACgsB,OAAO,CAACtZ,QAAR,CAAiByZ,MAAjB,CAAL,EAA+B;YACvB,eAAN;;;IAEFnsB,GAAG,IAAI,CAAP;SAEKysB,IAAL,GAAY,KAAK19B,IAAL,CAAUiR,GAAG,EAAb,CAAZ;SACKhM,MAAL,GAAc,KAAKjF,IAAL,CAAUq9B,YAAV,CAAuBpsB,GAAvB,CAAd;IACAA,GAAG,IAAI,CAAP;SAEKjM,KAAL,GAAa,KAAKhF,IAAL,CAAUq9B,YAAV,CAAuBpsB,GAAvB,CAAb;IACAA,GAAG,IAAI,CAAP;QAEM0sB,QAAQ,GAAG,KAAK39B,IAAL,CAAUiR,GAAG,EAAb,CAAjB;SACK2sB,UAAL,GAAkBV,eAAe,CAACS,QAAD,CAAjC;SAEKzvB,GAAL,GAAW,IAAX;;;;;0BAGIpO,UAAU;UACV,KAAKoO,GAAT,EAAc;;;;WAITA,GAAL,GAAWpO,QAAQ,CAACqF,GAAT,CAAa;QACtBI,IAAI,EAAE,SADgB;QAEtB+N,OAAO,EAAE,OAFa;QAGtBuqB,gBAAgB,EAAE,KAAKH,IAHD;QAItBI,KAAK,EAAE,KAAK94B,KAJU;QAKtB+4B,MAAM,EAAE,KAAK94B,MALS;QAMtBkB,UAAU,EAAE,KAAKy3B,UANK;QAOtBz9B,MAAM,EAAE;OAPC,CAAX,CALc;;;;UAkBV,KAAKy9B,UAAL,KAAoB,YAAxB,EAAsC;aAC/B1vB,GAAL,CAASlO,IAAT,CAAc,QAAd,IAA0B,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,EAAoC,GAApC,CAA1B;;;WAGGkO,GAAL,CAASpQ,GAAT,CAAa,KAAKkC,IAAlB,EAtBc;;aAyBN,KAAKA,IAAL,GAAY,IAApB;;;;;;;ICxFEg+B;oBACQh+B,IAAZ,EAAkBg7B,KAAlB,EAAyB;;;SAClBA,KAAL,GAAaA,KAAb;SACKiD,KAAL,GAAa,IAAIC,GAAJ,CAAQl+B,IAAR,CAAb;SACKgF,KAAL,GAAa,KAAKi5B,KAAL,CAAWj5B,KAAxB;SACKC,MAAL,GAAc,KAAKg5B,KAAL,CAAWh5B,MAAzB;SACKk5B,OAAL,GAAe,KAAKF,KAAL,CAAWE,OAA1B;SACKjwB,GAAL,GAAW,IAAX;;;;;0BAGIpO,UAAU;UACVs+B,WAAW,GAAG,KAAlB;WAEKt+B,QAAL,GAAgBA,QAAhB;;UACI,KAAKoO,GAAT,EAAc;;;;UAIRmwB,eAAe,GAAG,KAAKJ,KAAL,CAAWI,eAAnC;UACMC,YAAY,GAAG,KAAKL,KAAL,CAAWM,eAAX,KAA+B,CAApD;WAEKrwB,GAAL,GAAW,KAAKpO,QAAL,CAAcqF,GAAd,CAAkB;QAC3BI,IAAI,EAAE,SADqB;QAE3B+N,OAAO,EAAE,OAFkB;QAG3BuqB,gBAAgB,EAAEQ,eAAe,GAAG,CAAH,GAAO,KAAKJ,KAAL,CAAWP,IAHxB;QAI3BI,KAAK,EAAE,KAAK94B,KAJe;QAK3B+4B,MAAM,EAAE,KAAK94B,MALc;QAM3B9E,MAAM,EAAE;OANC,CAAX;;UASI,CAACk+B,eAAL,EAAsB;YACd3a,MAAM,GAAG,KAAK5jB,QAAL,CAAcqF,GAAd,CAAkB;UAC/Bq5B,SAAS,EAAEF,YAAY,GAAG,CAAH,GAAO,EADC;UAE/BG,MAAM,EAAE,KAAKR,KAAL,CAAWS,MAFY;UAG/Bb,gBAAgB,EAAE,KAAKI,KAAL,CAAWP,IAHE;UAI/BiB,OAAO,EAAE,KAAK35B;SAJD,CAAf;aAOKkJ,GAAL,CAASlO,IAAT,CAAc,aAAd,IAA+B0jB,MAA/B;QACAA,MAAM,CAAC5lB,GAAP;;;UAGE,KAAKmgC,KAAL,CAAWW,OAAX,CAAmBjiC,MAAnB,KAA8B,CAAlC,EAAqC;aAC9BuR,GAAL,CAASlO,IAAT,CAAc,YAAd,IAA8B,KAAKi+B,KAAL,CAAWL,UAAzC;OADF,MAEO;;YAECgB,OAAO,GAAG,KAAK9+B,QAAL,CAAcqF,GAAd,EAAhB;QACAy5B,OAAO,CAAC9gC,GAAR,CAAYQ,MAAM,CAACC,IAAP,CAAY,KAAK0/B,KAAL,CAAWW,OAAvB,CAAZ,EAHK;;aAMA1wB,GAAL,CAASlO,IAAT,CAAc,YAAd,IAA8B,CAC5B,SAD4B,EAE5B,WAF4B,EAG5B,KAAKi+B,KAAL,CAAWW,OAAX,CAAmBjiC,MAAnB,GAA4B,CAA5B,GAAgC,CAHJ,EAI5BiiC,OAJ4B,CAA9B;OAxCY;;;;UAkDV,KAAKX,KAAL,CAAWY,YAAX,CAAwBC,SAAxB,IAAqC,IAAzC,EAA+C;;;YAGvC5iC,GAAG,GAAG,KAAK+hC,KAAL,CAAWY,YAAX,CAAwBC,SAApC;aACK5wB,GAAL,CAASlO,IAAT,CAAc,MAAd,IAAwB,CAAC9D,GAAD,EAAMA,GAAN,CAAxB;OAJF,MAKO,IAAI,KAAK+hC,KAAL,CAAWY,YAAX,CAAwBE,GAA5B,EAAiC;;;YAG9BA,GAH8B,GAGtB,KAAKd,KAAL,CAAWY,YAHW,CAG9BE,GAH8B;YAIhCC,IAAI,GAAG,EAAb;;mDACcD,GALwB;;;;8DAKnB;gBAAV12B,CAAU;YACjB22B,IAAI,CAACliC,IAAL,CAAUuL,CAAV,EAAaA,CAAb;;;;;;;;aAGG6F,GAAL,CAASlO,IAAT,CAAc,MAAd,IAAwBg/B,IAAxB;OATK,MAUA,IAAI,KAAKf,KAAL,CAAWY,YAAX,CAAwBI,OAA5B,EAAqC;;;QAG1Cb,WAAW,GAAG,IAAd;eACO,KAAKc,uBAAL,EAAP;OAJK,MAKA,IAAIb,eAAJ,EAAqB;;;;QAI1BD,WAAW,GAAG,IAAd;eACO,KAAKe,iBAAL,EAAP;;;UAGEb,YAAY,IAAI,CAACF,WAArB,EAAkC;eACzB,KAAKgB,UAAL,EAAP;;;WAGG3+B,QAAL;;;;+BAGS;UACL,KAAK4+B,YAAT,EAAuB;YACfC,KAAK,GAAG,KAAKx/B,QAAL,CAAcqF,GAAd,CAAkB;UAC9BI,IAAI,EAAE,SADwB;UAE9B+N,OAAO,EAAE,OAFqB;UAG9ByqB,MAAM,EAAE,KAAK94B,MAHiB;UAI9B64B,KAAK,EAAE,KAAK94B,KAJkB;UAK9B64B,gBAAgB,EAAE,CALY;UAM9B19B,MAAM,EAAE,aANsB;UAO9BgG,UAAU,EAAE,YAPkB;UAQ9Bo5B,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ;SARI,CAAd;QAWAD,KAAK,CAACxhC,GAAN,CAAU,KAAKuhC,YAAf;aACKnxB,GAAL,CAASlO,IAAT,CAAc,OAAd,IAAyBs/B,KAAzB;OAdO;;;WAkBJpxB,GAAL,CAASpQ,GAAT,CAAa,KAAKqgC,OAAlB,EAlBS;;WAqBJF,KAAL,GAAa,IAAb;aACQ,KAAKE,OAAL,GAAe,IAAvB;;;;wCAGkB;;;aACX,KAAKF,KAAL,CAAWuB,YAAX,CAAwB,UAAAC,MAAM,EAAI;YACnCljC,CAAJ,EAAOmjC,CAAP;YACMC,UAAU,GAAG,KAAI,CAAC1B,KAAL,CAAWS,MAA9B;YACMkB,UAAU,GAAG,KAAI,CAAC56B,KAAL,GAAa,KAAI,CAACC,MAArC;YACMk5B,OAAO,GAAG7/B,MAAM,CAAC8R,KAAP,CAAawvB,UAAU,GAAGD,UAA1B,CAAhB;YACMN,YAAY,GAAG/gC,MAAM,CAAC8R,KAAP,CAAawvB,UAAb,CAArB;YAEI/hC,CAAC,GAAI6hC,CAAC,GAAGnjC,CAAC,GAAG,CAAjB;YACMsxB,GAAG,GAAG4R,MAAM,CAAC9iC,MAAnB,CARuC;;YAUjCkjC,aAAa,GAAG,KAAI,CAAC5B,KAAL,CAAWP,IAAX,KAAoB,EAApB,GAAyB,CAAzB,GAA6B,CAAnD;;eACO7/B,CAAC,GAAGgwB,GAAX,EAAgB;eACT,IAAIiS,UAAU,GAAG,CAAtB,EAAyBA,UAAU,GAAGH,UAAtC,EAAkDG,UAAU,EAA5D,EAAgE;YAC9D3B,OAAO,CAACuB,CAAC,EAAF,CAAP,GAAeD,MAAM,CAAC5hC,CAAC,EAAF,CAArB;YACAA,CAAC,IAAIgiC,aAAL;;;UAEFR,YAAY,CAAC9iC,CAAC,EAAF,CAAZ,GAAoBkjC,MAAM,CAAC5hC,CAAC,EAAF,CAA1B;UACAA,CAAC,IAAIgiC,aAAL;;;QAGF,KAAI,CAAC1B,OAAL,GAAep9B,IAAI,CAACC,WAAL,CAAiBm9B,OAAjB,CAAf;QACA,KAAI,CAACkB,YAAL,GAAoBt+B,IAAI,CAACC,WAAL,CAAiBq+B,YAAjB,CAApB;eACO,KAAI,CAAC5+B,QAAL,EAAP;OAtBK,CAAP;;;;8CA0BwB;;;UAClBo+B,YAAY,GAAG,KAAKZ,KAAL,CAAWY,YAAX,CAAwBI,OAA7C;aACO,KAAKhB,KAAL,CAAWuB,YAAX,CAAwB,UAAAC,MAAM,EAAI;YACjCJ,YAAY,GAAG/gC,MAAM,CAAC8R,KAAP,CAAa,MAAI,CAACpL,KAAL,GAAa,MAAI,CAACC,MAA/B,CAArB;YAEIpH,CAAC,GAAG,CAAR;;aACK,IAAI+R,CAAC,GAAG,CAAR,EAAW9R,GAAG,GAAG2hC,MAAM,CAAC9iC,MAA7B,EAAqCiT,CAAC,GAAG9R,GAAzC,EAA8C8R,CAAC,EAA/C,EAAmD;UACjDyvB,YAAY,CAACxhC,CAAC,EAAF,CAAZ,GAAoBghC,YAAY,CAACY,MAAM,CAAC7vB,CAAD,CAAP,CAAhC;;;QAGF,MAAI,CAACyvB,YAAL,GAAoBt+B,IAAI,CAACC,WAAL,CAAiBq+B,YAAjB,CAApB;eACO,MAAI,CAAC5+B,QAAL,EAAP;OATK,CAAP;;;;iCAaW;;;WACNw9B,KAAL,CAAWuB,YAAX,CAAwB,UAAAC,MAAM,EAAI;QAChC,MAAI,CAACtB,OAAL,GAAep9B,IAAI,CAACC,WAAL,CAAiBy+B,MAAjB,CAAf;;QACA,MAAI,CAACh/B,QAAL;OAFF;;;;;;;IC7JEs/B;;;;;;;yBACQnL,KAAKoG,OAAO;UAClBh7B,IAAJ;;UACI1B,MAAM,CAACM,QAAP,CAAgBg2B,GAAhB,CAAJ,EAA0B;QACxB50B,IAAI,GAAG40B,GAAP;OADF,MAEO,IAAIA,GAAG,YAAYK,WAAnB,EAAgC;QACrCj1B,IAAI,GAAG1B,MAAM,CAACC,IAAP,CAAY,IAAIy2B,UAAJ,CAAeJ,GAAf,CAAZ,CAAP;OADK,MAEA;YACDlH,KAAJ;;YACKA,KAAK,GAAG,yBAAyBsS,IAAzB,CAA8BpL,GAA9B,CAAb,EAAkD;UAChD50B,IAAI,GAAG1B,MAAM,CAACC,IAAP,CAAYmvB,KAAK,CAAC,CAAD,CAAjB,EAAsB,QAAtB,CAAP;SADF,MAEO;UACL1tB,IAAI,GAAG0sB,EAAE,CAACC,YAAH,CAAgBiI,GAAhB,CAAP;;cACI,CAAC50B,IAAL,EAAW;;;;;;UAMXA,IAAI,CAAC,CAAD,CAAJ,KAAY,IAAZ,IAAoBA,IAAI,CAAC,CAAD,CAAJ,KAAY,IAApC,EAA0C;eACjC,IAAIm9B,IAAJ,CAASn9B,IAAT,EAAeg7B,KAAf,CAAP;OADF,MAEO,IAAIh7B,IAAI,CAAC,CAAD,CAAJ,KAAY,IAAZ,IAAoBA,IAAI,CAACvB,QAAL,CAAc,OAAd,EAAuB,CAAvB,EAA0B,CAA1B,MAAiC,KAAzD,EAAgE;eAC9D,IAAIy/B,QAAJ,CAAQl+B,IAAR,EAAcg7B,KAAd,CAAP;OADK,MAEA;cACC,IAAIp/B,KAAJ,CAAU,uBAAV,CAAN;;;;;;;;AC/BN,kBAAe;EACbqkC,UADa,wBACA;SACNC,cAAL,GAAsB,EAAtB;WACQ,KAAKC,WAAL,GAAmB,CAA3B;GAHW;EAMblC,KANa,iBAMPrJ,GANO,EAMFvsB,CANE,EAMC+b,CAND,EAMkB;QAAdtoB,OAAc,uEAAJ,EAAI;QACzBskC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBrC,KAAhB,EAAuBsC,EAAvB,EAA2Bl/B,IAA3B,EAAiCm/B,KAAjC,EAAwCC,WAAxC,EAAqDC,OAArD,EAA8DC,OAA9D;;QACI,OAAOt4B,CAAP,KAAa,QAAjB,EAA2B;MACzBvM,OAAO,GAAGuM,CAAV;MACAA,CAAC,GAAG,IAAJ;KAJ2B;;;QAQvBu4B,iBAAiB,GACrB9kC,OAAO,CAAC8kC,iBAAR,IACC9kC,OAAO,CAAC8kC,iBAAR,KAA8B,KAA9B,IAAuC,KAAK9kC,OAAL,CAAa8kC,iBAFvD;IAIAv4B,CAAC,GAAG,CAAChH,IAAI,GAAGgH,CAAC,IAAI,IAAL,GAAYA,CAAZ,GAAgBvM,OAAO,CAACuM,CAAhC,KAAsC,IAAtC,GAA6ChH,IAA7C,GAAoD,KAAKgH,CAA7D;IACA+b,CAAC,GAAG,CAACoc,KAAK,GAAGpc,CAAC,IAAI,IAAL,GAAYA,CAAZ,GAAgBtoB,OAAO,CAACsoB,CAAjC,KAAuC,IAAvC,GAA8Coc,KAA9C,GAAsD,KAAKpc,CAA/D;;QAEI,OAAOwQ,GAAP,KAAe,QAAnB,EAA6B;MAC3BqJ,KAAK,GAAG,KAAKiC,cAAL,CAAoBtL,GAApB,CAAR;;;QAGE,CAACqJ,KAAL,EAAY;UACNrJ,GAAG,CAAC5vB,KAAJ,IAAa4vB,GAAG,CAAC3vB,MAArB,EAA6B;QAC3Bg5B,KAAK,GAAGrJ,GAAR;OADF,MAEO;QACLqJ,KAAK,GAAG,KAAK4C,SAAL,CAAejM,GAAf,CAAR;;;;QAIA,CAACqJ,KAAK,CAAC/vB,GAAX,EAAgB;MACd+vB,KAAK,CAAC/qB,KAAN,CAAY,IAAZ;;;QAGE,KAAKE,IAAL,CAAU0tB,QAAV,CAAmB7C,KAAK,CAACjD,KAAzB,KAAmC,IAAvC,EAA6C;WACtC5nB,IAAL,CAAU0tB,QAAV,CAAmB7C,KAAK,CAACjD,KAAzB,IAAkCiD,KAAK,CAAC/vB,GAAxC;;;iBAGsB+vB,KAnCK;QAmCvBj5B,KAnCuB,UAmCvBA,KAnCuB;QAmChBC,MAnCgB,UAmChBA,MAnCgB;;QAsCzB,CAAC27B,iBAAD,IAAsB3C,KAAK,CAACX,WAAN,GAAoB,CAA9C,EAAiD;iBAC7B,CAACr4B,MAAD,EAASD,KAAT,CAD6B;MAC9CA,KAD8C;MACvCC,MADuC;;;QAI7C2iB,CAAC,GAAG9rB,OAAO,CAACkJ,KAAR,IAAiBA,KAAzB;QACIyd,CAAC,GAAG3mB,OAAO,CAACmJ,MAAR,IAAkBA,MAA1B;;QAEInJ,OAAO,CAACkJ,KAAR,IAAiB,CAAClJ,OAAO,CAACmJ,MAA9B,EAAsC;UAC9B87B,EAAE,GAAGnZ,CAAC,GAAG5iB,KAAf;MACA4iB,CAAC,GAAG5iB,KAAK,GAAG+7B,EAAZ;MACAte,CAAC,GAAGxd,MAAM,GAAG87B,EAAb;KAHF,MAIO,IAAIjlC,OAAO,CAACmJ,MAAR,IAAkB,CAACnJ,OAAO,CAACkJ,KAA/B,EAAsC;UACrCg8B,EAAE,GAAGve,CAAC,GAAGxd,MAAf;MACA2iB,CAAC,GAAG5iB,KAAK,GAAGg8B,EAAZ;MACAve,CAAC,GAAGxd,MAAM,GAAG+7B,EAAb;KAHK,MAIA,IAAIllC,OAAO,CAACowB,KAAZ,EAAmB;MACxBtE,CAAC,GAAG5iB,KAAK,GAAGlJ,OAAO,CAACowB,KAApB;MACAzJ,CAAC,GAAGxd,MAAM,GAAGnJ,OAAO,CAACowB,KAArB;KAFK,MAGA,IAAIpwB,OAAO,CAACmlC,GAAZ,EAAiB;wCACXnlC,OAAO,CAACmlC,GADG;;MACrBX,EADqB;MACjBF,EADiB;MAEtBC,EAAE,GAAGC,EAAE,GAAGF,EAAV;MACAG,EAAE,GAAGv7B,KAAK,GAAGC,MAAb;;UACIs7B,EAAE,GAAGF,EAAT,EAAa;QACXzY,CAAC,GAAG0Y,EAAJ;QACA7d,CAAC,GAAG6d,EAAE,GAAGC,EAAT;OAFF,MAGO;QACL9d,CAAC,GAAG2d,EAAJ;QACAxY,CAAC,GAAGwY,EAAE,GAAGG,EAAT;;KATG,MAWA,IAAIzkC,OAAO,CAAColC,KAAZ,EAAmB;0CACbplC,OAAO,CAAColC,KADK;;MACvBZ,EADuB;MACnBF,EADmB;MAExBC,EAAE,GAAGC,EAAE,GAAGF,EAAV;MACAG,EAAE,GAAGv7B,KAAK,GAAGC,MAAb;;UACIs7B,EAAE,GAAGF,EAAT,EAAa;QACX5d,CAAC,GAAG2d,EAAJ;QACAxY,CAAC,GAAGwY,EAAE,GAAGG,EAAT;OAFF,MAGO;QACL3Y,CAAC,GAAG0Y,EAAJ;QACA7d,CAAC,GAAG6d,EAAE,GAAGC,EAAT;;;;QAIAzkC,OAAO,CAACmlC,GAAR,IAAenlC,OAAO,CAAColC,KAA3B,EAAkC;UAC5BplC,OAAO,CAACi7B,KAAR,KAAkB,QAAtB,EAAgC;QAC9B1uB,CAAC,GAAGA,CAAC,GAAGi4B,EAAE,GAAG,CAAT,GAAa1Y,CAAC,GAAG,CAArB;OADF,MAEO,IAAI9rB,OAAO,CAACi7B,KAAR,KAAkB,OAAtB,EAA+B;QACpC1uB,CAAC,GAAGA,CAAC,GAAGi4B,EAAJ,GAAS1Y,CAAb;;;UAGE9rB,OAAO,CAACqlC,MAAR,KAAmB,QAAvB,EAAiC;QAC/B/c,CAAC,GAAGA,CAAC,GAAGgc,EAAE,GAAG,CAAT,GAAa3d,CAAC,GAAG,CAArB;OADF,MAEO,IAAI3mB,OAAO,CAACqlC,MAAR,KAAmB,QAAvB,EAAiC;QACtC/c,CAAC,GAAGA,CAAC,GAAGgc,EAAJ,GAAS3d,CAAb;;;;QAIA,CAACme,iBAAL,EAAwB;cACd3C,KAAK,CAACX,WAAd;;;aAGO,CAAL;UACE7a,CAAC,GAAG,CAACA,CAAL;UACA2B,CAAC,IAAI3B,CAAL;UAEAge,WAAW,GAAG,CAAd;;;;aAGG,CAAL;UACE7Y,CAAC,GAAG,CAACA,CAAL;UACAnF,CAAC,GAAG,CAACA,CAAL;UACApa,CAAC,IAAIuf,CAAL;UACAxD,CAAC,IAAI3B,CAAL;UAEAge,WAAW,GAAG,CAAd;;;;aAGG,CAAL;UACEC,OAAO,GAAGr4B,CAAV;UACAs4B,OAAO,GAAGvc,CAAV;UAEA3B,CAAC,GAAG,CAACA,CAAL;UACApa,CAAC,IAAIuf,CAAL;UAEA6Y,WAAW,GAAG,GAAd;;;;aAGG,CAAL;;;;;aAKK,CAAL;UACEC,OAAO,GAAGr4B,CAAV;UACAs4B,OAAO,GAAGvc,CAAV;sBAES,CAAC3B,CAAD,EAAImF,CAAJ,CAJX;UAIGA,CAJH;UAIMnF,CAJN;UAKE2B,CAAC,IAAI3B,CAAL;UAEAge,WAAW,GAAG,EAAd;;;;aAGG,CAAL;UACEC,OAAO,GAAGr4B,CAAV;UACAs4B,OAAO,GAAGvc,CAAV;sBAES,CAAC3B,CAAD,EAAImF,CAAJ,CAJX;UAIGA,CAJH;UAIMnF,CAJN;UAKEA,CAAC,GAAG,CAACA,CAAL;UAEAge,WAAW,GAAG,EAAd;;;;aAGG,CAAL;UACEC,OAAO,GAAGr4B,CAAV;UACAs4B,OAAO,GAAGvc,CAAV;sBAES,CAAC3B,CAAD,EAAImF,CAAJ,CAJX;UAIGA,CAJH;UAIMnF,CAJN;UAKEA,CAAC,GAAG,CAACA,CAAL;UACAmF,CAAC,GAAG,CAACA,CAAL;UACAvf,CAAC,IAAIuf,CAAL;UAEA6Y,WAAW,GAAG,EAAd;;;;aAGG,CAAL;UACEC,OAAO,GAAGr4B,CAAV;UACAs4B,OAAO,GAAGvc,CAAV;sBAES,CAAC3B,CAAD,EAAImF,CAAJ,CAJX;UAIGA,CAJH;UAIMnF,CAJN;UAKEA,CAAC,GAAG,CAACA,CAAL;UACApa,CAAC,IAAIuf,CAAL;UACAxD,CAAC,IAAI3B,CAAL;UAEAge,WAAW,GAAG,CAAC,EAAf;;;KA5EN,MA+EO;MACLhe,CAAC,GAAG,CAACA,CAAL;MACA2B,CAAC,IAAI3B,CAAL;MACAge,WAAW,GAAG,CAAd;KAhL2B;;;QAoLzB3kC,OAAO,CAACmgC,IAAR,IAAgB,IAApB,EAA0B;WACnBA,IAAL,CAAU5zB,CAAV,EAAa+b,CAAb,EAAgBwD,CAAhB,EAAmBnF,CAAnB,EAAsB3mB,OAAO,CAACmgC,IAA9B;;;QAEEngC,OAAO,CAACogC,IAAR,IAAgB,IAApB,EAA0B;WACnBA,IAAL,CAAU7zB,CAAV,EAAa+b,CAAb,EAAgBwD,CAAhB,EAAmBnF,CAAnB,EAAsB3mB,OAAO,CAACogC,IAA9B;;;QAEEpgC,OAAO,CAACqgC,WAAR,IAAuB,IAA3B,EAAiC;WAC1BC,mBAAL,CAAyBtgC,OAAO,CAACqgC,WAAjC,EAA8C,KAA9C,EAAqD9zB,CAArD,EAAwD+b,CAAxD,EAA2D,IAA3D;KA3L2B;;;QA+LzB,KAAKA,CAAL,KAAWA,CAAf,EAAkB;WACXA,CAAL,IAAU3B,CAAV;;;SAGG+E,IAAL;;QAEIiZ,WAAJ,EAAiB;WACV3U,MAAL,CAAY2U,WAAZ,EAAyB;QACvBxU,MAAM,EAAE,CAACyU,OAAD,EAAUC,OAAV;OADV;;;SAKG3vB,SAAL,CAAe4W,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwBnF,CAAxB,EAA2Bpa,CAA3B,EAA8B+b,CAA9B;SACKnP,UAAL,YAAoBgpB,KAAK,CAACjD,KAA1B;SACKvT,OAAL;WAEO,IAAP;GArNW;EAwNboZ,SAxNa,qBAwNHjM,GAxNG,EAwNE;QACTqJ,KAAJ;;QACI,OAAOrJ,GAAP,KAAe,QAAnB,EAA6B;MAC3BqJ,KAAK,GAAG,KAAKiC,cAAL,CAAoBtL,GAApB,CAAR;;;QAGE,CAACqJ,KAAL,EAAY;MACVA,KAAK,GAAG8B,QAAQ,CAACpK,IAAT,CAAcf,GAAd,aAAuB,EAAE,KAAKuL,WAA9B,EAAR;;UACI,OAAOvL,GAAP,KAAe,QAAnB,EAA6B;aACtBsL,cAAL,CAAoBtL,GAApB,IAA2BqJ,KAA3B;;;;WAIGA,KAAP;;CArOJ;;ACFA,uBAAe;EACbmD,QADa,oBACJ/4B,CADI,EACD+b,CADC,EACEwD,CADF,EACKnF,CADL,EACQ3mB,OADR,EACiB;IAC5BA,OAAO,CAACyJ,IAAR,GAAe,OAAf;IACAzJ,OAAO,CAACulC,IAAR,GAAe,KAAKC,YAAL,CAAkBj5B,CAAlB,EAAqB+b,CAArB,EAAwBwD,CAAxB,EAA2BnF,CAA3B,CAAf;IACA3mB,OAAO,CAACylC,MAAR,GAAiB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAjB;;QAEIzlC,OAAO,CAACwX,OAAR,KAAoB,MAApB,IAA8B,OAAOxX,OAAO,CAAC0lC,CAAf,KAAqB,WAAvD,EAAoE;MAClE1lC,OAAO,CAAC0lC,CAAR,GAAY,KAAK,CAAjB,CADkE;;;QAIhE1lC,OAAO,CAACwX,OAAR,KAAoB,MAAxB,EAAgC;UAC1BxX,OAAO,CAACymB,CAAR,IAAa,IAAjB,EAAuB;QACrBzmB,OAAO,CAACymB,CAAR,GAAY,KAAKnR,eAAL,CAAqBtV,OAAO,CAACoV,KAAR,IAAiB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAtC,CAAZ;;KAXwB;;;WAcrBpV,OAAO,CAACoV,KAAf;;QAEI,OAAOpV,OAAO,CAAC2lC,IAAf,KAAwB,QAA5B,EAAsC;MACpC3lC,OAAO,CAAC2lC,IAAR,GAAe,IAAIxjC,MAAJ,CAAWnC,OAAO,CAAC2lC,IAAnB,CAAf;KAjB0B;;;SAqBvB,IAAIxlC,GAAT,IAAgBH,OAAhB,EAAyB;UACjBI,GAAG,GAAGJ,OAAO,CAACG,GAAD,CAAnB;MACAH,OAAO,CAACG,GAAG,CAAC,CAAD,CAAH,CAAO8I,WAAP,KAAuB9I,GAAG,CAACsB,KAAJ,CAAU,CAAV,CAAxB,CAAP,GAA+CrB,GAA/C;;;QAGIiJ,GAAG,GAAG,KAAKA,GAAL,CAASrJ,OAAT,CAAZ;SACKsX,IAAL,CAAUsuB,WAAV,CAAsB5kC,IAAtB,CAA2BqI,GAA3B;IACAA,GAAG,CAACrH,GAAJ;WACO,IAAP;GA9BW;EAiCb6jC,IAjCa,gBAiCRt5B,CAjCQ,EAiCL+b,CAjCK,EAiCFwD,CAjCE,EAiCCnF,CAjCD,EAiCImK,QAjCJ,EAiC4B;QAAd9wB,OAAc,uEAAJ,EAAI;IACvCA,OAAO,CAACwX,OAAR,GAAkB,MAAlB;IACAxX,OAAO,CAAC8J,QAAR,GAAmB,IAAI3H,MAAJ,CAAW2uB,QAAX,CAAnB;IACA9wB,OAAO,CAAC8lC,IAAR,GAAe,SAAf;;QACI9lC,OAAO,CAACoV,KAAR,IAAiB,IAArB,EAA2B;MACzBpV,OAAO,CAACoV,KAAR,GAAgB,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAAhB;;;WAEK,KAAKkwB,QAAL,CAAc/4B,CAAd,EAAiB+b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B3mB,OAA1B,CAAP;GAxCW;EA2CbogC,IA3Ca,gBA2CR7zB,CA3CQ,EA2CL+b,CA3CK,EA2CFwD,CA3CE,EA2CCnF,CA3CD,EA2CIpK,IA3CJ,EA2CwB;QAAdvc,OAAc,uEAAJ,EAAI;IACnCA,OAAO,CAACwX,OAAR,GAAkB,MAAlB;IACAxX,OAAO,CAACwmB,CAAR,GAAY,KAAKnd,GAAL,CAAS;MACnBuO,CAAC,EAAE,MADgB;MAEnBmuB,CAAC,EAAE,IAAI5jC,MAAJ,CAAWoa,IAAX;KAFO,CAAZ;IAIAvc,OAAO,CAACwmB,CAAR,CAAUxkB,GAAV;WACO,KAAKsjC,QAAL,CAAc/4B,CAAd,EAAiB+b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B3mB,OAA1B,CAAP;GAlDW;EAqDbmgC,IArDa,gBAqDR5zB,CArDQ,EAqDL+b,CArDK,EAqDFwD,CArDE,EAqDCnF,CArDD,EAqDIqf,GArDJ,EAqDuB;QAAdhmC,OAAc,uEAAJ,EAAI;IAClCA,OAAO,CAACwX,OAAR,GAAkB,MAAlB;;QAEI,OAAOwuB,GAAP,KAAe,QAAnB,EAA6B;;UAErBC,KAAK,GAAG,KAAKt8B,KAAL,CAAWzF,IAAX,CAAgB0F,KAAhB,CAAsB1F,IAApC;;UACI8hC,GAAG,IAAI,CAAP,IAAYA,GAAG,GAAGC,KAAK,CAACC,IAAN,CAAWrlC,MAAjC,EAAyC;QACvCb,OAAO,CAACwmB,CAAR,GAAY,KAAKnd,GAAL,CAAS;UACnBuO,CAAC,EAAE,MADgB;UAEnBmuB,CAAC,EAAE,CAACE,KAAK,CAACC,IAAN,CAAWF,GAAX,CAAD,EAAkB,KAAlB,EAAyB,IAAzB,EAA+B,IAA/B,EAAqC,IAArC;SAFO,CAAZ;QAIAhmC,OAAO,CAACwmB,CAAR,CAAUxkB,GAAV;OALF,MAMO;cACC,IAAIlC,KAAJ,oCAAsCkmC,GAAtC,EAAN;;KAVJ,MAYO;;MAELhmC,OAAO,CAACwmB,CAAR,GAAY,KAAKnd,GAAL,CAAS;QACnBuO,CAAC,EAAE,KADgB;QAEnBuuB,GAAG,EAAE,IAAIhkC,MAAJ,CAAW6jC,GAAX;OAFK,CAAZ;MAIAhmC,OAAO,CAACwmB,CAAR,CAAUxkB,GAAV;;;WAGK,KAAKsjC,QAAL,CAAc/4B,CAAd,EAAiB+b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B3mB,OAA1B,CAAP;GA7EW;EAgFbomC,OAhFa,mBAgFL75B,CAhFK,EAgFF+b,CAhFE,EAgFCwD,CAhFD,EAgFInF,CAhFJ,EAgFqB;QAAd3mB,OAAc,uEAAJ,EAAI;;6BACP,KAAKwlC,YAAL,CAAkBj5B,CAAlB,EAAqB+b,CAArB,EAAwBwD,CAAxB,EAA2BnF,CAA3B,CADO;;QACzBtN,EADyB;QACrBC,EADqB;QACjBC,EADiB;QACbC,EADa;;IAEhCxZ,OAAO,CAACqmC,UAAR,GAAqB,CAAChtB,EAAD,EAAKG,EAAL,EAASD,EAAT,EAAaC,EAAb,EAAiBH,EAAjB,EAAqBC,EAArB,EAAyBC,EAAzB,EAA6BD,EAA7B,CAArB;IACAtZ,OAAO,CAAC8J,QAAR,GAAmB,IAAI3H,MAAJ,EAAnB;WACO,KAAKmjC,QAAL,CAAc/4B,CAAd,EAAiB+b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B3mB,OAA1B,CAAP;GApFW;EAuFbsmC,SAvFa,qBAuFH/5B,CAvFG,EAuFA+b,CAvFA,EAuFGwD,CAvFH,EAuFMnF,CAvFN,EAuFuB;QAAd3mB,OAAc,uEAAJ,EAAI;IAClCA,OAAO,CAACwX,OAAR,GAAkB,WAAlB;;QACIxX,OAAO,CAACoV,KAAR,IAAiB,IAArB,EAA2B;MACzBpV,OAAO,CAACoV,KAAR,GAAgB,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAAhB;;;WAEK,KAAKgxB,OAAL,CAAa75B,CAAb,EAAgB+b,CAAhB,EAAmBwD,CAAnB,EAAsBnF,CAAtB,EAAyB3mB,OAAzB,CAAP;GA5FW;EA+FbugC,SA/Fa,qBA+FHh0B,CA/FG,EA+FA+b,CA/FA,EA+FGwD,CA/FH,EA+FMnF,CA/FN,EA+FuB;QAAd3mB,OAAc,uEAAJ,EAAI;IAClCA,OAAO,CAACwX,OAAR,GAAkB,WAAlB;WACO,KAAK4uB,OAAL,CAAa75B,CAAb,EAAgB+b,CAAhB,EAAmBwD,CAAnB,EAAsBnF,CAAtB,EAAyB3mB,OAAzB,CAAP;GAjGW;EAoGbygC,MApGa,kBAoGNl0B,CApGM,EAoGH+b,CApGG,EAoGAwD,CApGA,EAoGGnF,CApGH,EAoGoB;QAAd3mB,OAAc,uEAAJ,EAAI;IAC/BA,OAAO,CAACwX,OAAR,GAAkB,WAAlB;WACO,KAAK4uB,OAAL,CAAa75B,CAAb,EAAgB+b,CAAhB,EAAmBwD,CAAnB,EAAsBnF,CAAtB,EAAyB3mB,OAAzB,CAAP;GAtGW;EAyGbumC,cAzGa,0BAyGEltB,EAzGF,EAyGMC,EAzGN,EAyGUC,EAzGV,EAyGcC,EAzGd,EAyGgC;QAAdxZ,OAAc,uEAAJ,EAAI;IAC3CA,OAAO,CAACwX,OAAR,GAAkB,MAAlB;IACAxX,OAAO,CAAC8J,QAAR,GAAmB,IAAI3H,MAAJ,EAAnB;IACAnC,OAAO,CAAC4mB,CAAR,GAAY,CAACvN,EAAD,EAAK,KAAK/B,IAAL,CAAUnO,MAAV,GAAmBmQ,EAAxB,EAA4BC,EAA5B,EAAgC,KAAKjC,IAAL,CAAUnO,MAAV,GAAmBqQ,EAAnD,CAAZ;WACO,KAAK8rB,QAAL,CAAcjsB,EAAd,EAAkBC,EAAlB,EAAsBC,EAAtB,EAA0BC,EAA1B,EAA8BxZ,OAA9B,CAAP;GA7GW;EAgHbwmC,cAhHa,0BAgHEj6B,CAhHF,EAgHK+b,CAhHL,EAgHQwD,CAhHR,EAgHWnF,CAhHX,EAgH4B;QAAd3mB,OAAc,uEAAJ,EAAI;IACvCA,OAAO,CAACwX,OAAR,GAAkB,QAAlB;IACAxX,OAAO,CAAC8J,QAAR,GAAmB,IAAI3H,MAAJ,EAAnB;WACO,KAAKmjC,QAAL,CAAc/4B,CAAd,EAAiB+b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B3mB,OAA1B,CAAP;GAnHW;EAsHbymC,iBAtHa,6BAsHKl6B,CAtHL,EAsHQ+b,CAtHR,EAsHWwD,CAtHX,EAsHcnF,CAtHd,EAsH+B;QAAd3mB,OAAc,uEAAJ,EAAI;IAC1CA,OAAO,CAACwX,OAAR,GAAkB,QAAlB;IACAxX,OAAO,CAAC8J,QAAR,GAAmB,IAAI3H,MAAJ,EAAnB;WACO,KAAKmjC,QAAL,CAAc/4B,CAAd,EAAiB+b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B3mB,OAA1B,CAAP;GAzHW;EA4Hb0mC,cA5Ha,0BA4HEn6B,CA5HF,EA4HK+b,CA5HL,EA4HQwD,CA5HR,EA4HWnF,CA5HX,EA4HckL,IA5Hd,EA4HkC;QAAd7xB,OAAc,uEAAJ,EAAI;IAC7CA,OAAO,CAACwX,OAAR,GAAkB,UAAlB;IACAxX,OAAO,CAAC8J,QAAR,GAAmB,IAAI3H,MAAJ,CAAW0vB,IAAX,CAAnB;IACA7xB,OAAO,CAAC2mC,EAAR,GAAa,IAAIxkC,MAAJ,EAAb;WACO,KAAKmjC,QAAL,CAAc/4B,CAAd,EAAiB+b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B3mB,OAA1B,CAAP;GAhIW;EAmIb4mC,cAnIa,0BAmIEr6B,CAnIF,EAmIK+b,CAnIL,EAmIQwD,CAnIR,EAmIWnF,CAnIX,EAmIuC;QAAzBkgB,IAAyB,uEAAlB,EAAkB;QAAd7mC,OAAc,uEAAJ,EAAI;;QAE5C8mC,QAAQ,GAAG,KAAKD,IAAL,CACfA,IAAI,CAAC/N,GADU,EAEfx4B,MAAM,CAACu/B,MAAP,CAAc;MAAEkH,MAAM,EAAE;KAAxB,EAAgCF,IAAhC,CAFe,CAAjB;IAKA7mC,OAAO,CAACwX,OAAR,GAAkB,gBAAlB;IACAxX,OAAO,CAACgnC,EAAR,GAAaF,QAAb,CARkD;;QAW9C9mC,OAAO,CAAC8J,QAAZ,EAAsB;MACpB9J,OAAO,CAAC8J,QAAR,GAAmB,IAAI3H,MAAJ,CAAWnC,OAAO,CAAC8J,QAAnB,CAAnB;KADF,MAEO,IAAIg9B,QAAQ,CAAC5iC,IAAT,CAAc+iC,IAAlB,EAAwB;MAC7BjnC,OAAO,CAAC8J,QAAR,GAAmBg9B,QAAQ,CAAC5iC,IAAT,CAAc+iC,IAAjC;;;WAGK,KAAK3B,QAAL,CAAc/4B,CAAd,EAAiB+b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B3mB,OAA1B,CAAP;GApJW;EAuJbwlC,YAvJa,wBAuJAnsB,EAvJA,EAuJIC,EAvJJ,EAuJQwS,CAvJR,EAuJWnF,CAvJX,EAuJc;;QAErBnN,EAAE,GAAGF,EAAT;IACAA,EAAE,IAAIqN,CAAN,CAHyB;;QAMrBpN,EAAE,GAAGF,EAAE,GAAGyS,CAAd,CANyB;;oCASQ,KAAKpT,IATb;QASlBC,EATkB;QASdC,EATc;QASVC,EATU;QASNC,EATM;QASFC,EATE;QASEC,EATF;;IAUzBK,EAAE,GAAGV,EAAE,GAAGU,EAAL,GAAUR,EAAE,GAAGS,EAAf,GAAoBP,EAAzB;IACAO,EAAE,GAAGV,EAAE,GAAGS,EAAL,GAAUP,EAAE,GAAGQ,EAAf,GAAoBN,EAAzB;IACAO,EAAE,GAAGZ,EAAE,GAAGY,EAAL,GAAUV,EAAE,GAAGW,EAAf,GAAoBT,EAAzB;IACAS,EAAE,GAAGZ,EAAE,GAAGW,EAAL,GAAUT,EAAE,GAAGU,EAAf,GAAoBR,EAAzB;WAEO,CAACK,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,CAAP;;CAtKJ;;ICAM0tB;sBACQljC,QAAZ,EAAsBmjC,MAAtB,EAA8BC,KAA9B,EAAqCC,IAArC,EAA0E;QAA/BrnC,OAA+B,uEAArB;MAAEsnC,QAAQ,EAAE;KAAS;;;;SACnEtjC,QAAL,GAAgBA,QAAhB;SACKhE,OAAL,GAAeA,OAAf;SACKunC,WAAL,GAAmB,EAAnB;;QAEIF,IAAI,KAAK,IAAb,EAAmB;WACZE,WAAL,CAAiB,MAAjB,IAA2B,CAACF,IAAI,CAAC79B,UAAN,EAAkB,KAAlB,CAA3B;;;QAGE29B,MAAM,KAAK,IAAf,EAAqB;WACdI,WAAL,CAAiB,QAAjB,IAA6BJ,MAA7B;;;QAGEC,KAAK,KAAK,IAAd,EAAoB;WACbG,WAAL,CAAiB,OAAjB,IAA4B,IAAIplC,MAAJ,CAAWilC,KAAX,CAA5B;;;SAGG59B,UAAL,GAAkB,KAAKxF,QAAL,CAAcqF,GAAd,CAAkB,KAAKk+B,WAAvB,CAAlB;SACKC,QAAL,GAAgB,EAAhB;;;;;4BAGMJ,OAAsC;UAA/BpnC,OAA+B,uEAArB;QAAEsnC,QAAQ,EAAE;OAAS;UACtCtc,MAAM,GAAG,IAAIkc,UAAJ,CACb,KAAKljC,QADQ,EAEb,KAAKwF,UAFQ,EAGb49B,KAHa,EAIb,KAAKpjC,QAAL,CAAcsT,IAJD,EAKbtX,OALa,CAAf;WAOKwnC,QAAL,CAAcxmC,IAAd,CAAmBgqB,MAAnB;aAEOA,MAAP;;;;iCAGW;UACP,KAAKwc,QAAL,CAAc3mC,MAAd,GAAuB,CAA3B,EAA8B;YACxB,KAAKb,OAAL,CAAasnC,QAAjB,EAA2B;eACpBC,WAAL,CAAiBE,KAAjB,GAAyB,KAAKD,QAAL,CAAc3mC,MAAvC;;;YAGIC,KAAK,GAAG,KAAK0mC,QAAL,CAAc,CAAd,CAAd;YACEzmC,IAAI,GAAG,KAAKymC,QAAL,CAAc,KAAKA,QAAL,CAAc3mC,MAAd,GAAuB,CAArC,CADT;aAEK0mC,WAAL,CAAiBG,KAAjB,GAAyB5mC,KAAK,CAAC0I,UAA/B;aACK+9B,WAAL,CAAiBI,IAAjB,GAAwB5mC,IAAI,CAACyI,UAA7B;;aAEK,IAAIzH,CAAC,GAAG,CAAR,EAAWgwB,GAAG,GAAG,KAAKyV,QAAL,CAAc3mC,MAApC,EAA4CkB,CAAC,GAAGgwB,GAAhD,EAAqDhwB,CAAC,EAAtD,EAA0D;cAClD6lC,KAAK,GAAG,KAAKJ,QAAL,CAAczlC,CAAd,CAAd;;cACIA,CAAC,GAAG,CAAR,EAAW;YACT6lC,KAAK,CAACL,WAAN,CAAkBM,IAAlB,GAAyB,KAAKL,QAAL,CAAczlC,CAAC,GAAG,CAAlB,EAAqByH,UAA9C;;;cAEEzH,CAAC,GAAG,KAAKylC,QAAL,CAAc3mC,MAAd,GAAuB,CAA/B,EAAkC;YAChC+mC,KAAK,CAACL,WAAN,CAAkBO,IAAlB,GAAyB,KAAKN,QAAL,CAAczlC,CAAC,GAAG,CAAlB,EAAqByH,UAA9C;;;UAEFo+B,KAAK,CAACG,UAAN;;;;aAIG,KAAKv+B,UAAL,CAAgBxH,GAAhB,EAAP;;;;;;;ACxDJ,mBAAe;EACbgmC,WADa,yBACC;WACJ,KAAKC,OAAL,GAAe,IAAIf,UAAJ,CAAe,IAAf,EAAqB,IAArB,EAA2B,IAA3B,EAAiC,IAAjC,CAAvB;GAFW;EAKba,UALa,wBAKA;SACNE,OAAL,CAAaF,UAAb;;QACI,KAAKE,OAAL,CAAaT,QAAb,CAAsB3mC,MAAtB,GAA+B,CAAnC,EAAsC;WAC/B8I,KAAL,CAAWzF,IAAX,CAAgBgkC,QAAhB,GAA2B,KAAKD,OAAL,CAAaz+B,UAAxC;aACQ,KAAKG,KAAL,CAAWzF,IAAX,CAAgBikC,QAAhB,GAA2B,aAAnC;;;CATN;;ACFA;;;;IAKMC;+BACQC,OAAZ,EAAqBC,IAArB,EAA2B;;;SACpBC,IAAL,GAAY,CAAC;MAAEF,OAAO,EAAPA,OAAF;MAAWC,IAAI,EAAJA;KAAZ,CAAZ;;;;;yBAGGE,eAAe;;;MAClBA,aAAa,CAACD,IAAd,CAAmBhuB,OAAnB,CAA2B,UAAClR,GAAD;eAAS,KAAI,CAACk/B,IAAL,CAAUvnC,IAAV,CAAeqI,GAAf,CAAT;OAA3B;;;;;;;ICJEo/B;+BACQzkC,QAAZ,EAAsB0kC,IAAtB,EAA2D;;;QAA/B1oC,OAA+B,uEAArB,EAAqB;QAAjBwnC,QAAiB,uEAAN,IAAM;;;;SACpDxjC,QAAL,GAAgBA,QAAhB;SAEK2kC,SAAL,GAAiB,KAAjB;SACKC,MAAL,GAAc,KAAd;SACKC,QAAL,GAAgB,KAAhB;SACKr/B,UAAL,GAAkBxF,QAAQ,CAACqF,GAAT,CAAa;;MAE7BuO,CAAC,EAAE8wB;KAFa,CAAlB;QAKMxkC,IAAI,GAAG,KAAKsF,UAAL,CAAgBtF,IAA7B;;QAEI1C,KAAK,CAAC8B,OAAN,CAActD,OAAd,KAA0B,KAAK8oC,aAAL,CAAmB9oC,OAAnB,CAA9B,EAA2D;MACzDwnC,QAAQ,GAAGxnC,OAAX;MACAA,OAAO,GAAG,EAAV;;;QAGE,OAAOA,OAAO,CAAConC,KAAf,KAAyB,WAA7B,EAA0C;MACxCljC,IAAI,CAAC+iB,CAAL,GAAS,IAAI9kB,MAAJ,CAAWnC,OAAO,CAAConC,KAAnB,CAAT;;;QAEE,OAAOpnC,OAAO,CAAC+oC,IAAf,KAAwB,WAA5B,EAAyC;MACvC7kC,IAAI,CAAC8kC,IAAL,GAAY,IAAI7mC,MAAJ,CAAWnC,OAAO,CAAC+oC,IAAnB,CAAZ;;;QAEE,OAAO/oC,OAAO,CAACipC,GAAf,KAAuB,WAA3B,EAAwC;MACtC/kC,IAAI,CAACglC,GAAL,GAAW,IAAI/mC,MAAJ,CAAWnC,OAAO,CAACipC,GAAnB,CAAX;;;QAEE,OAAOjpC,OAAO,CAACsnC,QAAf,KAA4B,WAAhC,EAA6C;MAC3CpjC,IAAI,CAACilC,CAAL,GAAS,IAAIhnC,MAAJ,CAAWnC,OAAO,CAACsnC,QAAnB,CAAT;;;QAEE,OAAOtnC,OAAO,CAACopC,MAAf,KAA0B,WAA9B,EAA2C;MACzCllC,IAAI,CAACmlC,UAAL,GAAkB,IAAIlnC,MAAJ,CAAWnC,OAAO,CAACopC,MAAnB,CAAlB;;;SAGGE,SAAL,GAAiB,EAAjB;;QAEI9B,QAAJ,EAAc;UACR,CAAChmC,KAAK,CAAC8B,OAAN,CAAckkC,QAAd,CAAL,EAA8B;QAC5BA,QAAQ,GAAG,CAACA,QAAD,CAAX;;;MAEFA,QAAQ,CAACjtB,OAAT,CAAiB,UAACqtB,KAAD;eAAW,KAAI,CAACjK,GAAL,CAASiK,KAAT,CAAX;OAAjB;WACK5lC,GAAL;;;;;;wBAIA4lC,OAAO;UACL,KAAKgB,MAAT,EAAiB;cACT,IAAI9oC,KAAJ,uDAAN;;;UAGE,CAAC,KAAKgpC,aAAL,CAAmBlB,KAAnB,CAAL,EAAgC;cACxB,IAAI9nC,KAAJ,mCAAN;;;UAGE8nC,KAAK,YAAYa,mBAArB,EAA0C;QACxCb,KAAK,CAAC2B,SAAN,CAAgB,KAAK//B,UAArB;;YACI,KAAKm/B,SAAT,EAAoB;UAClBf,KAAK,CAAC4B,WAAN;;;;UAIA5B,KAAK,YAAYQ,mBAArB,EAA0C;aACnCqB,uBAAL,CAA6B7B,KAA7B;;;UAGE,OAAOA,KAAP,KAAiB,UAAjB,IAA+B,KAAKe,SAAxC,EAAmD;;QAEjDf,KAAK,GAAG,KAAK8B,kBAAL,CAAwB9B,KAAxB,CAAR;;;WAGG0B,SAAL,CAAetoC,IAAf,CAAoB4mC,KAApB;;aAEO,IAAP;;;;4CAGsBx+B,SAAS;;;MAC/BA,OAAO,CAACm/B,IAAR,CAAahuB,OAAb,CAAqB,gBAAuB;YAApB8tB,OAAoB,QAApBA,OAAoB;YAAXC,IAAW,QAAXA,IAAW;;YACpCqB,iBAAiB,GAAG,MAAI,CAAC3lC,QAAL,CAAc4lC,mBAAd,GACvBC,GADuB,CACnBxB,OAAO,CAACnkC,IAAR,CAAaqG,aADM,CAA1B;;QAEAo/B,iBAAiB,CAACrB,IAAD,CAAjB,GAA0B,MAAI,CAAC9+B,UAA/B;OAHF;;;;8BAOQsgC,WAAW;UACf,KAAKtgC,UAAL,CAAgBtF,IAAhB,CAAqB6M,CAAzB,EAA4B;cACpB,IAAIjR,KAAJ,mDAAN;;;WAGG0J,UAAL,CAAgBtF,IAAhB,CAAqB6M,CAArB,GAAyB+4B,SAAzB;;WAEKC,MAAL;;;;kCAGY;;;UACR,KAAKpB,SAAT,EAAoB;;;;WAIfW,SAAL,CAAe/uB,OAAf,CAAuB,UAACqtB,KAAD,EAAQrzB,KAAR,EAAkB;YACnCqzB,KAAK,YAAYa,mBAArB,EAA0C;UACxCb,KAAK,CAAC4B,WAAN;;;YAEE,OAAO5B,KAAP,KAAiB,UAArB,EAAiC;UAC/B,MAAI,CAAC0B,SAAL,CAAe/0B,KAAf,IAAwB,MAAI,CAACm1B,kBAAL,CAAwB9B,KAAxB,CAAxB;;OALJ;;WASKe,SAAL,GAAiB,IAAjB;;WAEKoB,MAAL;;;;0BAGI;UACA,KAAKnB,MAAT,EAAiB;;;;WAIZU,SAAL,CACGr8B,MADH,CACU,UAAC26B,KAAD;eAAWA,KAAK,YAAYa,mBAA5B;OADV,EAEGluB,OAFH,CAEW,UAACqtB,KAAD;eAAWA,KAAK,CAAC5lC,GAAN,EAAX;OAFX;;WAIK4mC,MAAL,GAAc,IAAd;;WAEKmB,MAAL;;;;kCAGYnC,OAAO;aACZA,KAAK,YAAYa,mBAAjB,IACHb,KAAK,YAAYQ,mBADd,IAEH,OAAOR,KAAP,KAAiB,UAFrB;;;;uCAKiBoC,SAAS;UACpB5gC,OAAO,GAAG,KAAKpF,QAAL,CAAc85B,oBAAd,CAAmC,KAAKt0B,UAAL,CAAgBtF,IAAhB,CAAqB0T,CAAxD,CAAhB;MACAoyB,OAAO;WACFhmC,QAAL,CAAcimC,gBAAd;;WAEKR,uBAAL,CAA6BrgC,OAA7B;;aAEOA,OAAP;;;;mCAGa;UACT,CAAC,KAAKI,UAAL,CAAgBtF,IAAhB,CAAqB6M,CAAtB,IAA2B,CAAC,KAAK63B,MAArC,EAA6C;eACpC,KAAP;;;aAGK,KAAKU,SAAL,CAAe1c,KAAf,CAAqB,UAACgb,KAAD,EAAW;YACjC,OAAOA,KAAP,KAAiB,UAArB,EAAiC;iBACxB,KAAP;;;YAEEA,KAAK,YAAYa,mBAArB,EAA0C;iBACjCb,KAAK,CAACsC,YAAN,EAAP;;;eAEK,IAAP;OAPK,CAAP;;;;6BAWO;;;UACH,KAAKrB,QAAL,IAAiB,CAAC,KAAKqB,YAAL,EAAtB,EAA2C;;;;WAItC1gC,UAAL,CAAgBtF,IAAhB,CAAqBimC,CAArB,GAAyB,EAAzB;;WAEKb,SAAL,CAAe/uB,OAAf,CAAuB,UAACqtB,KAAD;eAAW,MAAI,CAACwC,WAAL,CAAiBxC,KAAjB,CAAX;OAAvB;;WAEKp+B,UAAL,CAAgBxH,GAAhB,GATO;;;;WAcFsnC,SAAL,GAAiB,EAAjB;WACK9/B,UAAL,CAAgBtF,IAAhB,CAAqBimC,CAArB,GAAyB,IAAzB;WAEKtB,QAAL,GAAgB,IAAhB;;;;gCAGUjB,OAAO;;;UACbA,KAAK,YAAYa,mBAArB,EAA0C;aACnCj/B,UAAL,CAAgBtF,IAAhB,CAAqBimC,CAArB,CAAuBnpC,IAAvB,CAA4B4mC,KAAK,CAACp+B,UAAlC;;;UAGEo+B,KAAK,YAAYQ,mBAArB,EAA0C;QACxCR,KAAK,CAACW,IAAN,CAAWhuB,OAAX,CAAmB,iBAAuB;cAApB8tB,OAAoB,SAApBA,OAAoB;cAAXC,IAAW,SAAXA,IAAW;;cACpC,CAAC,MAAI,CAAC9+B,UAAL,CAAgBtF,IAAhB,CAAqBmmC,EAA1B,EAA8B;YAC5B,MAAI,CAAC7gC,UAAL,CAAgBtF,IAAhB,CAAqBmmC,EAArB,GAA0BhC,OAA1B;;;cAGE,MAAI,CAAC7+B,UAAL,CAAgBtF,IAAhB,CAAqBmmC,EAArB,KAA4BhC,OAAhC,EAAyC;YACvC,MAAI,CAAC7+B,UAAL,CAAgBtF,IAAhB,CAAqBimC,CAArB,CAAuBnpC,IAAvB,CAA4BsnC,IAA5B;WADF,MAEO;YACL,MAAI,CAAC9+B,UAAL,CAAgBtF,IAAhB,CAAqBimC,CAArB,CAAuBnpC,IAAvB,CAA4B;cAC1ByI,IAAI,EAAE,KADoB;cAE1B4gC,EAAE,EAAEhC,OAFsB;cAG1BiC,IAAI,EAAEhC;aAHR;;SARJ;;;;;;;;IC1LAiC;;;;;;;;;;;;;iCACS9pC,GAAGC,GAAG;aACVgb,QAAQ,CAACjb,CAAD,CAAR,GAAcib,QAAQ,CAAChb,CAAD,CAA7B;;;;gCAGU;aACH,MAAP;;;;gCAGUiK,GAAG;aACN+Q,QAAQ,CAAC/Q,CAAD,CAAf;;;;;EAVwB5K;;ACI5B,oBAAe;EAEbyqC,YAFa,wBAEAxqC,OAFA,EAES;SACfyqC,cAAL,GAAsB,EAAtB;;QAEIzqC,OAAO,CAAC0qC,MAAZ,EAAoB;WACbC,qBAAL,GAA6BzmC,IAA7B,CAAkC0mC,MAAlC,GAA2C,IAA3C;WACKC,iBAAL;;GAPS;EAWbC,WAXa,uBAWDxU,GAXC,EAWoB;QAAhBt2B,OAAgB,uEAAN,IAAM;;QAC3Bs2B,GAAG,KAAK,UAAR,IAAuBt2B,OAAO,IAAIA,OAAO,CAACsoC,IAA9C,EAAqD;UAC/CyC,OAAO,GAAG,CAAd;WACKzzB,IAAL,CAAUtN,QAAV,CAAmBuQ,OAAnB,CAA2B,UAACywB,OAAD,EAAa;YAClCD,OAAO,IAAIC,OAAO,CAACxC,aAAnB,IAAoCwC,OAAO,CAAC1U,GAAR,KAAgB,UAAxD,EAAoE;UAClEyU,OAAO;;OAFX;;aAKOA,OAAO,EAAd,EAAkB;aACXd,gBAAL;;;;QAIA,CAACjqC,OAAL,EAAc;WACPsX,IAAL,CAAUtN,QAAV,CAAmBhJ,IAAnB,CAAwB;QAAEs1B,GAAG,EAAHA;OAA1B;WACKnd,UAAL,YAAoBmd,GAApB;aACO,IAAP;;;SAGGhf,IAAL,CAAUtN,QAAV,CAAmBhJ,IAAnB,CAAwB;MAAEs1B,GAAG,EAAHA,GAAF;MAAOt2B,OAAO,EAAPA;KAA/B;QAEMwJ,UAAU,GAAG,EAAnB;;QAEI,OAAOxJ,OAAO,CAACsoC,IAAf,KAAwB,WAA5B,EAAyC;MACvC9+B,UAAU,CAAC8gC,IAAX,GAAkBtqC,OAAO,CAACsoC,IAA1B;;;QAEEhS,GAAG,KAAK,UAAZ,EAAwB;UAClB,OAAOt2B,OAAO,CAAC0oC,IAAf,KAAwB,QAA5B,EAAsC;QACpCl/B,UAAU,CAACC,IAAX,GAAkBzJ,OAAO,CAAC0oC,IAA1B;;;UAEElnC,KAAK,CAAC8B,OAAN,CAActD,OAAO,CAAC6c,IAAtB,CAAJ,EAAiC;QAC/BrT,UAAU,CAACkO,IAAX,GAAkB,CAAC1X,OAAO,CAAC6c,IAAR,CAAa,CAAb,CAAD,EAAkB,KAAKvF,IAAL,CAAUnO,MAAV,GAAmBnJ,OAAO,CAAC6c,IAAR,CAAa,CAAb,CAArC,EAChB7c,OAAO,CAAC6c,IAAR,CAAa,CAAb,CADgB,EACC,KAAKvF,IAAL,CAAUnO,MAAV,GAAmBnJ,OAAO,CAAC6c,IAAR,CAAa,CAAb,CADpB,CAAlB;;;UAGErb,KAAK,CAAC8B,OAAN,CAActD,OAAO,CAACirC,QAAtB,KACFjrC,OAAO,CAACirC,QAAR,CAAiBre,KAAjB,CAAuB,UAAAxsB,GAAG;eAAI,OAAOA,GAAP,KAAe,QAAnB;OAA1B,CADF,EAC0D;QACxDoJ,UAAU,CAAC0hC,QAAX,GAAsBlrC,OAAO,CAACirC,QAA9B;;;;QAGA3U,GAAG,KAAK,MAAZ,EAAoB;UACdt2B,OAAO,CAAC+oC,IAAZ,EAAkB;QAChBv/B,UAAU,CAACw/B,IAAX,GAAkB,IAAI7mC,MAAJ,CAAWnC,OAAO,CAAC+oC,IAAnB,CAAlB;;;UAEE/oC,OAAO,CAACipC,GAAZ,EAAiB;QACfz/B,UAAU,CAAC0/B,GAAX,GAAiB,IAAI/mC,MAAJ,CAAWnC,OAAO,CAACipC,GAAnB,CAAjB;;;UAEEjpC,OAAO,CAACsnC,QAAZ,EAAsB;QACpB99B,UAAU,CAAC2/B,CAAX,GAAe,IAAIhnC,MAAJ,CAAWnC,OAAO,CAACsnC,QAAnB,CAAf;;;UAEEtnC,OAAO,CAACopC,MAAZ,EAAoB;QAClB5/B,UAAU,CAAC6/B,UAAX,GAAwB,IAAIlnC,MAAJ,CAAWnC,OAAO,CAACopC,MAAnB,CAAxB;;;;SAICjwB,UAAL,YAAoBmd,GAApB,cAA2Br1B,SAAS,CAACC,OAAV,CAAkBsI,UAAlB,CAA3B;WACO,IAAP;GAlEW;EAqEbs0B,oBArEa,gCAqEQxH,GArER,EAqE2B;QAAdt2B,OAAc,uEAAJ,EAAI;QAChC2pC,iBAAiB,GAAG,KAAKC,mBAAL,GAA2BC,GAA3B,CAA+B,KAAKvyB,IAAL,CAAU6zB,mBAAzC,CAA1B;QACM7C,IAAI,GAAGqB,iBAAiB,CAAC9oC,MAA/B;IACA8oC,iBAAiB,CAAC3oC,IAAlB,CAAuB,IAAvB;SAEK8pC,WAAL,CAAiBxU,GAAjB,oCAA2Bt2B,OAA3B;MAAoCsoC,IAAI,EAAJA;;QAE9BE,aAAa,GAAG,IAAIJ,mBAAJ,CAAwB,KAAK9wB,IAAL,CAAU9N,UAAlC,EAA8C8+B,IAA9C,CAAtB;SACKhxB,IAAL,CAAUtN,QAAV,CAAmBvI,KAAnB,CAAyB,CAAC,CAA1B,EAA6B,CAA7B,EAAgC+mC,aAAhC,GAAgDA,aAAhD;WACOA,aAAP;GA9EW;EAiFbyB,gBAjFa,8BAiFM;SACZ3yB,IAAL,CAAUtN,QAAV,CAAmB4hB,GAAnB;SACKzS,UAAL,CAAgB,KAAhB;WACO,IAAP;GApFW;EAuFbykB,MAvFa,kBAuFN8K,IAvFM,EAuF+B;QAA/B1oC,OAA+B,uEAArB,EAAqB;QAAjBwnC,QAAiB,uEAAN,IAAM;WACnC,IAAIiB,mBAAJ,CAAwB,IAAxB,EAA8BC,IAA9B,EAAoC1oC,OAApC,EAA6CwnC,QAA7C,CAAP;GAxFW;EA2Fb/J,YA3Fa,wBA2FA2N,UA3FA,EA2FY;QACjBC,cAAc,GAAG,KAAKR,iBAAL,EAAvB;IACAO,UAAU,CAAC7B,SAAX,CAAqB8B,cAArB;IACAD,UAAU,CAAC5B,WAAX;SACKiB,cAAL,CAAoBzpC,IAApB,CAAyBoqC,UAAzB;;QACI,CAACC,cAAc,CAACnnC,IAAf,CAAoBimC,CAAzB,EAA4B;MAC1BkB,cAAc,CAACnnC,IAAf,CAAoBimC,CAApB,GAAwB,EAAxB;;;IAEFkB,cAAc,CAACnnC,IAAf,CAAoBimC,CAApB,CAAsBnpC,IAAtB,CAA2BoqC,UAAU,CAAC5hC,UAAtC;WACO,IAAP;GApGW;EAuGb8hC,gBAvGa,4BAuGIC,YAvGJ,EAuGkB;;;IAC7BA,YAAY,CAAChxB,OAAb,CAAqB,UAACywB,OAAD,EAAa;UAC5BA,OAAO,CAACxC,aAAZ,EAA2B;YACnBA,aAAa,GAAGwC,OAAO,CAACxC,aAA9B;;YACMgD,gBAAgB,GAAG,KAAI,CAAC1N,oBAAL,CAA0BkN,OAAO,CAAC1U,GAAlC,EAAuC0U,OAAO,CAAChrC,OAA/C,CAAzB;;QACAwoC,aAAa,CAACxnC,IAAd,CAAmBwqC,gBAAnB;QACA,KAAI,CAACl0B,IAAL,CAAUtN,QAAV,CAAmBvI,KAAnB,CAAyB,CAAC,CAA1B,EAA6B,CAA7B,EAAgC+mC,aAAhC,GAAgDA,aAAhD;OAJF,MAKO;QACL,KAAI,CAACsC,WAAL,CAAiBE,OAAO,CAAC1U,GAAzB,EAA8B0U,OAAO,CAAChrC,OAAtC;;KAPJ;GAxGW;EAoHbyrC,eApHa,2BAoHGn0B,IApHH,EAoHS;QACdi0B,YAAY,GAAGj0B,IAAI,CAACtN,QAA1B;IACAuhC,YAAY,CAAChxB,OAAb,CAAqB;aAAMjD,IAAI,CAAC5S,KAAL,CAAW,KAAX,CAAN;KAArB;IACA4S,IAAI,CAACtN,QAAL,GAAgB,EAAhB;WACOuhC,YAAP;GAxHW;EA2HbZ,qBA3Ha,mCA2HW;QAClB,CAAC,KAAKhhC,KAAL,CAAWzF,IAAX,CAAgBwnC,QAArB,EAA+B;WACxB/hC,KAAL,CAAWzF,IAAX,CAAgBwnC,QAAhB,GAA2B,KAAKriC,GAAL,CAAS,EAAT,CAA3B;;;WAEK,KAAKM,KAAL,CAAWzF,IAAX,CAAgBwnC,QAAvB;GA/HW;EAkIbb,iBAlIa,+BAkIO;QACd,CAAC,KAAKlhC,KAAL,CAAWzF,IAAX,CAAgBynC,cAArB,EAAqC;WAC9BhiC,KAAL,CAAWzF,IAAX,CAAgBynC,cAAhB,GAAiC,KAAKtiC,GAAL,CAAS;QACxCI,IAAI,EAAE,gBADkC;QAExCmiC,UAAU,EAAE,IAAIrB,aAAJ,EAF4B;QAGxCsB,iBAAiB,EAAE;OAHY,CAAjC;;;WAMK,KAAKliC,KAAL,CAAWzF,IAAX,CAAgBynC,cAAvB;GA1IW;EA6Ib/B,mBA7Ia,iCA6IS;WACb,KAAKiB,iBAAL,GAAyB3mC,IAAzB,CAA8B0nC,UAArC;GA9IW;EAiJbphC,6BAjJa,2CAiJmB;;SAEzBmgC,qBAAL;QAEMU,cAAc,GAAG,KAAKR,iBAAL,EAAvB;QACM1qC,GAAG,GAAGkrC,cAAc,CAACnnC,IAAf,CAAoB2nC,iBAApB,EAAZ;IACAR,cAAc,CAACnnC,IAAf,CAAoB0nC,UAApB,CAA+BjO,GAA/B,CAAmCx9B,GAAnC,EAAwC,EAAxC;WACOA,GAAP;GAxJW;EA2Jb2rC,WA3Ja,yBA2JC;QACNT,cAAc,GAAG,KAAK1hC,KAAL,CAAWzF,IAAX,CAAgBynC,cAAvC;;QACIN,cAAJ,EAAoB;MAClBA,cAAc,CAACrpC,GAAf;WACKyoC,cAAL,CAAoBlwB,OAApB,CAA4B,UAAC6wB,UAAD;eAAgBA,UAAU,CAACppC,GAAX,EAAhB;OAA5B;;;QAEE,KAAK2H,KAAL,CAAWzF,IAAX,CAAgBwnC,QAApB,EAA8B;WACvB/hC,KAAL,CAAWzF,IAAX,CAAgBwnC,QAAhB,CAAyB1pC,GAAzB;;;CAlKN;;ACVA,IAAM+pC,WAAW,GAAG;EAClBC,QAAQ,EAAE,CADQ;EAElB/P,QAAQ,EAAE,CAFQ;EAGlBgQ,QAAQ,EAAE,CAHQ;EAIlBC,SAAS,EAAE,MAJO;EAKlB73B,QAAQ,EAAE,MALQ;EAMlB83B,iBAAiB,EAAE,MAND;EAOlBC,WAAW,EAAE,MAPK;EAQlBC,UAAU,EAAE,OARM;EASlBC,KAAK,EAAE,OATW;EAUlBC,IAAI,EAAE,OAVY;EAWlB/rC,IAAI,EAAE,OAXY;EAYlBgsC,WAAW,EAAE,QAZK;EAalBC,OAAO,EAAE;CAbX;AAeA,IAAMC,aAAa,GAAG;EACpBnnC,IAAI,EAAE,CADc;EAEpBonC,MAAM,EAAE,CAFY;EAGpBlnC,KAAK,EAAE;CAHT;AAKA,IAAMmnC,SAAS,GAAG;EAAE/hC,KAAK,EAAE,GAAT;EAAcgiC,YAAY,EAAE;CAA9C;AACA,IAAMC,cAAc,GAAG;EACrBC,GAAG,EAAE,GADgB;EAErBC,QAAQ,EAAE,GAFW;EAGrBC,IAAI,EAAE,GAHe;EAIrBC,KAAK,EAAE,GAJc;EAKrBC,GAAG,EAAE;CALP;AAOA,IAAMC,cAAc,GAAG;EACrBzpC,MAAM,EAAE;IACN0pC,IAAI,EAAE,CADA;IAENC,QAAQ,EAAE,KAFJ;IAGNC,QAAQ,EAAE,YAHJ;IAINC,QAAQ,EAAE,EAJJ;IAKNC,eAAe,EAAE;GANE;EAQrBC,OAAO,EAAE;IACPL,IAAI,EAAE,CADC;IAEPC,QAAQ,EAAE;;CAVd;AAcA,oBAAe;;;;;EAKbK,QALa,sBAKF;QACL,CAAC,KAAKlU,KAAV,EAAiB;YACT,IAAI35B,KAAJ,CAAU,gDAAV,CAAN;;;SAEG8tC,SAAL,GAAiB;MACfhN,KAAK,EAAE,EADQ;MAEfvH,WAAW,EAAE,KAAKI,KAAL,CAAWld;KAF1B;SAIKqxB,SAAL,CAAehN,KAAf,CAAqB,KAAKnH,KAAL,CAAWx1B,EAAhC,IAAsC,KAAKw1B,KAAL,CAAWpwB,GAAX,EAAtC;QAEInF,IAAI,GAAG;MACT2pC,MAAM,EAAE,EADC;MAETC,eAAe,EAAE,IAFR;MAGTnH,EAAE,EAAE,IAAIxkC,MAAJ,YAAe,KAAKs3B,KAAL,CAAWx1B,EAA1B,eAHK;MAIT8pC,EAAE,EAAE;QACF9jC,IAAI,EAAE;;KALV;IAQA/F,IAAI,CAAC6pC,EAAL,CAAQ9jC,IAAR,CAAa,KAAKwvB,KAAL,CAAWx1B,EAAxB,IAA8B,KAAKw1B,KAAL,CAAWpwB,GAAX,EAA9B;QACM2kC,QAAQ,GAAG,KAAK3kC,GAAL,CAASnF,IAAT,CAAjB;SACKyF,KAAL,CAAWzF,IAAX,CAAgB8pC,QAAhB,GAA2BA,QAA3B;WACO,IAAP;GA1BW;;;;;EAgCbC,WAhCa,yBAgCC;;;QACR,KAAKtkC,KAAL,CAAWzF,IAAX,CAAgB8pC,QAApB,EAA8B;UAE1B,CAAC1tC,MAAM,CAACC,IAAP,CAAY,KAAKqtC,SAAL,CAAehN,KAA3B,EAAkC//B,MAAnC,IACA,CAAC,KAAK+sC,SAAL,CAAevU,WAFlB,EAGE;cACM,IAAIv5B,KAAJ,CAAU,iCAAV,CAAN;;;UAEEouC,QAAQ,GAAG,KAAKvkC,KAAL,CAAWzF,IAAX,CAAgB8pC,QAAhB,CAAyB9pC,IAAzB,CAA8B6pC,EAA9B,CAAiC9jC,IAAhD;MACA3J,MAAM,CAACC,IAAP,CAAY,KAAKqtC,SAAL,CAAehN,KAA3B,EAAkCrmB,OAAlC,CAA0C,UAAAgC,IAAI,EAAI;QAChD2xB,QAAQ,CAAC3xB,IAAD,CAAR,GAAiB,KAAI,CAACqxB,SAAL,CAAehN,KAAf,CAAqBrkB,IAArB,CAAjB;OADF;;WAGK5S,KAAL,CAAWzF,IAAX,CAAgB8pC,QAAhB,CAAyB9pC,IAAzB,CAA8B2pC,MAA9B,CAAqCtzB,OAArC,CAA6C,UAAA4zB,QAAQ,EAAI;QACvD,KAAI,CAACC,SAAL,CAAeD,QAAf;OADF;;WAGKxkC,KAAL,CAAWzF,IAAX,CAAgB8pC,QAAhB,CAAyBhsC,GAAzB;;;WAEK,IAAP;GAjDW;EAoDbosC,SApDa,qBAoDH/kC,GApDG,EAoDE;;;QACT7H,KAAK,CAAC8B,OAAN,CAAc+F,GAAG,CAACnF,IAAJ,CAASgiC,IAAvB,CAAJ,EAAkC;MAChC78B,GAAG,CAACnF,IAAJ,CAASgiC,IAAT,CAAc3rB,OAAd,CAAsB,UAAA8zB,QAAQ,EAAI;QAChC,MAAI,CAACD,SAAL,CAAeC,QAAf;OADF;MAGAhlC,GAAG,CAACrH,GAAJ;;;WAEK,IAAP;GA3DW;;;;;;;;;EAqEbssC,SArEa,qBAqEH/xB,IArEG,EAqEiB;QAAdvc,OAAc,uEAAJ,EAAI;;QACxBuuC,SAAS,GAAG,KAAKC,UAAL,CAAgBjyB,IAAhB,EAAsB,IAAtB,EAA4Bvc,OAA5B,CAAhB;;QACImuC,QAAQ,GAAG,KAAK9kC,GAAL,CAASklC,SAAT,CAAf;;SACKE,YAAL,CAAkBN,QAAlB;;WACOA,QAAP;GAzEW;;;;;;;;;;;;;EAuFbO,cAvFa,0BAuFEnyB,IAvFF,EAuFQmsB,IAvFR,EAuFcn8B,CAvFd,EAuFiB+b,CAvFjB,EAuFoBwD,CAvFpB,EAuFuBnF,CAvFvB,EAuFwC;QAAd3mB,OAAc,uEAAJ,EAAI;;QAC/CuuC,SAAS,GAAG,KAAKC,UAAL,CAAgBjyB,IAAhB,EAAsBmsB,IAAtB,EAA4B1oC,OAA5B,CAAhB;;IACAuuC,SAAS,CAAC/2B,OAAV,GAAoB,QAApB;;QACI+2B,SAAS,CAAC7I,CAAV,KAAgB3P,SAApB,EAA+B;MAC7BwY,SAAS,CAAC7I,CAAV,GAAc,CAAd,CAD6B;KAHoB;;;SAQ9CJ,QAAL,CAAc/4B,CAAd,EAAiB+b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B4nB,SAA1B;QACII,QAAQ,GAAG,KAAKr3B,IAAL,CAAUsuB,WAAV,CAAsB,KAAKtuB,IAAL,CAAUsuB,WAAV,CAAsB/kC,MAAtB,GAA+B,CAArD,CAAf;WAEO,KAAK4tC,YAAL,CAAkBE,QAAlB,CAAP;GAlGW;EAqGbC,QArGa,oBAqGJryB,IArGI,EAqGEhQ,CArGF,EAqGK+b,CArGL,EAqGQwD,CArGR,EAqGWnF,CArGX,EAqG4B;QAAd3mB,OAAc,uEAAJ,EAAI;WAChC,KAAK0uC,cAAL,CAAoBnyB,IAApB,EAA0B,MAA1B,EAAkChQ,CAAlC,EAAqC+b,CAArC,EAAwCwD,CAAxC,EAA2CnF,CAA3C,EAA8C3mB,OAA9C,CAAP;GAtGW;EAyGb6uC,cAzGa,0BAyGEtyB,IAzGF,EAyGQhQ,CAzGR,EAyGW+b,CAzGX,EAyGcwD,CAzGd,EAyGiBnF,CAzGjB,EAyGkC;QAAd3mB,OAAc,uEAAJ,EAAI;WACtC,KAAK0uC,cAAL,CAAoBnyB,IAApB,EAA0B,YAA1B,EAAwChQ,CAAxC,EAA2C+b,CAA3C,EAA8CwD,CAA9C,EAAiDnF,CAAjD,EAAoD3mB,OAApD,CAAP;GA1GW;EA6Gb8uC,SA7Ga,qBA6GHvyB,IA7GG,EA6GGhQ,CA7GH,EA6GM+b,CA7GN,EA6GSwD,CA7GT,EA6GYnF,CA7GZ,EA6G6B;QAAd3mB,OAAc,uEAAJ,EAAI;WACjC,KAAK0uC,cAAL,CAAoBnyB,IAApB,EAA0B,OAA1B,EAAmChQ,CAAnC,EAAsC+b,CAAtC,EAAyCwD,CAAzC,EAA4CnF,CAA5C,EAA+C3mB,OAA/C,CAAP;GA9GW;EAiHb+uC,QAjHa,oBAiHJxyB,IAjHI,EAiHEhQ,CAjHF,EAiHK+b,CAjHL,EAiHQwD,CAjHR,EAiHWnF,CAjHX,EAiH4B;QAAd3mB,OAAc,uEAAJ,EAAI;WAChC,KAAK0uC,cAAL,CAAoBnyB,IAApB,EAA0B,MAA1B,EAAkChQ,CAAlC,EAAqC+b,CAArC,EAAwCwD,CAAxC,EAA2CnF,CAA3C,EAA8C3mB,OAA9C,CAAP;GAlHW;EAqHbgvC,eArHa,2BAqHGzyB,IArHH,EAqHShQ,CArHT,EAqHY+b,CArHZ,EAqHewD,CArHf,EAqHkBnF,CArHlB,EAqHmC;QAAd3mB,OAAc,uEAAJ,EAAI;WACvC,KAAK0uC,cAAL,CAAoBnyB,IAApB,EAA0B,aAA1B,EAAyChQ,CAAzC,EAA4C+b,CAA5C,EAA+CwD,CAA/C,EAAkDnF,CAAlD,EAAqD3mB,OAArD,CAAP;GAtHW;EAyHbivC,YAzHa,wBAyHA1yB,IAzHA,EAyHMhQ,CAzHN,EAyHS+b,CAzHT,EAyHYwD,CAzHZ,EAyHenF,CAzHf,EAyHgC;QAAd3mB,OAAc,uEAAJ,EAAI;WACpC,KAAK0uC,cAAL,CAAoBnyB,IAApB,EAA0B,UAA1B,EAAsChQ,CAAtC,EAAyC+b,CAAzC,EAA4CwD,CAA5C,EAA+CnF,CAA/C,EAAkD3mB,OAAlD,CAAP;GA1HW;EA6HbyuC,YA7Ha,wBA6HAN,QA7HA,EA6HU;QACjBhH,MAAM,GAAGgH,QAAQ,CAACjqC,IAAT,CAAcwF,MAA3B;;QACIy9B,MAAJ,EAAY;UACN,CAACA,MAAM,CAACjjC,IAAP,CAAYgiC,IAAjB,EAAuB;QACrBiB,MAAM,CAACjjC,IAAP,CAAYgiC,IAAZ,GAAmB,EAAnB;;;MAEFiB,MAAM,CAACjjC,IAAP,CAAYgiC,IAAZ,CAAiBllC,IAAjB,CAAsBmtC,QAAtB;KAJF,MAKO;WACAxkC,KAAL,CAAWzF,IAAX,CAAgB8pC,QAAhB,CAAyB9pC,IAAzB,CAA8B2pC,MAA9B,CAAqC7sC,IAArC,CAA0CmtC,QAA1C;;;WAEK,IAAP;GAvIW;EA0IbK,UA1Ia,sBA0IFjyB,IA1IE,EA0IImsB,IA1IJ,EA0IwB;QAAd1oC,OAAc,uEAAJ,EAAI;;QAC/B,CAAC,KAAK4tC,SAAV,EAAqB;YACb,IAAI9tC,KAAJ,CACJ,0EADI,CAAN;;;QAIEgN,IAAI,GAAGxM,MAAM,CAACu/B,MAAP,CAAc,EAAd,EAAkB7/B,OAAlB,CAAX;;QACI0oC,IAAI,KAAK,IAAb,EAAmB;MACjB57B,IAAI,GAAG,KAAKoiC,YAAL,CAAkBxG,IAAlB,EAAwB1oC,OAAxB,CAAP;;;IAEF8M,IAAI,GAAG,KAAKqiC,aAAL,CAAmBriC,IAAnB,CAAP;IACAA,IAAI,GAAG,KAAKsiC,eAAL,CAAqBtiC,IAArB,CAAP;IACAA,IAAI,GAAG,KAAKuiC,YAAL,CAAkBviC,IAAlB,CAAP;IACAA,IAAI,GAAG,KAAKwiC,eAAL,CAAqBxiC,IAArB,CAAP;IACAA,IAAI,GAAG,KAAKyiC,cAAL,CAAoBziC,IAApB,CAAP;IACAA,IAAI,GAAG,KAAK0iC,cAAL,CAAoB1iC,IAApB,CAAP;IACAA,IAAI,CAACma,CAAL,GAAS,IAAI9kB,MAAJ,CAAWoa,IAAX,CAAT;;QACIzP,IAAI,CAACq6B,MAAT,EAAiB;MACfr6B,IAAI,CAACpD,MAAL,GAAcoD,IAAI,CAACq6B,MAAnB;aACOr6B,IAAI,CAACq6B,MAAZ;;;WAEKr6B,IAAP;GA/JW;EAkKboiC,YAlKa,wBAkKAxG,IAlKA,EAkKM57B,IAlKN,EAkKY;QACnB47B,IAAI,KAAK,MAAb,EAAqB;MACnB57B,IAAI,CAAC2iC,EAAL,GAAU,IAAV;KADF,MAEO,IAAI/G,IAAI,KAAK,YAAb,EAA2B;MAChC57B,IAAI,CAAC2iC,EAAL,GAAU,KAAV;MACA3iC,IAAI,CAACu/B,UAAL,GAAkB,IAAlB;KAFK,MAGA,IAAI3D,IAAI,KAAK,aAAb,EAA4B;MACjC57B,IAAI,CAAC2iC,EAAL,GAAU,KAAV;MACA3iC,IAAI,CAACs/B,WAAL,GAAmB,IAAnB;KAFK,MAGA,IAAI1D,IAAI,KAAK,UAAb,EAAyB;MAC9B57B,IAAI,CAAC2iC,EAAL,GAAU,KAAV;KADK,MAEA,IAAI/G,IAAI,KAAK,OAAb,EAAsB;MAC3B57B,IAAI,CAAC2iC,EAAL,GAAU,IAAV;MACA3iC,IAAI,CAACw/B,KAAL,GAAa,IAAb;KAFK,MAGA,IAAI5D,IAAI,KAAK,MAAb,EAAqB;MAC1B57B,IAAI,CAAC2iC,EAAL,GAAU,IAAV;KADK,MAEA;YACC,IAAI3vC,KAAJ,yCAA2C4oC,IAA3C,OAAN;;;WAEK57B,IAAP;GArLW;EAwLb0iC,cAxLa,0BAwLE1iC,IAxLF,EAwLQ;QACb4iC,CAAC,GAAG5iC,IAAI,CAAC6iC,MAAf;;QACID,CAAC,IAAIA,CAAC,CAAChH,IAAX,EAAiB;UACXkH,WAAJ;UACIC,QAAJ;UACIjoB,MAAM,GAAG,EAAb;;UACIklB,cAAc,CAAC4C,CAAC,CAAChH,IAAH,CAAd,KAA2B3S,SAA/B,EAA0C;QACxC6Z,WAAW,wBAAX;QACAC,QAAQ,qBAAR;QACAjoB,MAAM,GAAGklB,cAAc,CAAC4C,CAAC,CAAChH,IAAH,CAAvB;OAHF,MAIO;YACDiH,MAAM,GAAGD,CAAC,CAAChH,IAAF,CAAOltB,MAAP,CAAc,CAAd,EAAiBvS,WAAjB,KAAiCymC,CAAC,CAAChH,IAAF,CAAOjnC,KAAP,CAAa,CAAb,CAA9C;QACAmuC,WAAW,eAAQD,MAAR,eAAX;QACAE,QAAQ,eAAQF,MAAR,YAAR;;YAEID,CAAC,CAAChH,IAAF,KAAW,MAAf,EAAuB;UACrBkH,WAAW,IAAI,IAAf;UACAhoB,MAAM,GAAGzlB,MAAM,CAACutC,CAAC,CAACI,KAAH,CAAf;SAFF,MAGO,IAAIJ,CAAC,CAAChH,IAAF,KAAW,MAAf,EAAuB;UAC5B9gB,MAAM,GAAGzlB,MAAM,CAACutC,CAAC,CAACI,KAAH,CAAf;SADK,MAEA,IAAIJ,CAAC,CAAChH,IAAF,KAAW,QAAf,EAAyB;cAC1B9E,CAAC,GAAGtjC,MAAM,CAACu/B,MAAP,CAAc,EAAd,EAAkBuN,cAAc,CAACzpC,MAAjC,EAAyC+rC,CAAzC,CAAR;UACA9nB,MAAM,GAAGzlB,MAAM,CACb,CACEA,MAAM,CAACyhC,CAAC,CAACyJ,IAAH,CADR,EAEEzJ,CAAC,CAAC0J,QAAF,GAAa,GAAb,GAAmB,GAFrB,EAGE,MAAM1J,CAAC,CAAC2J,QAAR,GAAmB,GAHrB,EAIE,MAJF,EAKE,MAAM3J,CAAC,CAAC4J,QAAR,GAAmB,GALrB,EAMErrC,MAAM,CAACyhC,CAAC,CAAC6J,eAAH,CANR,EAOEpsC,IAPF,CAOO,GAPP,CADa,CAAf;SAFK,MAYA,IAAIquC,CAAC,CAAChH,IAAF,KAAW,SAAf,EAA0B;cAC3B9E,EAAC,GAAGtjC,MAAM,CAACu/B,MAAP,CAAc,EAAd,EAAkBuN,cAAc,CAACM,OAAjC,EAA0CgC,CAA1C,CAAR;;UACA9nB,MAAM,GAAGzlB,MAAM,CAAC,CAACA,MAAM,CAACyhC,EAAC,CAACyJ,IAAH,CAAP,EAAiBzJ,EAAC,CAAC0J,QAAF,GAAa,GAAb,GAAmB,GAApC,EAAyCjsC,IAAzC,CAA8C,GAA9C,CAAD,CAAf;;;;MAGJyL,IAAI,CAACijC,EAAL,GAAUjjC,IAAI,CAACijC,EAAL,GAAUjjC,IAAI,CAACijC,EAAf,GAAoB,EAA9B;MACAjjC,IAAI,CAACijC,EAAL,CAAQ5F,CAAR,GAAY;QACVvyB,CAAC,EAAE,YADO;QAEVo4B,EAAE,EAAE,IAAI7tC,MAAJ,WAAcytC,WAAd,cAA6BhoB,MAA7B;OAFN;MAIA9a,IAAI,CAACijC,EAAL,CAAQrK,CAAR,GAAY;QACV9tB,CAAC,EAAE,YADO;QAEVo4B,EAAE,EAAE,IAAI7tC,MAAJ,WAAc0tC,QAAd,cAA0BjoB,MAA1B;OAFN;;;WAKK9a,IAAI,CAAC6iC,MAAZ;WACO7iC,IAAP;GAxOW;EA2ObyiC,cA3Oa,0BA2OEziC,IA3OF,EA2OQ;QACfsI,KAAK,GAAG,KAAKE,eAAL,CAAqBxI,IAAI,CAACmjC,eAA1B,CAAZ;;QACI76B,KAAJ,EAAW;UACL,CAACtI,IAAI,CAACojC,EAAV,EAAc;QACZpjC,IAAI,CAACojC,EAAL,GAAU,EAAV;;;MAEFpjC,IAAI,CAACojC,EAAL,CAAQC,EAAR,GAAa/6B,KAAb;;;IAEFA,KAAK,GAAG,KAAKE,eAAL,CAAqBxI,IAAI,CAACsjC,WAA1B,CAAR;;QACIh7B,KAAJ,EAAW;UACL,CAACtI,IAAI,CAACojC,EAAV,EAAc;QACZpjC,IAAI,CAACojC,EAAL,GAAU,EAAV;;;MAEFpjC,IAAI,CAACojC,EAAL,CAAQG,EAAR,GAAaj7B,KAAb;;;WAEKtI,IAAI,CAACmjC,eAAZ;WACOnjC,IAAI,CAACsjC,WAAZ;WACOtjC,IAAP;GA5PW;EA+PbqiC,aA/Pa,yBA+PCnvC,OA/PD,EA+PU;QACjBgrB,MAAM,GAAG,CAAb;IACA1qB,MAAM,CAACC,IAAP,CAAYP,OAAZ,EAAqBua,OAArB,CAA6B,UAAApa,GAAG,EAAI;UAC9B4rC,WAAW,CAAC5rC,GAAD,CAAf,EAAsB;YAChBH,OAAO,CAACG,GAAD,CAAX,EAAkB;UAChB6qB,MAAM,IAAI+gB,WAAW,CAAC5rC,GAAD,CAArB;;;eAEKH,OAAO,CAACG,GAAD,CAAd;;KALJ;;QAQI6qB,MAAM,KAAK,CAAf,EAAkB;MAChBhrB,OAAO,CAACswC,EAAR,GAAatwC,OAAO,CAACswC,EAAR,GAAatwC,OAAO,CAACswC,EAArB,GAA0B,CAAvC;MACAtwC,OAAO,CAACswC,EAAR,IAActlB,MAAd;;;WAEKhrB,OAAP;GA7QW;EAgRbovC,eAhRa,2BAgRGpvC,OAhRH,EAgRY;QACnBgrB,MAAM,GAAG,CAAb;;QACIhrB,OAAO,CAACi7B,KAAR,KAAkBlF,SAAtB,EAAiC;UAC3B,OAAO2W,aAAa,CAAC1sC,OAAO,CAACi7B,KAAT,CAApB,KAAwC,QAA5C,EAAsD;QACpDjQ,MAAM,GAAG0hB,aAAa,CAAC1sC,OAAO,CAACi7B,KAAT,CAAtB;;;aAEKj7B,OAAO,CAACi7B,KAAf;;;QAEEjQ,MAAM,KAAK,CAAf,EAAkB;MAChBhrB,OAAO,CAAC8mB,CAAR,GAAYkE,MAAZ,CADgB;;;WAGXhrB,OAAP;GA3RW;EA8RbqvC,YA9Ra,wBA8RArvC,OA9RA,EA8RS;;QAEhB,KAAK4tC,SAAL,CAAehN,KAAf,CAAqB,KAAKnH,KAAL,CAAWx1B,EAAhC,MAAwC,IAA5C,EAAkD;WAC3C2pC,SAAL,CAAehN,KAAf,CAAqB,KAAKnH,KAAL,CAAWx1B,EAAhC,IAAsC,KAAKw1B,KAAL,CAAWpwB,GAAX,EAAtC;KAHkB;;;QAOhB,KAAKukC,SAAL,CAAevU,WAAf,KAA+B,KAAKI,KAAL,CAAWld,IAA9C,EAAoD;MAClDvc,OAAO,CAAC+tC,EAAR,GAAa;QAAE9jC,IAAI,EAAE;OAArB,CADkD;;UAI5C2vB,QAAQ,GAAG55B,OAAO,CAAC45B,QAAR,IAAoB,CAArC;MAEA55B,OAAO,CAAC+tC,EAAR,CAAW9jC,IAAX,CAAgB,KAAKwvB,KAAL,CAAWx1B,EAA3B,IAAiC,KAAKw1B,KAAL,CAAWpwB,GAAX,EAAjC;MACArJ,OAAO,CAAC2mC,EAAR,GAAa,IAAIxkC,MAAJ,YAAe,KAAKs3B,KAAL,CAAWx1B,EAA1B,cAAgC21B,QAAhC,aAAb;;;WAEK55B,OAAP;GA9SW;EAiTbsvC,eAjTa,2BAiTGtvC,OAjTH,EAiTY;QACnBuwC,MAAM,GAAG,EAAb;;aACSC,aAAT,CAAuB/vC,CAAvB,EAA0B;UACpBe,KAAK,CAAC8B,OAAN,CAAc7C,CAAd,CAAJ,EAAsB;aACf,IAAIgwC,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAGhwC,CAAC,CAACI,MAA1B,EAAkC4vC,GAAG,EAArC,EAAyC;cACnC,OAAOhwC,CAAC,CAACgwC,GAAD,CAAR,KAAkB,QAAtB,EAAgC;YAC9BF,MAAM,CAACvvC,IAAP,CAAY,IAAImB,MAAJ,CAAW1B,CAAC,CAACgwC,GAAD,CAAZ,CAAZ;WADF,MAEO;YACLF,MAAM,CAACvvC,IAAP,CAAYP,CAAC,CAACgwC,GAAD,CAAb;;;;;;IAKRD,aAAa,CAACxwC,OAAO,CAAC0wC,GAAT,CAAb;;QACI1wC,OAAO,CAACuwC,MAAZ,EAAoB;MAClBC,aAAa,CAACxwC,OAAO,CAACuwC,MAAT,CAAb;aACOvwC,OAAO,CAACuwC,MAAf;;;QAEEA,MAAM,CAAC1vC,MAAX,EAAmB;MACjBb,OAAO,CAAC0wC,GAAR,GAAcH,MAAd;;;IAGFjwC,MAAM,CAACC,IAAP,CAAYqsC,SAAZ,EAAuBryB,OAAvB,CAA+B,UAAApa,GAAG,EAAI;UAChCH,OAAO,CAACG,GAAD,CAAP,KAAiB41B,SAArB,EAAgC;QAC9B/1B,OAAO,CAAC4sC,SAAS,CAACzsC,GAAD,CAAV,CAAP,GAA0BH,OAAO,CAACG,GAAD,CAAjC;eACOH,OAAO,CAACG,GAAD,CAAd;;KAHJ;KAMC,GAAD,EAAM,IAAN,EAAYoa,OAAZ,CAAoB,UAAApa,GAAG,EAAI;UACrB,OAAOH,OAAO,CAACG,GAAD,CAAd,KAAwB,QAA5B,EAAsC;QACpCH,OAAO,CAACG,GAAD,CAAP,GAAe,IAAIgC,MAAJ,CAAWnC,OAAO,CAACG,GAAD,CAAlB,CAAf;;KAFJ;;QAMIH,OAAO,CAACkwC,EAAR,IAAclwC,OAAO,CAACkwC,EAAR,CAAWzzB,EAA7B,EAAiC;MAC/Bzc,OAAO,CAACkwC,EAAR,CAAWzzB,EAAX,GAAgB,IAAIta,MAAJ,CAAWnC,OAAO,CAACkwC,EAAR,CAAWzzB,EAAtB,CAAhB;;;QAEEzc,OAAO,CAACk/B,KAAZ,EAAmB;MACjBl/B,OAAO,CAACkwC,EAAR,GAAalwC,OAAO,CAACkwC,EAAR,GAAalwC,OAAO,CAACkwC,EAArB,GAA0B,EAAvC;MACAlwC,OAAO,CAACkwC,EAAR,CAAWzzB,EAAX,GAAgB,IAAIta,MAAJ,CAAWnC,OAAO,CAACk/B,KAAnB,CAAhB;aACOl/B,OAAO,CAACk/B,KAAf;;;WAEKl/B,OAAP;;CA3VJ;;ACvCA,uBAAe;;;;;;;;;;;;;EAab6mC,IAba,gBAaR/N,GAbQ,EAaW;QAAd94B,OAAc,uEAAJ,EAAI;IACtBA,OAAO,CAACuc,IAAR,GAAevc,OAAO,CAACuc,IAAR,IAAgBuc,GAA/B;QAEM6X,OAAO,GAAG;MACdlnC,IAAI,EAAE,cADQ;MAEdmnC,MAAM,EAAE;KAFV;QAII1sC,IAAJ;;QAEI,CAAC40B,GAAL,EAAU;YACF,IAAIh5B,KAAJ,CAAU,kBAAV,CAAN;;;QAEE0C,MAAM,CAACM,QAAP,CAAgBg2B,GAAhB,CAAJ,EAA0B;MACxB50B,IAAI,GAAG40B,GAAP;KADF,MAEO,IAAIA,GAAG,YAAYK,WAAnB,EAAgC;MACrCj1B,IAAI,GAAG1B,MAAM,CAACC,IAAP,CAAY,IAAIy2B,UAAJ,CAAeJ,GAAf,CAAZ,CAAP;KADK,MAEA;UACDlH,KAAJ;;UACKA,KAAK,GAAG,2BAA2BsS,IAA3B,CAAgCpL,GAAhC,CAAb,EAAoD;YAC9ClH,KAAK,CAAC,CAAD,CAAT,EAAc;UACZ+e,OAAO,CAACn5B,OAAR,GAAkBoa,KAAK,CAAC,CAAD,CAAL,CAAShvB,OAAT,CAAiB,GAAjB,EAAsB,KAAtB,CAAlB;;;QAEFsB,IAAI,GAAG1B,MAAM,CAACC,IAAP,CAAYmvB,KAAK,CAAC,CAAD,CAAjB,EAAsB,QAAtB,CAAP;OAJF,MAKO;QACL1tB,IAAI,GAAG0sB,EAAE,CAACC,YAAH,CAAgBiI,GAAhB,CAAP;;YACI,CAAC50B,IAAL,EAAW;gBACH,IAAIpE,KAAJ,uDAAyDg5B,GAAzD,EAAN;SAHG;;;2BAOwBlI,EAAE,CAACigB,QAAH,CAAY/X,GAAZ,CAPxB;YAOGgY,SAPH,gBAOGA,SAPH;YAOcC,KAPd,gBAOcA,KAPd;;QAQLJ,OAAO,CAACC,MAAR,CAAe1iC,YAAf,GAA8B4iC,SAA9B;QACAH,OAAO,CAACC,MAAR,CAAeI,OAAf,GAAyBD,KAAzB;;KAhCkB;;;QAqClB/wC,OAAO,CAACixC,YAAR,YAAgCluC,IAApC,EAA0C;MACxC4tC,OAAO,CAACC,MAAR,CAAe1iC,YAAf,GAA8BlO,OAAO,CAACixC,YAAtC;;;QAEEjxC,OAAO,CAACkxC,YAAR,YAAgCnuC,IAApC,EAA0C;MACxC4tC,OAAO,CAACC,MAAR,CAAeI,OAAf,GAAyBhxC,OAAO,CAACkxC,YAAjC;KAzCoB;;;QA4ClBlxC,OAAO,CAAC0oC,IAAZ,EAAkB;MAChBiI,OAAO,CAACn5B,OAAR,GAAkBxX,OAAO,CAAC0oC,IAAR,CAAa9lC,OAAb,CAAqB,GAArB,EAA0B,KAA1B,CAAlB;KA7CoB;;;QAiDhBuuC,QAAQ,GAAG7iC,QAAQ,CAACC,GAAT,CACfD,QAAQ,CAACG,GAAT,CAAaC,SAAb,CAAuB6C,MAAvB,CAA8B,IAAI2nB,UAAJ,CAAeh1B,IAAf,CAA9B,CADe,CAAjB;IAGAysC,OAAO,CAACC,MAAR,CAAeQ,QAAf,GAA0B,IAAIjvC,MAAJ,CAAWgvC,QAAX,CAA1B;IACAR,OAAO,CAACC,MAAR,CAAeS,IAAf,GAAsBntC,IAAI,CAACotC,UAA3B,CArDsB;;;QAyDlBjoC,GAAJ;QACI,CAAC,KAAKkoC,aAAV,EAAyB,KAAKA,aAAL,GAAqB,EAArB;QACrB1K,IAAI,GAAG,KAAK0K,aAAL,CAAmBvxC,OAAO,CAACuc,IAA3B,CAAX;;QACIsqB,IAAI,IAAI2K,OAAO,CAACb,OAAD,EAAU9J,IAAV,CAAnB,EAAoC;MAClCx9B,GAAG,GAAGw9B,IAAI,CAACx9B,GAAX;KADF,MAEO;MACLA,GAAG,GAAG,KAAKA,GAAL,CAASsnC,OAAT,CAAN;MACAtnC,GAAG,CAACrH,GAAJ,CAAQkC,IAAR;WAEKqtC,aAAL,CAAmBvxC,OAAO,CAACuc,IAA3B,sCAAwCo0B,OAAxC;QAAiDtnC,GAAG,EAAHA;;KAlE7B;;;QAqEhBooC,YAAY,GAAG;MACnBhoC,IAAI,EAAE,UADa;MAEnBi8B,CAAC,EAAE,IAAIvjC,MAAJ,CAAWnC,OAAO,CAACuc,IAAnB,CAFgB;MAGnBm1B,EAAE,EAAE;QAAEhM,CAAC,EAAEr8B;OAHU;MAInBsoC,EAAE,EAAE,IAAIxvC,MAAJ,CAAWnC,OAAO,CAACuc,IAAnB;KAJN;;QAMIvc,OAAO,CAAC4xC,WAAZ,EAAyB;MACvBH,YAAY,CAACxK,IAAb,GAAoB,IAAI9kC,MAAJ,CAAWnC,OAAO,CAAC4xC,WAAnB,CAApB;;;QAEI9K,QAAQ,GAAG,KAAKz9B,GAAL,CAASooC,YAAT,CAAjB;IACA3K,QAAQ,CAAC9kC,GAAT;;QAEI,CAAChC,OAAO,CAAC+mC,MAAb,EAAqB;WACd8K,oBAAL,CAA0B7xC,OAAO,CAACuc,IAAlC,EAAwCuqB,QAAxC;;;WAGKA,QAAP;;CAlGJ;;;AAuGA,SAAS0K,OAAT,CAAiB/wC,CAAjB,EAAoBC,CAApB,EAAuB;SAEnBD,CAAC,CAAC+W,OAAF,KAAc9W,CAAC,CAAC8W,OAAhB,IACA/W,CAAC,CAACmwC,MAAF,CAASQ,QAAT,CAAkBzuC,QAAlB,OAAiCjC,CAAC,CAACkwC,MAAF,CAASQ,QAAT,CAAkBzuC,QAAlB,EADjC,IAEAlC,CAAC,CAACmwC,MAAF,CAASS,IAAT,KAAkB3wC,CAAC,CAACkwC,MAAF,CAASS,IAF3B,IAGA5wC,CAAC,CAACmwC,MAAF,CAAS1iC,YAAT,KAA0BxN,CAAC,CAACkwC,MAAF,CAAS1iC,YAHnC,IAIAzN,CAAC,CAACmwC,MAAF,CAASI,OAAT,KAAqBtwC,CAAC,CAACkwC,MAAF,CAASI,OALhC;;;ACzGF,WAAe;EAEXc,QAFW,oBAEFC,OAFE,EAEO;QACVA,OAAO,CAACv2B,MAAR,CAAeu2B,OAAO,CAAClxC,MAAR,GAAiB,CAAhC,MAAuC,GAA3C,EAAgD;WACvCmxC,kBAAL,GAA0BD,OAAO,CAACv2B,MAAR,CAAeu2B,OAAO,CAAClxC,MAAR,GAAiB,CAAhC,EAAmCoI,WAAnC,EAA1B;WACKirB,MAAL,GAAcxY,QAAQ,CAACq2B,OAAO,CAACv2B,MAAR,CAAeu2B,OAAO,CAAClxC,MAAR,GAAiB,CAAhC,CAAD,CAAtB;KAFJ,MAGO;;WAEEmxC,kBAAL,GAA0B,GAA1B;WACK9d,MAAL,GAAcxY,QAAQ,CAACq2B,OAAO,CAACv2B,MAAR,CAAeu2B,OAAO,CAAClxC,MAAR,GAAiB,CAAhC,CAAD,CAAtB;;GATG;EAaXoxC,SAbW,uBAaC;SACHC,gBAAL;;QACMC,MAAM,aAAMvf,SAAN,gCAAZ;QACMwf,QAAQ,aAAMxf,SAAN,6CAAd;;SACKyf,qBAAL,CAA2BzhB,EAAE,CAAC0hB,UAAH,CAAcH,MAAd,IAAwBA,MAAxB,GAAiCC,QAA5D;GAjBO;EAoBXC,qBApBW,iCAoBWE,QApBX,EAoBqB;QACtBC,UAAU,GAAG5hB,EAAE,CAACC,YAAH,CAAgB0hB,QAAhB,CAAnB;QAEME,eAAe,GAAG,KAAKppC,GAAL,CAAS;MAC7B5E,MAAM,EAAE+tC,UAAU,CAAC3xC,MADU;MAE7B0V,CAAC,EAAE;KAFiB,CAAxB;IAIAk8B,eAAe,CAAC/tC,KAAhB,CAAsB8tC,UAAtB;IACAC,eAAe,CAACzwC,GAAhB;QAEM0wC,SAAS,GAAG,KAAKrpC,GAAL,CAAS;MACvBI,IAAI,EAAE,cADiB;MAEvBmO,CAAC,EAAE,WAFoB;MAGvB+6B,IAAI,EAAE,IAAIxwC,MAAJ,CAAW,mBAAX,CAHiB;MAIvBywC,yBAAyB,EAAE,IAAIzwC,MAAJ,CAAW,mBAAX,CAJJ;MAKvB0wC,iBAAiB,EAAEJ;KALL,CAAlB;IAOAC,SAAS,CAAC1wC,GAAV;SAEK2H,KAAL,CAAWzF,IAAX,CAAgB4uC,aAAhB,GAAgC,CAACJ,SAAD,CAAhC;GAvCO;EA0CXK,UA1CW,wBA0CE;2IAGU,KAAK7e,MAFxB,6DAG0B,KAAK8d,kBAH/B;GA3CO;EAmDXE,gBAnDW,8BAmDQ;SACVc,SAAL,CAAe,KAAKD,UAAL,EAAf;;CApDR;;ACDA,YAAe;EAEXE,SAFW,uBAEC;SACH/e,MAAL,GAAc,CAAd;GAHO;EAMX+d,SANW,uBAMC;SACHiB,iBAAL;GAPO;EAUXA,iBAVW,+BAUS;SACXF,SAAL,CAAe,KAAKG,WAAL,EAAf;GAXO;EAcXA,WAdW,yBAcG;8IAGU,KAAKjf,MAFzB;;CAfR;;ACEA,kBAAe;EACXkf,aADW,yBACGlf,MADH,EACW;IAClB5zB,MAAM,CAACu/B,MAAP,CAAc,IAAd,EAAoB3L,MAApB;GAFO;EAKXmf,UALW,sBAKArzC,OALA,EAKS;YAERA,OAAO,CAACk0B,MAAhB;WACS,SAAL;WACK,UAAL;WACK,UAAL;WACK,SAAL;WACK,UAAL;WACK,UAAL;WACK,SAAL;WACK,UAAL;WACK,UAAL;aACSkf,aAAL,CAAmBE,IAAnB;;aACKxB,QAAL,CAAc9xC,OAAO,CAACk0B,MAAtB;;;WAEC,QAAL;aACSkf,aAAL,CAAmBG,KAAnB;;aACKN,SAAL;;;;CAtBhB;;ICFMO;yBACY;;;SACLC,SAAL;;;;;iCAOS;WACJA,SAAL,GAAiB,KAAKA,SAAL,CAAezuC,MAAf,mGAAjB;;;;2BAOG0uC,KAAmB;UAAdC,OAAc,uEAAN,IAAM;WACjBF,SAAL,GAAiB,KAAKA,SAAL,CAAezuC,MAAf,CAAsB0uC,GAAtB,CAAjB;UACIC,OAAJ,EACI,KAAKF,SAAL,GAAiB,KAAKA,SAAL,CAAezuC,MAAf,CAAsB,IAAtB,CAAjB;;;;6BAGC;aAAS,KAAKyuC,SAAZ;;;;gCAEC;aAAS,KAAKA,SAAL,CAAe5yC,MAAtB;;;;0BAER;WACG+yC,UAAL;;WACKH,SAAL,GAAiB,KAAKA,SAAL,CAAe1T,IAAf,EAAjB;;;;;;;AC5BR,oBAAe;EACX8T,YADW,0BACI;SACNC,QAAL,GAAgB,IAAIN,WAAJ,EAAhB;GAFO;EAKXR,SALW,qBAKDU,GALC,EAKkB;QAAdC,OAAc,uEAAN,IAAM;SAAOG,QAAL,CAAcC,MAAd,CAAqBL,GAArB,EAAyBC,OAAzB;GALpB;EAOXK,QAPW,sBAOA;SACFhB,SAAL,8HAEsB,KAAKhlC,IAAL,CAAUE,YAAV,CAAuB+lC,WAAvB,GAAqCxjB,KAArC,CAA2C,GAA3C,EAAgD,CAAhD,IAAmD,GAFzE,6DAGuB,KAAKziB,IAAL,CAAUkmC,OAHjC;;QAQI,KAAKlmC,IAAL,CAAUmmC,KAAV,IAAmB,KAAKnmC,IAAL,CAAUomC,MAA7B,IAAuC,KAAKpmC,IAAL,CAAUqmC,OAArD,EAA8D;WACrDrB,SAAL;;UAII,KAAKhlC,IAAL,CAAUmmC,KAAd,EAAqB;aACZnB,SAAL,+HAGuC,KAAKhlC,IAAL,CAAUmmC,KAHjD;;;UASA,KAAKnmC,IAAL,CAAUomC,MAAd,EAAsB;aACbpB,SAAL,0GAGkB,KAAKhlC,IAAL,CAAUomC,MAH5B;;;UASA,KAAKpmC,IAAL,CAAUqmC,OAAd,EAAuB;aACdrB,SAAL,qIAGuC,KAAKhlC,IAAL,CAAUqmC,OAHjD;;;WASCrB,SAAL;;;SAKCA,SAAL,4HAEoB,KAAKhlC,IAAL,CAAUkmC,OAF9B,sBAEwD,KAFxD;;QAII,KAAKlmC,IAAL,CAAUsmC,QAAd,EAAwB;WACftB,SAAL,uCACgB,KAAKhlC,IAAL,CAAUsmC,QAD1B,sBACqD,KADrD;;;SAICtB,SAAL;GAjEO;EAsEXuB,WAtEW,yBAsEG;SACLP,QAAL;;SAEKF,QAAL,CAAc9xC,GAAd;;;;;;QAMI,KAAKgN,OAAL,IAAgB,GAApB,EAAyB;WAChBwlC,WAAL,GAAmB,KAAKnrC,GAAL,CAAS;QACxBxI,MAAM,EAAE,KAAKizC,QAAL,CAAcW,SAAd,EADgB;QAExBhrC,IAAI,EAAE,UAFkB;QAGxB+N,OAAO,EAAE;OAHM,CAAnB;WAKKg9B,WAAL,CAAiBpwC,QAAjB,GAA4B,KAA5B;WACKowC,WAAL,CAAiB9vC,KAAjB,CAAuBlC,MAAM,CAACC,IAAP,CAAY,KAAKqxC,QAAL,CAAcY,MAAd,EAAZ,EAAoC,OAApC,CAAvB;WACKF,WAAL,CAAiBxyC,GAAjB;WACK2H,KAAL,CAAWzF,IAAX,CAAgBywC,QAAhB,GAA2B,KAAKH,WAAhC;;;CAxFZ;;ICwBMI;;;;;yBACsB;;;QAAd50C,OAAc,uEAAJ,EAAI;;;;8BAClBA,OAAN;UACKA,OAAL,GAAeA,OAAf,CAFwB;;YAKhBA,OAAO,CAAC+O,UAAhB;WACO,KAAL;cACOC,OAAL,GAAe,GAAf;;;WAEG,KAAL;cACOA,OAAL,GAAe,GAAf;;;WAEG,KAAL;cACOA,OAAL,GAAe,GAAf;;;WAEG,KAAL;WACK,SAAL;cACOA,OAAL,GAAe,GAAf;;;;cAGKA,OAAL,GAAe,GAAf;;KApBoB;;;UAyBnB5K,QAAL,GACE,MAAKpE,OAAL,CAAaoE,QAAb,IAAyB,IAAzB,GAAgC,MAAKpE,OAAL,CAAaoE,QAA7C,GAAwD,IAD1D;UAGKywC,WAAL,GAAmB,EAAnB;UACKC,gBAAL,GAAwB,CAAxB,CA7BwB;;UAgCnBC,QAAL,GAAgB,EAAhB;UACKC,QAAL,GAAgB,CAAhB;UACKpM,MAAL,GAAc,KAAd;UACK/jC,OAAL,GAAe,CAAf;;QACM+E,KAAK,GAAG,MAAKP,GAAL,CAAS;MACrBI,IAAI,EAAE,OADe;MAErBg+B,KAAK,EAAE,CAFc;MAGrBvB,IAAI,EAAE;KAHM,CAAd;;QAMM+O,KAAK,GAAG,MAAK5rC,GAAL,CAAS;MACrB6rC,KAAK,EAAE,IAAIzqC,WAAJ;KADK,CAAd;;UAIKd,KAAL,GAAa,MAAKN,GAAL,CAAS;MACpBI,IAAI,EAAE,SADc;MAEpBG,KAAK,EAALA,KAFoB;MAGpBqrC,KAAK,EAALA;KAHW,CAAb;;QAMI,MAAKj1C,OAAL,CAAa+oC,IAAjB,EAAuB;YAChBp/B,KAAL,CAAWzF,IAAX,CAAgB8kC,IAAhB,GAAuB,IAAI7mC,MAAJ,CAAW,MAAKnC,OAAL,CAAa+oC,IAAxB,CAAvB;KArDsB;;;UAyDnBzxB,IAAL,GAAY,IAAZ,CAzDwB;;UA4DnBu8B,YAAL;;UACKx4B,SAAL;;UACKmQ,UAAL;;UACK4N,SAAL,CAAep5B,OAAO,CAACizB,IAAvB;;UACK8J,QAAL;;UACKoH,UAAL;;UACK6D,WAAL;;UACKwC,YAAL,CAAkBxqC,OAAlB;;UACKqzC,UAAL,CAAgBrzC,OAAhB,EApEwB;;;UAuEnBgO,IAAL,GAAY;MACVmnC,QAAQ,EAAE,QADA;MAEVjB,OAAO,EAAE,QAFC;MAGVhmC,YAAY,EAAE,IAAInL,IAAJ;KAHhB;;QAMI,MAAK/C,OAAL,CAAagO,IAAjB,EAAuB;WAChB,IAAI7N,GAAT,IAAgB,MAAKH,OAAL,CAAagO,IAA7B,EAAmC;YAC3B5N,GAAG,GAAG,MAAKJ,OAAL,CAAagO,IAAb,CAAkB7N,GAAlB,CAAZ;cACK6N,IAAL,CAAU7N,GAAV,IAAiBC,GAAjB;;;;QAIA,MAAKJ,OAAL,CAAao1C,YAAjB,EAA+B;YACxBzrC,KAAL,CAAWzF,IAAX,CAAgBmxC,iBAAhB,GAAoC,MAAKhsC,GAAL,CAAS;QAC3CisC,eAAe,EAAE;OADiB,CAApC;KArFsB;;;UA2FnBrlC,GAAL,GAAWlC,WAAW,CAACwnC,cAAZ,CAA2B,MAAKvnC,IAAhC,CAAX,CA3FwB;;UA8FnBlJ,SAAL,GAAiBiJ,WAAW,CAACwD,MAAZ,gCAAyBvR,OAAzB,CAAjB,CA9FwB;;;UAkGnBmF,MAAL,gBAAoB,MAAK6J,OAAzB,GAlGwB;;;UAqGnB7J,MAAL,CAAY,mBAAZ,EArGwB;;;QAwGpB,MAAKnF,OAAL,CAAaw1C,aAAb,KAA+B,KAAnC,EAA0C;YACnCC,OAAL;;;;;;;;4BAIIz1C,SAAS;UACXA,OAAO,IAAI,IAAf,EAAqB;QAChBA,OADgB,GACJ,IADI,CAChBA,OADgB;OADN;;;UAMX,CAAC,KAAKA,OAAL,CAAa01C,WAAlB,EAA+B;aACxBC,UAAL;OAPa;;;WAWVr+B,IAAL,GAAY,IAAI3O,OAAJ,CAAY,IAAZ,EAAkB3I,OAAlB,CAAZ;;WACK60C,WAAL,CAAiB7zC,IAAjB,CAAsB,KAAKsW,IAA3B,EAZe;;;UAeT2uB,KAAK,GAAG,KAAKt8B,KAAL,CAAWzF,IAAX,CAAgB0F,KAAhB,CAAsB1F,IAApC;MACA+hC,KAAK,CAACC,IAAN,CAAWllC,IAAX,CAAgB,KAAKsW,IAAL,CAAU9N,UAA1B;MACAy8B,KAAK,CAACwB,KAAN,GAjBe;;WAoBVl7B,CAAL,GAAS,KAAK+K,IAAL,CAAUvO,OAAV,CAAkBxD,IAA3B;WACK+iB,CAAL,GAAS,KAAKhR,IAAL,CAAUvO,OAAV,CAAkBzD,GAA3B,CArBe;;;WAyBVoT,IAAL,GAAY,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAAZ;WACKxD,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAC,CAAzB,EAA4B,CAA5B,EAA+B,KAAKoC,IAAL,CAAUnO,MAAzC;WAEKszB,IAAL,CAAU,WAAV;aAEO,IAAP;;;;sCAGgBz8B,SAAS;UACnBurC,YAAY,GAAG,KAAKE,eAAL,CAAqB,KAAKn0B,IAA1B,CAArB;WAEKm+B,OAAL,CAAaz1C,OAAb;WAEKsrC,gBAAL,CAAsBC,YAAtB;aAEO,IAAP;;;;wCAGkB;aACX;QAAE3S,KAAK,EAAE,KAAKkc,gBAAd;QAAgCc,KAAK,EAAE,KAAKf,WAAL,CAAiBh0C;OAA/D;;;;iCAGW+C,GAAG;UACV0T,IAAJ;;UACI,EAAEA,IAAI,GAAG,KAAKu9B,WAAL,CAAiBjxC,CAAC,GAAG,KAAKkxC,gBAA1B,CAAT,CAAJ,EAA2D;cACnD,IAAIh1C,KAAJ,wBACY8D,CADZ,0DAEF,KAAKkxC,gBAFH,iBAGG,KAAKA,gBAAL,GAAwB,KAAKD,WAAL,CAAiBh0C,MAAzC,GAAkD,CAHrD,EAAN;;;aAOM,KAAKyW,IAAL,GAAYA,IAApB;;;;iCAGW;;;UAGL2uB,KAAK,GAAG,KAAK4O,WAAnB;WACKA,WAAL,GAAmB,EAAnB;WACKC,gBAAL,IAAyB7O,KAAK,CAACplC,MAA/B;;iDACiBolC,KANN;;;;4DAMa;cAAf3uB,IAAe;eACjBm0B,eAAL,CAAqBn0B,IAArB;UACAA,IAAI,CAACtV,GAAL;;;;;;;;;;wCAIgBua,MAAe;wCAANkL,IAAM;QAANA,IAAM;;;UAC7BA,IAAI,CAAC5mB,MAAL,KAAgB,CAApB,EAAuB;QACrB4mB,IAAI,GAAG,CAAC,KAAD,EAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB,CAAP;;;UAEEA,IAAI,CAAC,CAAD,CAAJ,KAAY,KAAZ,IAAqBA,IAAI,CAAC,CAAD,CAAJ,KAAY,IAArC,EAA2C;QACzCA,IAAI,CAAC,CAAD,CAAJ,GAAU,KAAKnQ,IAAL,CAAUnO,MAAV,GAAmBse,IAAI,CAAC,CAAD,CAAjC;;;MAEFA,IAAI,CAACouB,OAAL,CAAa,KAAKv+B,IAAL,CAAU9N,UAAvB;;WACKG,KAAL,CAAWzF,IAAX,CAAgB+wC,KAAhB,CAAsB/wC,IAAtB,CAA2BgxC,KAA3B,CAAiCvX,GAAjC,CAAqCphB,IAArC,EAA2CkL,IAA3C;;;;yCAGmBlL,MAAMlT,KAAK;UAC1B,CAAC,KAAKM,KAAL,CAAWzF,IAAX,CAAgB+wC,KAAhB,CAAsB/wC,IAAtB,CAA2B4xC,aAAhC,EAA+C;;aAExCnsC,KAAL,CAAWzF,IAAX,CAAgB+wC,KAAhB,CAAsB/wC,IAAtB,CAA2B4xC,aAA3B,GAA2C,IAAIrrC,WAAJ,CAAgB;UAAEvK,MAAM,EAAE;SAA1B,CAA3C;OAH4B;;;WAOzByJ,KAAL,CAAWzF,IAAX,CAAgB+wC,KAAhB,CAAsB/wC,IAAtB,CAA2B4xC,aAA3B,CAAyCnY,GAAzC,CAA6CphB,IAA7C,EAAmDlT,GAAnD;;;;uCAGiBkT,MAAMw5B,IAAI;UACvB,CAAC,KAAKpsC,KAAL,CAAWzF,IAAX,CAAgB+wC,KAAhB,CAAsB/wC,IAAtB,CAA2B8xC,UAAhC,EAA4C;aACrCrsC,KAAL,CAAWzF,IAAX,CAAgB+wC,KAAhB,CAAsB/wC,IAAtB,CAA2B8xC,UAA3B,GAAwC,IAAIvrC,WAAJ,EAAxC;;;UAEEvG,IAAI,GAAG;QACT8rC,EAAE,EAAE,IAAI7tC,MAAJ,CAAW4zC,EAAX,CADK;QAETn+B,CAAC,EAAE;OAFL;;WAIKjO,KAAL,CAAWzF,IAAX,CAAgB+wC,KAAhB,CAAsB/wC,IAAtB,CAA2B8xC,UAA3B,CAAsCrY,GAAtC,CAA0CphB,IAA1C,EAAgDrY,IAAhD;;;;wBAGEA,MAAM;UACFmF,GAAG,GAAG,IAAItF,YAAJ,CAAiB,IAAjB,EAAuB,KAAKgxC,QAAL,CAAcl0C,MAAd,GAAuB,CAA9C,EAAiDqD,IAAjD,CAAZ;;WACK6wC,QAAL,CAAc/zC,IAAd,CAAmB,IAAnB,EAFQ;;;WAGHg0C,QAAL;aACO3rC,GAAP;;;;4BAGM;;;;2BAGDnF,MAAM;UACP,CAAC1B,MAAM,CAACM,QAAP,CAAgBoB,IAAhB,CAAL,EAA4B;QAC1BA,IAAI,GAAG1B,MAAM,CAACC,IAAP,CAAYyB,IAAI,GAAG,IAAnB,EAAyB,QAAzB,CAAP;;;WAGGlD,IAAL,CAAUkD,IAAV;aACQ,KAAKW,OAAL,IAAgBX,IAAI,CAACrD,MAA7B;;;;+BAGSqD,MAAM;WACVoT,IAAL,CAAU5S,KAAV,CAAgBR,IAAhB;aACO,IAAP;;;;4BAGMmF,KAAK;WACN0rC,QAAL,CAAc1rC,GAAG,CAACpF,EAAJ,GAAS,CAAvB,IAA4BoF,GAAG,CAACzE,MAAhC;;UACI,EAAE,KAAKowC,QAAP,KAAoB,CAApB,IAAyB,KAAKpM,MAAlC,EAA0C;aACnCqN,SAAL;;eACQ,KAAKrN,MAAL,GAAc,KAAtB;;;;;0BAIEjY,UAAU3a,IAAI;;UAEZkgC,GAAG,GAAG,IAAIp2C,KAAJ,oIAAZ;MAKAq2C,OAAO,CAACC,IAAR,CAAaF,GAAG,CAACG,KAAjB;WAEKC,IAAL,CAAU1lB,EAAE,CAAC2lB,iBAAH,CAAqB5lB,QAArB,CAAV;WACK3uB,GAAL;aACO,KAAK+4B,IAAL,CAAU,KAAV,EAAiB/kB,EAAjB,CAAP;;;;0BAGI;WACC2/B,UAAL;WAEKa,KAAL,GAAa,KAAKntC,GAAL,EAAb;;WACK,IAAIlJ,GAAT,IAAgB,KAAK6N,IAArB,EAA2B;YACrB5N,GAAG,GAAG,KAAK4N,IAAL,CAAU7N,GAAV,CAAV;;YACI,OAAOC,GAAP,KAAe,QAAnB,EAA6B;UAC3BA,GAAG,GAAG,IAAI+B,MAAJ,CAAW/B,GAAX,CAAN;;;YAGEq2C,KAAK,GAAG,KAAKptC,GAAL,CAASjJ,GAAT,CAAZ;QACAq2C,KAAK,CAACz0C,GAAN;aAEKw0C,KAAL,CAAWtyC,IAAX,CAAgB/D,GAAhB,IAAuBs2C,KAAvB;;;WAGGD,KAAL,CAAWx0C,GAAX;;WAEK,IAAIua,IAAT,IAAiB,KAAK+c,aAAtB,EAAqC;YAC7BrG,IAAI,GAAG,KAAKqG,aAAL,CAAmB/c,IAAnB,CAAb;QACA0W,IAAI,CAACtuB,QAAL;;;WAGGojC,UAAL;WACK+D,WAAL;;UAEI,KAAK5X,MAAT,EAAiB;aACV+d,SAAL;;;WAGGsC,WAAL;;WAEK5qC,KAAL,CAAW3H,GAAX;;WACK2H,KAAL,CAAWzF,IAAX,CAAgB0F,KAAhB,CAAsB5H,GAAtB;;WACK2H,KAAL,CAAWzF,IAAX,CAAgB+wC,KAAhB,CAAsBjzC,GAAtB;;WACKisC,WAAL;;UAEI,KAAKtkC,KAAL,CAAWzF,IAAX,CAAgBmxC,iBAApB,EAAuC;aAChC1rC,KAAL,CAAWzF,IAAX,CAAgBmxC,iBAAhB,CAAkCrzC,GAAlC;;;UAGE,KAAK8C,SAAT,EAAoB;aACbA,SAAL,CAAe9C,GAAf;;;UAGE,KAAKgzC,QAAL,KAAkB,CAAtB,EAAyB;eAChB,KAAKiB,SAAL,EAAP;OADF,MAEO;eACG,KAAKrN,MAAL,GAAc,IAAtB;;;;;gCAIQ;;UAEJ8N,UAAU,GAAG,KAAK7xC,OAAxB;;WACKM,MAAL,CAAY,MAAZ;;WACKA,MAAL,aAAiB,KAAK4vC,QAAL,CAAcl0C,MAAd,GAAuB,CAAxC;;WACKsE,MAAL,CAAY,qBAAZ;;kDAEmB,KAAK4vC,QAPd;;;;+DAOwB;cAAzBnwC,MAAyB;UAChCA,MAAM,GAAG,oBAAaA,MAAb,EAAsBnD,KAAtB,CAA4B,CAAC,EAA7B,CAAT;;eACK0D,MAAL,CAAYP,MAAM,GAAG,WAArB;SATQ;;;;;;;;UAaJ+xC,OAAO,GAAG;QACdtF,IAAI,EAAE,KAAK0D,QAAL,CAAcl0C,MAAd,GAAuB,CADf;QAEd+1C,IAAI,EAAE,KAAKjtC,KAFG;QAGdgpC,IAAI,EAAE,KAAK6D,KAHG;QAIdK,EAAE,EAAE,CAAC,KAAK5mC,GAAN,EAAW,KAAKA,GAAhB;OAJN;;UAMI,KAAKnL,SAAT,EAAoB;QAClB6xC,OAAO,CAACG,OAAR,GAAkB,KAAKhyC,SAAL,CAAe0E,UAAjC;;;WAGGrE,MAAL,CAAY,SAAZ;;WACKA,MAAL,CAAYlE,SAAS,CAACC,OAAV,CAAkBy1C,OAAlB,CAAZ;;WAEKxxC,MAAL,CAAY,WAAZ;;WACKA,MAAL,WAAeuxC,UAAf;;WACKvxC,MAAL,CAAY,OAAZ,EA5BU;;;aA+BH,KAAKnE,IAAL,CAAU,IAAV,CAAP;;;;+BAGS;aACF,sBAAP;;;;;EA3VsBqZ,MAAM,CAAC08B;;AA+VjC,IAAMC,KAAK,GAAG,SAARA,KAAQ,CAAAC,OAAO,EAAI;EACvB32C,MAAM,CAACu/B,MAAP,CAAc+U,WAAW,CAACsC,SAA1B,EAAqCD,OAArC;CADF;;AAIAD,KAAK,CAACG,aAAD,CAAL;AACAH,KAAK,CAACI,UAAD,CAAL;AACAJ,KAAK,CAACK,WAAD,CAAL;AACAL,KAAK,CAACM,UAAD,CAAL;AACAN,KAAK,CAACO,SAAD,CAAL;AACAP,KAAK,CAACQ,WAAD,CAAL;AACAR,KAAK,CAACS,gBAAD,CAAL;AACAT,KAAK,CAACU,YAAD,CAAL;AACAV,KAAK,CAACW,aAAD,CAAL;AACAX,KAAK,CAACY,aAAD,CAAL;AACAZ,KAAK,CAACa,gBAAD,CAAL;AACAb,KAAK,CAACc,WAAD,CAAL;AAEAlD,WAAW,CAACza,WAAZ,GAA0BA,WAA1B;;;;"}