import { prisma } from '@/lib/prisma';

export interface ActivityLogEntry {
  id?: string;
  userId: string;
  action: string;
  entityType: string;
  entityId: string;
  details?: any;
  timestamp?: Date;
}

export class ActivityLogger {
  /**
   * Log an activity entry
   */
  static async log(entry: Omit<ActivityLogEntry, 'id' | 'timestamp'>): Promise<void> {
    try {
      await prisma.activity_logs.create({
        data: {
          userId: entry.userId,
          action: entry.action,
          entityType: entry.entityType,
          entityId: entry.entityId,
          details: entry.details ? JSON.stringify(entry.details) : undefined,
          timestamp: new Date(),
        },
      });
    } catch (error) {
      console.error('Failed to log activity:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Log multiple activity entries
   */
  static async logBatch(entries: Omit<ActivityLogEntry, 'id' | 'timestamp'>[]): Promise<void> {
    try {
      await prisma.activity_logs.createMany({
        data: entries.map(entry => ({
          userId: entry.userId,
          action: entry.action,
          entityType: entry.entityType,
          entityId: entry.entityId,
          details: entry.details ? JSON.stringify(entry.details) : undefined,
          timestamp: new Date(),
        })),
      });
    } catch (error) {
      console.error('Failed to log batch activities:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Get activity logs for a specific entity
   */
  static async getEntityLogs(entityType: string, entityId: string): Promise<ActivityLogEntry[]> {
    try {
      const logs = await prisma.activity_logs.findMany({
        where: {
          entityType,
          entityId,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return logs.map(log => ({
        id: log.id,
        userId: log.userId || '',
        action: log.action,
        entityType: log.entityType || '',
        entityId: log.entityId || '',
        details: log.details ? JSON.parse(log.details as string) : null,
        timestamp: log.createdAt,
      }));
    } catch (error) {
      console.error('Failed to get entity logs:', error);
      return [];
    }
  }

  /**
   * Get activity logs for a specific user
   */
  static async getUserLogs(userId: string, limit: number = 50): Promise<ActivityLogEntry[]> {
    try {
      const logs = await prisma.activity_logs.findMany({
        where: {
          userId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
      });

      return logs.map(log => ({
        id: log.id,
        userId: log.userId || '',
        action: log.action,
        entityType: log.entityType || '',
        entityId: log.entityId || '',
        details: log.details ? JSON.parse(log.details as string) : null,
        timestamp: log.createdAt,
      }));
    } catch (error) {
      console.error('Failed to get user logs:', error);
      return [];
    }
  }
}
