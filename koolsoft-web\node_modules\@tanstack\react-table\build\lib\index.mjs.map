{"version": 3, "file": "index.mjs", "sources": ["../../src/index.tsx"], "sourcesContent": ["import * as React from 'react'\nexport * from '@tanstack/table-core'\n\nimport {\n  TableOptions,\n  TableOptionsResolved,\n  RowData,\n  createTable,\n} from '@tanstack/table-core'\n\nexport type Renderable<TProps> = React.ReactNode | React.ComponentType<TProps>\n\n//\n\n/**\n * If rendering headers, cells, or footers with custom markup, use flexRender instead of `cell.getValue()` or `cell.renderValue()`.\n */\nexport function flexRender<TProps extends object>(\n  Comp: Renderable<TProps>,\n  props: TProps\n): React.ReactNode | React.JSX.Element {\n  return !Comp ? null : isReactComponent<TProps>(Comp) ? (\n    <Comp {...props} />\n  ) : (\n    Comp\n  )\n}\n\nfunction isReactComponent<TProps>(\n  component: unknown\n): component is React.ComponentType<TProps> {\n  return (\n    isClassComponent(component) ||\n    typeof component === 'function' ||\n    isExoticComponent(component)\n  )\n}\n\nfunction isClassComponent(component: any) {\n  return (\n    typeof component === 'function' &&\n    (() => {\n      const proto = Object.getPrototypeOf(component)\n      return proto.prototype && proto.prototype.isReactComponent\n    })()\n  )\n}\n\nfunction isExoticComponent(component: any) {\n  return (\n    typeof component === 'object' &&\n    typeof component.$$typeof === 'symbol' &&\n    ['react.memo', 'react.forward_ref'].includes(component.$$typeof.description)\n  )\n}\n\nexport function useReactTable<TData extends RowData>(\n  options: TableOptions<TData>\n) {\n  // Compose in the generic options to the user options\n  const resolvedOptions: TableOptionsResolved<TData> = {\n    state: {}, // Dummy state\n    onStateChange: () => {}, // noop\n    renderFallbackValue: null,\n    ...options,\n  }\n\n  // Create a new table and store it in state\n  const [tableRef] = React.useState(() => ({\n    current: createTable<TData>(resolvedOptions),\n  }))\n\n  // By default, manage table state here using the table's initial state\n  const [state, setState] = React.useState(() => tableRef.current.initialState)\n\n  // Compose the default state above with any user state. This will allow the user\n  // to only control a subset of the state if desired.\n  tableRef.current.setOptions(prev => ({\n    ...prev,\n    ...options,\n    state: {\n      ...state,\n      ...options.state,\n    },\n    // Similarly, we'll maintain both our internal state and any user-provided\n    // state.\n    onStateChange: updater => {\n      setState(updater)\n      options.onStateChange?.(updater)\n    },\n  }))\n\n  return tableRef.current\n}\n"], "names": ["flexRender", "Comp", "props", "isReactComponent", "React", "createElement", "component", "isClassComponent", "isExoticComponent", "proto", "Object", "getPrototypeOf", "prototype", "$$typeof", "includes", "description", "useReactTable", "options", "resolvedOptions", "state", "onStateChange", "renderFallbackValue", "tableRef", "useState", "current", "createTable", "setState", "initialState", "setOptions", "prev", "updater"], "mappings": ";;;;;;;;;;;;;;AAYA;;AAEA;AACA;AACA;AACO,SAASA,UAAUA,CACxBC,IAAwB,EACxBC,KAAa,EACwB;AACrC,EAAA,OAAO,CAACD,IAAI,GAAG,IAAI,GAAGE,gBAAgB,CAASF,IAAI,CAAC,gBAClDG,KAAA,CAAAC,aAAA,CAACJ,IAAI,EAAKC,KAAQ,CAAC,GAEnBD,IACD,CAAA;AACH,CAAA;AAEA,SAASE,gBAAgBA,CACvBG,SAAkB,EACwB;AAC1C,EAAA,OACEC,gBAAgB,CAACD,SAAS,CAAC,IAC3B,OAAOA,SAAS,KAAK,UAAU,IAC/BE,iBAAiB,CAACF,SAAS,CAAC,CAAA;AAEhC,CAAA;AAEA,SAASC,gBAAgBA,CAACD,SAAc,EAAE;AACxC,EAAA,OACE,OAAOA,SAAS,KAAK,UAAU,IAC/B,CAAC,MAAM;AACL,IAAA,MAAMG,KAAK,GAAGC,MAAM,CAACC,cAAc,CAACL,SAAS,CAAC,CAAA;IAC9C,OAAOG,KAAK,CAACG,SAAS,IAAIH,KAAK,CAACG,SAAS,CAACT,gBAAgB,CAAA;AAC5D,GAAC,GAAG,CAAA;AAER,CAAA;AAEA,SAASK,iBAAiBA,CAACF,SAAc,EAAE;EACzC,OACE,OAAOA,SAAS,KAAK,QAAQ,IAC7B,OAAOA,SAAS,CAACO,QAAQ,KAAK,QAAQ,IACtC,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAACC,QAAQ,CAACR,SAAS,CAACO,QAAQ,CAACE,WAAW,CAAC,CAAA;AAEhF,CAAA;AAEO,SAASC,aAAaA,CAC3BC,OAA4B,EAC5B;AACA;AACA,EAAA,MAAMC,eAA4C,GAAG;IACnDC,KAAK,EAAE,EAAE;AAAE;AACXC,IAAAA,aAAa,EAAEA,MAAM,EAAE;AAAE;AACzBC,IAAAA,mBAAmB,EAAE,IAAI;IACzB,GAAGJ,OAAAA;GACJ,CAAA;;AAED;EACA,MAAM,CAACK,QAAQ,CAAC,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,OAAO;IACvCC,OAAO,EAAEC,WAAW,CAAQP,eAAe,CAAA;AAC7C,GAAC,CAAC,CAAC,CAAA;;AAEH;AACA,EAAA,MAAM,CAACC,KAAK,EAAEO,QAAQ,CAAC,GAAGtB,KAAK,CAACmB,QAAQ,CAAC,MAAMD,QAAQ,CAACE,OAAO,CAACG,YAAY,CAAC,CAAA;;AAE7E;AACA;AACAL,EAAAA,QAAQ,CAACE,OAAO,CAACI,UAAU,CAACC,IAAI,KAAK;AACnC,IAAA,GAAGA,IAAI;AACP,IAAA,GAAGZ,OAAO;AACVE,IAAAA,KAAK,EAAE;AACL,MAAA,GAAGA,KAAK;AACR,MAAA,GAAGF,OAAO,CAACE,KAAAA;KACZ;AACD;AACA;IACAC,aAAa,EAAEU,OAAO,IAAI;MACxBJ,QAAQ,CAACI,OAAO,CAAC,CAAA;MACjBb,OAAO,CAACG,aAAa,IAArBH,IAAAA,IAAAA,OAAO,CAACG,aAAa,CAAGU,OAAO,CAAC,CAAA;AAClC,KAAA;AACF,GAAC,CAAC,CAAC,CAAA;EAEH,OAAOR,QAAQ,CAACE,OAAO,CAAA;AACzB;;;;"}