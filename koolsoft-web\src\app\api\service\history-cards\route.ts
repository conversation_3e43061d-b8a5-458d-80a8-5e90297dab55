import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getHistoryCardRepository } from '@/lib/repositories';

/**
 * GET /api/service/history-cards
 * Get service-related history cards with optional filtering
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const customerId = searchParams.get('customerId');
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '10');

      const historyCardRepository = getHistoryCardRepository();

      // Calculate pagination
      const skip = (page - 1) * limit;
      const take = limit;

      let historyCards = [];
      let total = 0;

      if (customerId) {
        // Get service history cards for a specific customer
        historyCards = await historyCardRepository.findByCustomerId(customerId, skip, take);
        total = await historyCardRepository.count({ 
          customerId,
          source: 'SERVICE' 
        });
      } else {
        // Get all service history cards
        historyCards = await historyCardRepository.findWithFilter(
          { source: 'SERVICE' },
          skip,
          take,
          { createdAt: 'desc' }
        );
        total = await historyCardRepository.count({ source: 'SERVICE' });
      }

      // Filter to only include service-related history cards
      const serviceHistoryCards = historyCards.filter(card => 
        card.source === 'SERVICE' || 
        card.repairs?.length > 0 || 
        card.maintenance?.length > 0 ||
        card.complaints?.some((complaint: any) => 
          ['REPAIR', 'MAINTENANCE', 'INSTALLATION', 'INSPECTION'].includes(complaint.complaintType)
        )
      );

      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      return NextResponse.json({
        historyCards: serviceHistoryCards,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNextPage,
          hasPreviousPage,
        },
      });
    } catch (error) {
      console.error('Error fetching service history cards:', error);
      return NextResponse.json(
        { error: 'Failed to fetch service history cards' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/service/history-cards
 * Create a new service history card
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();
      const { customerId, serviceReportId, notes } = body;

      if (!customerId) {
        return NextResponse.json(
          { error: 'Customer ID is required' },
          { status: 400 }
        );
      }

      const historyCardRepository = getHistoryCardRepository();

      // Check if a service history card already exists for this customer
      let historyCard = await historyCardRepository.findFirst({
        where: {
          customerId,
          source: 'SERVICE',
        },
      });

      if (!historyCard) {
        // Get the next card number
        const lastCard = await historyCardRepository.findFirst({
          orderBy: { cardNo: 'desc' },
        });
        const nextCardNo = lastCard ? (lastCard.cardNo || 0) + 1 : 1;

        // Create new history card
        historyCard = await historyCardRepository.create({
          customer: { connect: { id: customerId } },
          cardNo: nextCardNo,
          source: 'SERVICE',
        });
      }

      // If serviceReportId is provided, create a history section
      if (serviceReportId && notes) {
        // This would require a history sections repository
        // For now, we'll just return the history card
        console.log(`Creating history section for service report: ${serviceReportId}`);
      }

      // Get the history card with all related data
      const historyCardWithRelations = await historyCardRepository.findWithRelations(historyCard.id);

      return NextResponse.json(
        { 
          message: 'Service history card created successfully',
          historyCard: historyCardWithRelations 
        },
        { status: 201 }
      );
    } catch (error) {
      console.error('Error creating service history card:', error);
      return NextResponse.json(
        { error: 'Failed to create service history card' },
        { status: 500 }
      );
    }
  }
);
